// 消息类型
export interface Message {
  role: 'user' | 'assistant'
  content: string
  message_id?: string
  timestamp?: number
  kg_data?: KnowledgeGraphData
  thinking_content?: string
}

// 知识图谱数据类型
export interface KnowledgeGraphData {
  nodes: Array<{
    id: string
    label: string
    type?: string
    properties?: Record<string, any>
  }>
  links: Array<{
    source: string
    target: string
    label: string
    weight?: number
  }>
}

// 聊天响应类型
export interface ChatResponse {
  answer: string
  message_id: string
  execution_trace?: any
  kg_data?: KnowledgeGraphData
  thinking_content?: string
}

// 智能体类型
export interface AgentType {
  id: string
  name: string
  description: string
}

// 性能指标类型
export interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: number
  messageLength?: number
}
