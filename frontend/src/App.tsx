import React from 'react'
import { useStore } from './store/useStore'
import Sidebar from './components/Sidebar'
import ChatArea from './components/ChatArea'
import DebugPanel from './components/DebugPanel'
import { cn } from './lib/utils'

function App() {
  const { debugMode, sidebarCollapsed } = useStore()

  return (
    <div className="flex h-screen bg-gradient-to-br from-blue-50 to-indigo-100 overflow-hidden">
      {/* 侧边栏 */}
      <Sidebar />

      {/* 主内容区域 */}
      <div className={cn(
        "flex-1 flex transition-all duration-300 min-w-0",
        sidebarCollapsed ? "ml-16" : "ml-80",
        "lg:ml-80 md:ml-16 sm:ml-0" // 响应式边距
      )}>
        {/* 聊天区域 */}
        <div className={cn(
          "flex-1 flex flex-col min-w-0",
          debugMode ? "lg:mr-96 md:mr-80 sm:mr-0" : ""
        )}>
          <ChatArea />
        </div>

        {/* 调试面板 */}
        {debugMode && (
          <div className={cn(
            "border-l border-gray-200 bg-white",
            "w-96 lg:w-96 md:w-80 sm:w-full sm:absolute sm:inset-0 sm:z-50"
          )}>
            <DebugPanel />
          </div>
        )}
      </div>
    </div>
  )
}

export default App
