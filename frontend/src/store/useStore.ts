import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Message, KnowledgeGraphData, PerformanceMetric } from '../types'
import { generateId } from '../lib/utils'

export interface AppState {
  // 会话状态
  sessionId: string
  messages: Message[]
  isLoading: boolean
  
  // Agent 配置
  agentType: string
  showThinking: boolean
  useStream: boolean
  
  // UI 状态
  debugMode: boolean
  currentTab: string
  sidebarCollapsed: boolean
  
  // 知识图谱状态
  currentKGData: KnowledgeGraphData | null
  currentKGMessage: number | null
  
  // 性能监控
  performanceMetrics: PerformanceMetric[]
  
  // Actions
  setSessionId: (id: string) => void
  addMessage: (message: Message) => void
  updateMessage: (index: number, updates: Partial<Message>) => void
  clearMessages: () => void
  setLoading: (loading: boolean) => void
  
  setAgentType: (type: string) => void
  setShowThinking: (show: boolean) => void
  setUseStream: (use: boolean) => void
  
  setDebugMode: (debug: boolean) => void
  setCurrentTab: (tab: string) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  
  setCurrentKGData: (data: KnowledgeGraphData | null) => void
  setCurrentKGMessage: (index: number | null) => void
  
  addPerformanceMetric: (metric: PerformanceMetric) => void
  clearPerformanceMetrics: () => void
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      sessionId: generateId(),
      messages: [],
      isLoading: false,
      
      agentType: 'graph_agent',
      showThinking: false,
      useStream: true,
      
      debugMode: false,
      currentTab: '聊天',
      sidebarCollapsed: false,
      
      currentKGData: null,
      currentKGMessage: null,
      
      performanceMetrics: [],
      
      // Actions
      setSessionId: (id) => set({ sessionId: id }),
      
      addMessage: (message) => set((state) => ({
        messages: [...state.messages, {
          ...message,
          timestamp: message.timestamp || Date.now(),
          message_id: message.message_id || generateId(),
        }]
      })),
      
      updateMessage: (index, updates) => set((state) => ({
        messages: state.messages.map((msg, i) => 
          i === index ? { ...msg, ...updates } : msg
        )
      })),
      
      clearMessages: () => set({ 
        messages: [],
        currentKGData: null,
        currentKGMessage: null,
      }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setAgentType: (type) => set({ 
        agentType: type,
        // 切换 agent 时重置相关状态
        showThinking: type === 'deep_research_agent' ? get().showThinking : false,
      }),
      
      setShowThinking: (show) => set({ showThinking: show }),
      
      setUseStream: (use) => set({ useStream: use }),
      
      setDebugMode: (debug) => set({ 
        debugMode: debug,
        // 启用调试模式时禁用流式响应
        useStream: debug ? false : get().useStream,
      }),
      
      setCurrentTab: (tab) => set({ currentTab: tab }),
      
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      
      setCurrentKGData: (data) => set({ currentKGData: data }),
      
      setCurrentKGMessage: (index) => set({ currentKGMessage: index }),
      
      addPerformanceMetric: (metric) => set((state) => ({
        performanceMetrics: [...state.performanceMetrics, metric]
      })),
      
      clearPerformanceMetrics: () => set({ performanceMetrics: [] }),
    }),
    {
      name: 'graph-rag-store',
      partialize: (state) => ({
        sessionId: state.sessionId,
        agentType: state.agentType,
        showThinking: state.showThinking,
        useStream: state.useStream,
        debugMode: state.debugMode,
        sidebarCollapsed: state.sidebarCollapsed,
      }),
    }
  )
)
