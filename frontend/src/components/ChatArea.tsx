import React, { useState, useRef, useEffect } from 'react'
import { Send, Bot, User, Loader2, ThumbsUp, ThumbsDown, Network } from 'lucide-react'
import { useStore } from '../store/useStore'
import { chatAPI, knowledgeGraphAPI } from '../services/api'
import { cn } from '../lib/utils'
import MessageContent from './MessageContent'
import { motion, AnimatePresence } from 'framer-motion'

const ChatArea: React.FC = () => {
  const [input, setInput] = useState('')
  const [streamingContent, setStreamingContent] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  const {
    messages,
    isLoading,
    agentType,
    useStream,
    sessionId,
    addMessage,
    updateMessage,
    setLoading,
    addPerformanceMetric,
    setCurrentKGData,
    setCurrentKGMessage,
  } = useStore()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingContent])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    const userMessage = input.trim()
    setInput('')
    setLoading(true)

    // 添加用户消息
    addMessage({
      role: 'user',
      content: userMessage,
    })

    try {
      const startTime = Date.now()

      if (useStream) {
        // 流式响应
        setStreamingContent('')
        let fullResponse = ''

        const response = await chatAPI.sendMessageStream(
          userMessage,
          agentType,
          sessionId,
          (token) => {
            fullResponse += token
            setStreamingContent(fullResponse)
          }
        )

        // 添加完整的助手消息
        addMessage({
          role: 'assistant',
          content: response || fullResponse,
        })

        setStreamingContent('')
      } else {
        // 非流式响应
        const response = await chatAPI.sendMessage(userMessage, agentType, sessionId)
        
        addMessage({
          role: 'assistant',
          content: response.answer,
          message_id: response.message_id,
          kg_data: response.kg_data,
          thinking_content: response.thinking_content,
        })
      }

      // 记录性能指标
      const duration = Date.now() - startTime
      addPerformanceMetric({
        operation: 'send_message',
        duration,
        timestamp: Date.now(),
        messageLength: userMessage.length,
      })

    } catch (error) {
      console.error('发送消息失败:', error)
      addMessage({
        role: 'assistant',
        content: '抱歉，发生了错误，请稍后重试。',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const handleFeedback = async (messageIndex: number, feedback: 'positive' | 'negative') => {
    const message = messages[messageIndex]
    if (message.message_id) {
      try {
        await chatAPI.sendFeedback(message.message_id, feedback)
        // 可以添加视觉反馈
      } catch (error) {
        console.error('发送反馈失败:', error)
      }
    }
  }

  const handleExtractKG = async (messageIndex: number) => {
    const message = messages[messageIndex]
    const userMessage = messageIndex > 0 ? messages[messageIndex - 1] : null

    if (message.role === 'assistant') {
      try {
        const kgData = await knowledgeGraphAPI.getKnowledgeGraph(
          message.content,
          userMessage?.content
        )

        if (kgData && kgData.nodes && kgData.nodes.length > 0) {
          // 更新消息的知识图谱数据
          updateMessage(messageIndex, { kg_data: kgData })
          // 设置当前知识图谱数据
          setCurrentKGData(kgData)
          setCurrentKGMessage(messageIndex)
        }
      } catch (error) {
        console.error('提取知识图谱失败:', error)
      }
    }
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* 头部 */}
      <div className="flex-shrink-0 p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            GraphRAG 智能对话系统
          </h1>
          <p className="text-gray-600">
            基于知识图谱的智能问答，支持多种检索策略
          </p>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-6 space-y-6">
          {messages.length === 0 && (
            <div className="text-center py-12">
              <Bot className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                开始对话
              </h3>
              <p className="text-gray-500">
                向我提问任何关于文档内容的问题
              </p>
            </div>
          )}

          <AnimatePresence>
            {messages.map((message, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "flex space-x-4",
                  message.role === 'user' ? "justify-end" : "justify-start"
                )}
              >
                {message.role === 'assistant' && (
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <Bot className="w-5 h-5 text-white" />
                    </div>
                  </div>
                )}

                <div className={cn(
                  "max-w-3xl rounded-2xl px-4 py-3",
                  message.role === 'user'
                    ? "bg-blue-600 text-white ml-12"
                    : "bg-gray-100 text-gray-900"
                )}>
                  <MessageContent message={message} />
                  
                  {message.role === 'assistant' && (
                    <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-gray-200">
                      <button
                        onClick={() => handleFeedback(index, 'positive')}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                        title="好评"
                      >
                        <ThumbsUp className="w-4 h-4 text-gray-500" />
                      </button>
                      <button
                        onClick={() => handleFeedback(index, 'negative')}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                        title="差评"
                      >
                        <ThumbsDown className="w-4 h-4 text-gray-500" />
                      </button>
                      {agentType !== 'deep_research_agent' && (
                        <button
                          onClick={() => handleExtractKG(index)}
                          className="p-1 hover:bg-gray-200 rounded transition-colors"
                          title="提取知识图谱"
                        >
                          <Network className="w-4 h-4 text-gray-500" />
                        </button>
                      )}
                    </div>
                  )}
                </div>

                {message.role === 'user' && (
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-gray-600" />
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {/* 流式响应显示 */}
          {streamingContent && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex space-x-4"
            >
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Bot className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="max-w-3xl bg-gray-100 rounded-2xl px-4 py-3">
                <MessageContent message={{ role: 'assistant', content: streamingContent }} />
                <div className="inline-block w-2 h-5 bg-blue-500 animate-pulse ml-1" />
              </div>
            </motion.div>
          )}

          {/* 加载指示器 */}
          {isLoading && !streamingContent && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex space-x-4"
            >
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Loader2 className="w-5 h-5 text-white animate-spin" />
                </div>
              </div>
              <div className="bg-gray-100 rounded-2xl px-4 py-3">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                  <span className="text-gray-500 text-sm">AI 正在思考...</span>
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* 输入区域 */}
      <div className="flex-shrink-0 border-t border-gray-200 bg-white">
        <div className="max-w-4xl mx-auto p-6">
          <form onSubmit={handleSubmit} className="relative">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="输入您的问题..."
              className="w-full resize-none rounded-2xl border border-gray-300 px-4 py-3 pr-12 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-colors"
              rows={1}
              style={{ minHeight: '48px', maxHeight: '120px' }}
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={!input.trim() || isLoading}
              className={cn(
                "absolute right-2 top-2 p-2 rounded-xl transition-all duration-200",
                input.trim() && !isLoading
                  ? "bg-blue-600 text-white hover:bg-blue-700 shadow-lg"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              )}
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default ChatArea
