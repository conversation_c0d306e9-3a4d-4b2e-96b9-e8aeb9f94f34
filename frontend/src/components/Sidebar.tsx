import React from 'react'
import { 
  MessageSquare, 
  Settings, 
  Brain, 
  BarChart3, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Sparkles,
  Zap,
  Search,
  Network,
  Layers
} from 'lucide-react'
import { useStore } from '../store/useStore'
import { chatAPI } from '../services/api'
import { cn } from '../lib/utils'

const agentTypes = [
  {
    id: 'graph_agent',
    name: '图谱智能体',
    description: '使用知识图谱的局部与全局搜索',
    icon: Network,
    color: 'text-blue-500'
  },
  {
    id: 'hybrid_agent',
    name: '混合智能体',
    description: '结合多种搜索策略',
    icon: Layers,
    color: 'text-purple-500'
  },
  {
    id: 'naive_rag_agent',
    name: '基础RAG',
    description: '传统向量检索增强生成',
    icon: Search,
    color: 'text-green-500'
  },
  {
    id: 'deep_research_agent',
    name: '深度研究',
    description: '多轮推理的深度研究智能体',
    icon: Brain,
    color: 'text-orange-500'
  },
  {
    id: 'fusion_agent',
    name: '融合智能体',
    description: '融合图谱和RAG的高级智能体',
    icon: Sparkles,
    color: 'text-pink-500'
  }
]

const Sidebar: React.FC = () => {
  const {
    agentType,
    showThinking,
    useStream,
    debugMode,
    sidebarCollapsed,
    sessionId,
    setAgentType,
    setShowThinking,
    setUseStream,
    setDebugMode,
    setSidebarCollapsed,
    clearMessages,
    clearPerformanceMetrics,
  } = useStore()

  const handleClearChat = async () => {
    try {
      await chatAPI.clearChat(sessionId)
      clearMessages()
      clearPerformanceMetrics()
    } catch (error) {
      console.error('清除聊天历史失败:', error)
    }
  }

  const currentAgent = agentTypes.find(agent => agent.id === agentType)

  return (
    <div className={cn(
      "fixed left-0 top-0 h-full bg-white border-r border-gray-200 shadow-lg transition-all duration-300 z-20",
      sidebarCollapsed ? "w-16" : "w-80",
      // 移动端响应式
      "lg:relative lg:translate-x-0",
      "md:w-16 md:translate-x-0",
      "sm:w-80 sm:-translate-x-full sm:fixed",
      !sidebarCollapsed && "sm:translate-x-0"
    )}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!sidebarCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              GraphRAG
            </h1>
          </div>
        )}
        <button
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-5 h-5" />
          ) : (
            <ChevronLeft className="w-5 h-5" />
          )}
        </button>
      </div>

      {/* 内容区域 */}
      <div className="flex flex-col h-full pb-16">
        {!sidebarCollapsed ? (
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* Agent 选择 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3">智能体选择</h3>
              <div className="space-y-2">
                {agentTypes.map((agent) => {
                  const Icon = agent.icon
                  return (
                    <button
                      key={agent.id}
                      onClick={() => setAgentType(agent.id)}
                      className={cn(
                        "w-full p-3 rounded-lg border text-left transition-all duration-200",
                        agentType === agent.id
                          ? "border-blue-500 bg-blue-50 shadow-sm"
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      )}
                    >
                      <div className="flex items-start space-x-3">
                        <Icon className={cn("w-5 h-5 mt-0.5", agent.color)} />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900">{agent.name}</div>
                          <div className="text-sm text-gray-500 mt-1">{agent.description}</div>
                        </div>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* 深度研究智能体特殊设置 */}
            {agentType === 'deep_research_agent' && (
              <div>
                <h3 className="text-sm font-semibold text-gray-700 mb-3">推理设置</h3>
                <label className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showThinking}
                    onChange={(e) => setShowThinking(e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="font-medium text-gray-900">显示推理过程</div>
                    <div className="text-sm text-gray-500">展示AI的思考步骤</div>
                  </div>
                </label>
              </div>
            )}

            {/* 系统设置 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3">系统设置</h3>
              <div className="space-y-2">
                <label className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={debugMode}
                    onChange={(e) => setDebugMode(e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div>
                    <div className="font-medium text-gray-900">调试模式</div>
                    <div className="text-sm text-gray-500">显示执行轨迹和详细信息</div>
                  </div>
                </label>

                {!debugMode && (
                  <label className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={useStream}
                      onChange={(e) => setUseStream(e.target.checked)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <div>
                      <div className="font-medium text-gray-900">流式响应</div>
                      <div className="text-sm text-gray-500">实时显示AI回答</div>
                    </div>
                  </label>
                )}
              </div>
            </div>

            {/* 示例问题 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3">示例问题</h3>
              <div className="space-y-2">
                {[
                  "华东理工大学的奖学金政策是什么？",
                  "学生违纪处分有哪些规定？",
                  "毕业生就业资助如何申请？"
                ].map((example, index) => (
                  <button
                    key={index}
                    className="w-full p-3 text-left text-sm text-gray-600 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    {example}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          // 折叠状态下的图标菜单
          <div className="flex flex-col items-center py-4 space-y-4">
            {currentAgent && (
              <div className="p-2 rounded-lg bg-blue-50">
                <currentAgent.icon className={cn("w-6 h-6", currentAgent.color)} />
              </div>
            )}
            <button
              onClick={() => setDebugMode(!debugMode)}
              className={cn(
                "p-2 rounded-lg transition-colors",
                debugMode ? "bg-blue-50 text-blue-600" : "hover:bg-gray-100"
              )}
            >
              <BarChart3 className="w-6 h-6" />
            </button>
          </div>
        )}

        {/* 底部操作 */}
        <div className={cn(
          "absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-white",
          sidebarCollapsed ? "px-2" : ""
        )}>
          {!sidebarCollapsed ? (
            <button
              onClick={handleClearChat}
              className="w-full flex items-center justify-center space-x-2 p-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Trash2 className="w-5 h-5" />
              <span>清除对话历史</span>
            </button>
          ) : (
            <button
              onClick={handleClearChat}
              className="w-full p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors flex justify-center"
            >
              <Trash2 className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default Sidebar
