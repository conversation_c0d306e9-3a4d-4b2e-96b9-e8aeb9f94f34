import React from 'react'
import ReactMarkdown from 'react-markdown'
import type { Message } from '../types'
import { cn } from '../lib/utils'

interface MessageContentProps {
  message: Message
}

const MessageContent: React.FC<MessageContentProps> = ({ message }) => {
  if (message.role === 'user') {
    return (
      <div className="whitespace-pre-wrap break-words">
        {message.content}
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {/* 思考过程 */}
      {message.thinking_content && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="text-sm font-medium text-yellow-800 mb-2">
            💭 思考过程
          </div>
          <div className="text-sm text-yellow-700 whitespace-pre-wrap">
            {message.thinking_content}
          </div>
        </div>
      )}

      {/* 主要回答 */}
      <div className="prose prose-sm max-w-none">
        <ReactMarkdown
          components={{
            // 自定义渲染组件
            h1: ({ children }) => (
              <h1 className="text-lg font-bold text-gray-900 mb-2">{children}</h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-base font-semibold text-gray-900 mb-2">{children}</h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-sm font-semibold text-gray-900 mb-1">{children}</h3>
            ),
            p: ({ children }) => (
              <p className="text-gray-700 mb-2 leading-relaxed">{children}</p>
            ),
            ul: ({ children }) => (
              <ul className="list-disc list-inside space-y-1 mb-2">{children}</ul>
            ),
            ol: ({ children }) => (
              <ol className="list-decimal list-inside space-y-1 mb-2">{children}</ol>
            ),
            li: ({ children }) => (
              <li className="text-gray-700">{children}</li>
            ),
            code: ({ children, className }) => {
              const isInline = !className
              return isInline ? (
                <code className="bg-gray-200 px-1 py-0.5 rounded text-sm font-mono">
                  {children}
                </code>
              ) : (
                <pre className="bg-gray-100 p-3 rounded-lg overflow-x-auto">
                  <code className="text-sm font-mono">{children}</code>
                </pre>
              )
            },
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 mb-2">
                {children}
              </blockquote>
            ),
            a: ({ href, children }) => (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                {children}
              </a>
            ),
            table: ({ children }) => (
              <div className="overflow-x-auto mb-2">
                <table className="min-w-full border border-gray-300 rounded-lg">
                  {children}
                </table>
              </div>
            ),
            thead: ({ children }) => (
              <thead className="bg-gray-50">{children}</thead>
            ),
            tbody: ({ children }) => (
              <tbody className="divide-y divide-gray-200">{children}</tbody>
            ),
            tr: ({ children }) => (
              <tr className="hover:bg-gray-50">{children}</tr>
            ),
            th: ({ children }) => (
              <th className="px-3 py-2 text-left text-sm font-semibold text-gray-900 border-b border-gray-300">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="px-3 py-2 text-sm text-gray-700 border-b border-gray-200">
                {children}
              </td>
            ),
          }}
        >
          {message.content}
        </ReactMarkdown>
      </div>

      {/* 知识图谱数据指示器 */}
      {message.kg_data && message.kg_data.nodes && message.kg_data.nodes.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="text-sm font-medium text-blue-800 mb-1">
            🔗 知识图谱数据
          </div>
          <div className="text-sm text-blue-700">
            包含 {message.kg_data.nodes.length} 个实体和 {message.kg_data.links?.length || 0} 个关系
          </div>
        </div>
      )}
    </div>
  )
}

export default MessageContent
