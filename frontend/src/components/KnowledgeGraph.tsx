import React, { useRef, useEffect, useState } from 'react'
import ForceGraph2D from 'react-force-graph-2d'
import type { KnowledgeGraphData } from '../types'
import { useStore } from '../store/useStore'
import { knowledgeGraphAPI } from '../services/api'
import { 
  Search, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Settings,
  Download,
  Maximize2
} from 'lucide-react'
import { cn } from '../lib/utils'

interface KnowledgeGraphProps {
  data?: KnowledgeGraphData
  className?: string
}

interface GraphNode {
  id: string
  label: string
  type?: string
  properties?: Record<string, any>
  x?: number
  y?: number
  vx?: number
  vy?: number
  fx?: number
  fy?: number
}

interface GraphLink {
  source: string | GraphNode
  target: string | GraphNode
  label: string
  weight?: number
}

const KnowledgeGraph: React.FC<KnowledgeGraphProps> = ({ data, className }) => {
  const graphRef = useRef<any>()
  const [graphData, setGraphData] = useState<{ nodes: GraphNode[], links: GraphLink[] }>({ nodes: [], links: [] })
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [graphSettings, setGraphSettings] = useState({
    nodeSize: 8,
    linkWidth: 2,
    chargeStrength: -300,
    linkDistance: 100,
  })

  const { currentKGData, setCurrentKGData } = useStore()

  useEffect(() => {
    const kgData = data || currentKGData
    if (kgData && kgData.nodes && kgData.links) {
      const nodes: GraphNode[] = kgData.nodes.map(node => ({
        ...node,
        id: node.id,
        label: node.label,
      }))

      const links: GraphLink[] = kgData.links.map(link => ({
        ...link,
        source: link.source,
        target: link.target,
      }))

      setGraphData({ nodes, links })
    }
  }, [data, currentKGData])

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node)
    // 可以在这里添加节点点击的其他逻辑
  }

  const handleNodeRightClick = (node: GraphNode, event: MouseEvent) => {
    event.preventDefault()
    // 可以在这里添加右键菜单逻辑
  }

  const handleZoomIn = () => {
    if (graphRef.current) {
      graphRef.current.zoom(graphRef.current.zoom() * 1.2)
    }
  }

  const handleZoomOut = () => {
    if (graphRef.current) {
      graphRef.current.zoom(graphRef.current.zoom() * 0.8)
    }
  }

  const handleReset = () => {
    if (graphRef.current) {
      graphRef.current.zoomToFit(400)
    }
  }

  const handleSearch = () => {
    if (searchTerm && graphRef.current) {
      const node = graphData.nodes.find(n => 
        n.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
      if (node) {
        graphRef.current.centerAt(node.x, node.y, 1000)
        graphRef.current.zoom(2, 1000)
        setSelectedNode(node)
      }
    }
  }

  const getNodeColor = (node: GraphNode) => {
    const colors = {
      person: '#3b82f6',
      organization: '#10b981',
      location: '#f59e0b',
      concept: '#8b5cf6',
      document: '#ef4444',
      default: '#6b7280'
    }
    return colors[node.type as keyof typeof colors] || colors.default
  }

  const filteredNodes = searchTerm 
    ? graphData.nodes.filter(node => 
        node.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : graphData.nodes

  if (!graphData.nodes.length) {
    return (
      <div className={cn("flex items-center justify-center h-64 bg-gray-50 rounded-lg", className)}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-500">暂无知识图谱数据</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("relative bg-white rounded-lg border border-gray-200", className)}>
      {/* 工具栏 */}
      <div className="absolute top-4 left-4 z-10 flex items-center space-x-2">
        <div className="flex items-center bg-white rounded-lg shadow-sm border border-gray-200">
          <input
            type="text"
            placeholder="搜索节点..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            className="px-3 py-2 text-sm border-0 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleSearch}
            className="px-3 py-2 text-gray-500 hover:text-blue-600 border-l border-gray-200"
          >
            <Search className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center bg-white rounded-lg shadow-sm border border-gray-200">
          <button
            onClick={handleZoomIn}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-50"
            title="放大"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          <button
            onClick={handleZoomOut}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-50 border-l border-gray-200"
            title="缩小"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          <button
            onClick={handleReset}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-50 border-l border-gray-200"
            title="重置视图"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-gray-50 border-l border-gray-200"
            title="全屏"
          >
            <Maximize2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 图谱统计 */}
      <div className="absolute top-4 right-4 z-10 bg-white rounded-lg shadow-sm border border-gray-200 p-3">
        <div className="text-sm text-gray-600">
          <div>节点: {graphData.nodes.length}</div>
          <div>边: {graphData.links.length}</div>
        </div>
      </div>

      {/* 图谱可视化 */}
      <div className={cn("w-full", isFullscreen ? "fixed inset-0 z-50 bg-white" : "h-96")}>
        <ForceGraph2D
          ref={graphRef}
          graphData={graphData}
          nodeId="id"
          nodeLabel="label"
          nodeColor={getNodeColor}
          nodeVal={(node: GraphNode) => graphSettings.nodeSize}
          nodeCanvasObject={(node: GraphNode, ctx, globalScale) => {
            const label = node.label
            const fontSize = 12 / globalScale
            ctx.font = `${fontSize}px Sans-Serif`
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            
            // 绘制节点
            ctx.fillStyle = getNodeColor(node)
            ctx.beginPath()
            ctx.arc(node.x!, node.y!, graphSettings.nodeSize, 0, 2 * Math.PI, false)
            ctx.fill()
            
            // 绘制标签
            ctx.fillStyle = selectedNode?.id === node.id ? '#000' : '#333'
            ctx.fillText(label, node.x!, node.y! + graphSettings.nodeSize + fontSize)
          }}
          linkLabel="label"
          linkWidth={(link: GraphLink) => graphSettings.linkWidth}
          linkColor={() => '#999'}
          linkDirectionalArrowLength={6}
          linkDirectionalArrowRelPos={1}
          onNodeClick={handleNodeClick}
          onNodeRightClick={handleNodeRightClick}
          d3AlphaDecay={0.02}
          d3VelocityDecay={0.3}
          cooldownTicks={100}
          enableNodeDrag={true}
          enableZoomInteraction={true}
          enablePanInteraction={true}
        />
      </div>

      {/* 节点详情面板 */}
      {selectedNode && (
        <div className="absolute bottom-4 left-4 z-10 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-900">节点详情</h3>
            <button
              onClick={() => setSelectedNode(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium text-gray-700">标签:</span>
              <span className="ml-2 text-gray-900">{selectedNode.label}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">ID:</span>
              <span className="ml-2 text-gray-900">{selectedNode.id}</span>
            </div>
            {selectedNode.type && (
              <div>
                <span className="font-medium text-gray-700">类型:</span>
                <span className="ml-2 text-gray-900">{selectedNode.type}</span>
              </div>
            )}
            {selectedNode.properties && Object.keys(selectedNode.properties).length > 0 && (
              <div>
                <span className="font-medium text-gray-700">属性:</span>
                <div className="ml-2 mt-1 space-y-1">
                  {Object.entries(selectedNode.properties).map(([key, value]) => (
                    <div key={key} className="text-xs">
                      <span className="text-gray-600">{key}:</span>
                      <span className="ml-1 text-gray-900">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default KnowledgeGraph
