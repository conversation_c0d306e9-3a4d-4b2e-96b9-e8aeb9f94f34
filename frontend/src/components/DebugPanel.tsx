import React, { useState } from 'react'
import {
  BarChart3,
  Network,
  FileText,
  Activity,
  Clock,
  MessageSquare,
  TrendingUp
} from 'lucide-react'
import { useStore } from '../store/useStore'
import { cn } from '../lib/utils'
import KnowledgeGraph from './KnowledgeGraph'

const tabs = [
  { id: 'performance', name: '性能监控', icon: BarChart3 },
  { id: 'knowledge-graph', name: '知识图谱', icon: Network },
  { id: 'sources', name: '源内容', icon: FileText },
  { id: 'trace', name: '执行轨迹', icon: Activity },
]

const DebugPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('performance')
  const { performanceMetrics, messages, currentKGData } = useStore()

  const renderPerformanceTab = () => {
    const avgDuration = performanceMetrics.length > 0 
      ? performanceMetrics.reduce((sum, metric) => sum + metric.duration, 0) / performanceMetrics.length
      : 0

    const totalRequests = performanceMetrics.length
    const recentMetrics = performanceMetrics.slice(-10)

    return (
      <div className="space-y-4">
        {/* 统计卡片 */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">平均响应时间</span>
            </div>
            <div className="text-lg font-bold text-blue-900 mt-1">
              {avgDuration.toFixed(2)}ms
            </div>
          </div>
          
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center space-x-2">
              <MessageSquare className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">总请求数</span>
            </div>
            <div className="text-lg font-bold text-green-900 mt-1">
              {totalRequests}
            </div>
          </div>
        </div>

        {/* 最近请求列表 */}
        <div>
          <h4 className="text-sm font-semibold text-gray-700 mb-2">最近请求</h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {recentMetrics.map((metric, index) => (
              <div key={index} className="bg-gray-50 p-3 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {metric.operation}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(metric.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900">
                      {metric.duration.toFixed(2)}ms
                    </div>
                    {metric.messageLength && (
                      <div className="text-xs text-gray-500">
                        {metric.messageLength} 字符
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const renderKnowledgeGraphTab = () => {
    if (!currentKGData || !currentKGData.nodes || currentKGData.nodes.length === 0) {
      return (
        <div className="text-center py-8">
          <Network className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500 text-sm">暂无知识图谱数据</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {/* 知识图谱可视化 */}
        <div className="h-80">
          <KnowledgeGraph data={currentKGData} className="h-full" />
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-purple-900">实体数量</div>
            <div className="text-lg font-bold text-purple-900">
              {currentKGData.nodes.length}
            </div>
          </div>

          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-orange-900">关系数量</div>
            <div className="text-lg font-bold text-orange-900">
              {currentKGData.links?.length || 0}
            </div>
          </div>
        </div>

        {/* 实体列表 */}
        <div>
          <h4 className="text-sm font-semibold text-gray-700 mb-2">实体列表</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {currentKGData.nodes.slice(0, 5).map((node, index) => (
              <div key={index} className="bg-gray-50 p-2 rounded text-sm">
                <div className="font-medium text-gray-900">{node.label}</div>
                <div className="text-xs text-gray-500">
                  {node.type || '未知类型'}
                </div>
              </div>
            ))}
            {currentKGData.nodes.length > 5 && (
              <div className="text-xs text-gray-500 text-center">
                还有 {currentKGData.nodes.length - 5} 个实体...
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderSourcesTab = () => {
    return (
      <div className="text-center py-8">
        <FileText className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500 text-sm">源内容功能开发中...</p>
      </div>
    )
  }

  const renderTraceTab = () => {
    return (
      <div className="text-center py-8">
        <Activity className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500 text-sm">执行轨迹功能开发中...</p>
      </div>
    )
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'performance':
        return renderPerformanceTab()
      case 'knowledge-graph':
        return renderKnowledgeGraphTab()
      case 'sources':
        return renderSourcesTab()
      case 'trace':
        return renderTraceTab()
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="flex-shrink-0 p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">调试面板</h2>
      </div>

      {/* 标签页 */}
      <div className="flex-shrink-0 border-b border-gray-200">
        <div className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex-1 flex items-center justify-center space-x-2 py-3 px-2 text-sm font-medium border-b-2 transition-colors",
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600 bg-blue-50"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                )}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.name}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        {renderTabContent()}
      </div>
    </div>
  )
}

export default DebugPanel
