// Version 2.5.7 react-kapsule - https://github.com/vasturiano/react-kapsule
!function(r,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define(["react"],t):(r="undefined"!=typeof globalThis?globalThis:r||self).fromKapsule=t(r.React)}(this,(function(r){"use strict";function t(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function e(r,t){return function(r){if(Array.isArray(r))return r}(r)||function(r,t){var e=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=e){var n,o,u,i,a=[],f=!0,c=!1;try{if(u=(e=e.call(r)).next,0===t);else for(;!(f=(n=u.call(e)).done)&&(a.push(n.value),a.length!==t);f=!0);}catch(r){c=!0,o=r}finally{try{if(!f&&null!=e.return&&(i=e.return(),Object(i)!==i))return}finally{if(c)throw o}}return a}}(r,t)||o(r,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(r){return function(r){if(Array.isArray(r))return t(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||o(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(r,e){if(r){if("string"==typeof r)return t(r,e);var n={}.toString.call(r).slice(8,-1);return"Object"===n&&r.constructor&&(n=r.constructor.name),"Map"===n||"Set"===n?Array.from(r):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(r,e):void 0}}function u(r,t,e){return(t=function(r){var t=function(r,t){if("object"!=typeof r||null===r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==typeof t?t:String(t)}(t))in r?Object.defineProperty(r,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):r[t]=e,r}function i(r,t){return function(r){if(Array.isArray(r))return r}(r)||function(r,t){var e=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=e){var n,o,u,i,a=[],f=!0,c=!1;try{if(u=(e=e.call(r)).next,0===t){if(Object(e)!==e)return;f=!1}else for(;!(f=(n=u.call(e)).done)&&(a.push(n.value),a.length!==t);f=!0);}catch(r){c=!0,o=r}finally{try{if(!f&&null!=e.return&&(i=e.return(),Object(i)!==i))return}finally{if(c)throw o}}return a}}(r,t)||f(r,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(r){return function(r){if(Array.isArray(r))return c(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||f(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(r,t){if(r){if("string"==typeof r)return c(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?c(r,t):void 0}}function c(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function l(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.useEffect,o=r.useRef(),u=r.useRef(!1),i=r.useRef(!1),a=e(r.useState(0),2);a[0];var f=a[1];u.current&&(i.current=!0),n((function(){return u.current||(o.current=t(),u.current=!0),f((function(r){return r+1})),function(){i.current&&o.current&&o.current()}}),[])}return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=e.wrapperElementType,f=void 0===o?"div":o,c=e.nodeMapper,s=void 0===c?function(r){return r}:c,y=e.methodNames,d=void 0===y?[]:y,m=e.initPropNames,p=void 0===m?[]:m;return r.forwardRef((function(e,o){var c=r.useRef(),y=r.useMemo((function(){var r=Object.fromEntries(p.filter((function(r){return e.hasOwnProperty(r)})).map((function(r){return[r,e[r]]})));return t(r)}),[]);l((function(){y(s(c.current))}),r.useLayoutEffect),l((function(){return y._destructor instanceof Function?y._destructor:void 0}));var m,b,v,h=r.useCallback((function(r){for(var t=arguments.length,e=new Array(t>1?t-1:0),n=1;n<t;n++)e[n-1]=arguments[n];return y[r]instanceof Function?y[r].apply(y,e):void 0}),[y]),g=r.useRef({});return Object.keys((m=e,b=[].concat(n(d),n(p)),v=new Set(b),Object.assign.apply(Object,[{}].concat(a(Object.entries(m).filter((function(r){var t=i(r,1)[0];return!v.has(t)})).map((function(r){var t=i(r,2);return u({},t[0],t[1])}))))))).filter((function(r){return g.current[r]!==e[r]})).forEach((function(r){return h(r,e[r])})),g.current=e,r.useImperativeHandle(o,(function(){return Object.fromEntries(d.map((function(r){return[r,function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return h.apply(void 0,[r].concat(e))}]})))}),[h]),r.createElement(f,{ref:c})}))}}));
