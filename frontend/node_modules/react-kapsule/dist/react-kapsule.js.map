{"version": 3, "file": "react-kapsule.js", "sources": ["../node_modules/jerrypick/dist/jerrypick.mjs", "../src/index.js"], "sourcesContent": ["function _iterableToArrayLimit(arr, i) {\n  var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (null != _i) {\n    var _s,\n      _e,\n      _x,\n      _r,\n      _arr = [],\n      _n = !0,\n      _d = !1;\n    try {\n      if (_x = (_i = _i.call(arr)).next, 0 === i) {\n        if (Object(_i) !== _i) return;\n        _n = !1;\n      } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0);\n    } catch (err) {\n      _d = !0, _e = err;\n    } finally {\n      try {\n        if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return;\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar pluck = function pluck(obj, keys) {\n  return Object.assign.apply(Object, [{}].concat(_toConsumableArray(keys.map(function (key) {\n    return _defineProperty({}, key, obj[key]);\n  }))));\n};\nvar omit = function omit(obj, keys) {\n  var keySet = new Set(keys);\n  return Object.assign.apply(Object, [{}].concat(_toConsumableArray(Object.entries(obj).filter(function (_ref2) {\n    var _ref3 = _slicedToArray(_ref2, 1),\n      key = _ref3[0];\n    return !keySet.has(key);\n  }).map(function (_ref4) {\n    var _ref5 = _slicedToArray(_ref4, 2),\n      key = _ref5[0],\n      val = _ref5[1];\n    return _defineProperty({}, key, val);\n  }))));\n};\n\nexport { omit, pluck };\n", "import React, {\n  useState,\n  useEffect,\n  useLayoutEffect,\n  useRef,\n  useMemo,\n  useCallback,\n  forwardRef,\n  useImperativeHandle\n} from 'react';\n\nimport { omit } from 'jerrypick';\n\nexport default function(kapsuleComponent, {\n  wrapperElementType = 'div',\n  nodeMapper = node => node,\n  methodNames = [],\n  initPropNames = []\n} = {}) {\n\n  return forwardRef((props, ref) => {\n    const domEl = useRef();\n\n    // instantiate the inner kapsule component with the defined initPropNames\n    const comp = useMemo(() => {\n      const configOptions = Object.fromEntries(\n        initPropNames\n          .filter(p => props.hasOwnProperty(p))\n          .map(prop => [prop, props[prop]])\n      );\n\n      return kapsuleComponent(configOptions);\n    }, []);\n\n    useEffectOnce(() => {\n      comp(nodeMapper(domEl.current)); // mount kapsule synchronously on this element ref, optionally mapped into an object that the kapsule understands\n    }, useLayoutEffect);\n\n    useEffectOnce(() => {\n      // invoke destructor on unmount, if it exists\n      return comp._destructor instanceof Function ? comp._destructor : undefined;\n    });\n\n    // Call a component method\n    const _call = useCallback((method, ...args) =>\n      comp[method] instanceof Function\n        ? comp[method](...args)\n        : undefined // method not found\n    , [comp]);\n\n    // propagate component props that have changed\n    const prevPropsRef = useRef({});\n    Object.keys(omit(props, [...methodNames, ...initPropNames])) // initPropNames or methodNames should not be called\n      .filter(p => prevPropsRef.current[p] !== props[p])\n      .forEach(p => _call(p, props[p]));\n    prevPropsRef.current = props;\n\n    // bind external methods to parent ref\n    useImperativeHandle(ref, () => Object.fromEntries(\n      methodNames.map(method =>\n        [\n          method,\n          (...args) => _call(method, ...args)\n        ]\n      )\n    ), [_call]);\n\n    return React.createElement(wrapperElementType, { ref: domEl });\n  });\n}\n\n//\n\n// Handle R18 strict mode double mount at init\nfunction useEffectOnce(effect, useEffectFn = useEffect) {\n  const destroyFunc = useRef();\n  const effectCalled = useRef(false);\n  const renderAfterCalled = useRef(false);\n  const [val, setVal] = useState(0);\n\n  if (effectCalled.current) {\n    renderAfterCalled.current = true;\n  }\n\n  useEffectFn(() => {\n    // only execute the effect first time around\n    if (!effectCalled.current) {\n      destroyFunc.current = effect();\n      effectCalled.current = true;\n    }\n\n    // this forces one render after the effect is run\n    setVal((val) => val + 1);\n\n    return () => {\n      // if the comp didn't render since the useEffect was called,\n      // we know it's the dummy React cycle\n      if (!renderAfterCalled.current) return;\n      if (destroyFunc.current) destroyFunc.current();\n    };\n  }, []);\n}\n"], "names": ["kapsuleComponent", "_ref", "arguments", "length", "undefined", "_ref$wrapperElementTy", "wrapperElementType", "_ref$nodeMapper", "nodeMapper", "node", "_ref$methodNames", "methodNames", "_ref$initPropNames", "initPropNames", "forwardRef", "props", "ref", "domEl", "useRef", "comp", "useMemo", "configOptions", "Object", "fromEntries", "filter", "p", "hasOwnProperty", "map", "prop", "useEffectOnce", "current", "useLayoutEffect", "_destructor", "Function", "_call", "useCallback", "method", "_len", "args", "Array", "_key", "apply", "prevPropsRef", "keys", "omit", "concat", "_toConsumableArray", "for<PERSON>ach", "useImperativeHandle", "_len2", "_key2", "React", "createElement", "effect", "useEffectFn", "useEffect", "destroyFunc", "effectCalled", "renderAfterCalled", "_useState", "useState", "_useState2", "_slicedToArray", "val", "setVal"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,SAAS,qBAAqB,CAAC,GAAG,EAAE,CAAC,EAAE;EACvC,EAAE,IAAI,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,WAAW,IAAI,OAAO,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,CAAC;EACzG,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;EAClB,IAAI,IAAI,EAAE;EACV,MAAM,EAAE;EACR,MAAM,EAAE;EACR,MAAM,EAAE;EACR,MAAM,IAAI,GAAG,EAAE;EACf,MAAM,EAAE,GAAG,IAAE;EACb,MAAM,EAAE,GAAG,KAAE;EACb,IAAI,IAAI;EACR,MAAM,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE;EAClD,QAAQ,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE;EAC/B,QAAQ,EAAE,GAAG,CAAC,CAAC;EACf,OAAO,MAAM,OAAO,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;EACzG,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB,MAAM,EAAE,GAAG,IAAE,EAAE,EAAE,GAAG,GAAG;EACvB,KAAK,SAAS;EACd,MAAM,IAAI;EACV,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;EAC/E,OAAO,SAAS;EAChB,QAAQ,IAAI,EAAE,EAAE,MAAM,EAAE;EACxB;EACA;EACA,IAAI,OAAO,IAAI;EACf;EACA;EACA,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;EAC1C,EAAE,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC;EAC3B,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;EAClB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;EACpC,MAAM,KAAK,EAAE,KAAK;EAClB,MAAM,UAAU,EAAE,IAAI;EACtB,MAAM,YAAY,EAAE,IAAI;EACxB,MAAM,QAAQ,EAAE;EAChB,KAAK,CAAC;EACN,GAAG,MAAM;EACT,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK;EACpB;EACA,EAAE,OAAO,GAAG;EACZ;EACA,SAAS,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE;EAChC,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,2BAA2B,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,gBAAgB,EAAE;EAC3H;EACA,SAAS,kBAAkB,CAAC,GAAG,EAAE;EACjC,EAAE,OAAO,kBAAkB,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,2BAA2B,CAAC,GAAG,CAAC,IAAI,kBAAkB,EAAE;EACrH;EACA,SAAS,kBAAkB,CAAC,GAAG,EAAE;EACjC,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC;EACvD;EACA,SAAS,eAAe,CAAC,GAAG,EAAE;EAC9B,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,GAAG;EACpC;EACA,SAAS,gBAAgB,CAAC,IAAI,EAAE;EAChC,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;EAC3H;EACA,SAAS,2BAA2B,CAAC,CAAC,EAAE,MAAM,EAAE;EAChD,EAAE,IAAI,CAAC,CAAC,EAAE;EACV,EAAE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC;EAChE,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACxD,EAAE,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI;EAC7D,EAAE,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;EACtD,EAAE,IAAI,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC;EAClH;EACA,SAAS,iBAAiB,CAAC,GAAG,EAAE,GAAG,EAAE;EACrC,EAAE,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM;EACvD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EACvE,EAAE,OAAO,IAAI;EACb;EACA,SAAS,kBAAkB,GAAG;EAC9B,EAAE,MAAM,IAAI,SAAS,CAAC,sIAAsI,CAAC;EAC7J;EACA,SAAS,gBAAgB,GAAG;EAC5B,EAAE,MAAM,IAAI,SAAS,CAAC,2IAA2I,CAAC;EAClK;EACA,SAAS,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE;EACnC,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK;EAC/D,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;EACtC,EAAE,IAAI,IAAI,KAAK,SAAS,EAAE;EAC1B,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAiB,CAAC;EACjD,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,OAAO,GAAG;EAC3C,IAAI,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC;EACvE;EACA,EAAE,OAAO,CAAC,IAAI,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,EAAE,KAAK,CAAC;EACrD;EACA,SAAS,cAAc,CAAC,GAAG,EAAE;EAC7B,EAAE,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC;EACvC,EAAE,OAAO,OAAO,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;EACpD;EAOA,IAAI,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE;EACpC,EAAE,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC;EAC5B,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE;EAChH,IAAI,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;EACxC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;EACpB,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;EAC3B,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE;EAC1B,IAAI,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;EACxC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;EACpB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;EACpB,IAAI,OAAO,eAAe,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACxC,GAAG,CAAC,CAAC,CAAC,CAAC;EACP,CAAC;;EC9Fc,cAAA,EAASA,gBAAgB,EAKhC;EAAA,EAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAJ,EAAE;MAAAG,qBAAA,GAAAJ,IAAA,CAJJK,kBAAkB;EAAlBA,IAAAA,kBAAkB,GAAAD,qBAAA,KAAG,MAAA,GAAA,KAAK,GAAAA,qBAAA;MAAAE,eAAA,GAAAN,IAAA,CAC1BO,UAAU;EAAVA,IAAAA,UAAU,GAAAD,eAAA,KAAG,MAAA,GAAA,UAAAE,IAAI,EAAA;EAAA,MAAA,OAAIA,IAAI;EAAA,KAAA,GAAAF,eAAA;MAAAG,gBAAA,GAAAT,IAAA,CACzBU,WAAW;EAAXA,IAAAA,WAAW,GAAAD,gBAAA,KAAG,MAAA,GAAA,EAAE,GAAAA,gBAAA;MAAAE,kBAAA,GAAAX,IAAA,CAChBY,aAAa;EAAbA,IAAAA,aAAa,GAAAD,kBAAA,KAAG,MAAA,GAAA,EAAE,GAAAA,kBAAA;EAGlB,EAAA,oBAAOE,gBAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EAChC,IAAA,IAAMC,KAAK,GAAGC,YAAM,EAAE;;EAEtB;EACA,IAAA,IAAMC,IAAI,GAAGC,aAAO,CAAC,YAAM;QACzB,IAAMC,aAAa,GAAGC,MAAM,CAACC,WAAW,CACtCV,aAAa,CACVW,MAAM,CAAC,UAAAC,CAAC,EAAA;EAAA,QAAA,OAAIV,KAAK,CAACW,cAAc,CAACD,CAAC,CAAC;EAAA,OAAA,CAAC,CACpCE,GAAG,CAAC,UAAAC,IAAI,EAAA;EAAA,QAAA,OAAI,CAACA,IAAI,EAAEb,KAAK,CAACa,IAAI,CAAC,CAAC;EAAA,OAAA,CACpC,CAAC;QAED,OAAO5B,gBAAgB,CAACqB,aAAa,CAAC;OACvC,EAAE,EAAE,CAAC;EAENQ,IAAAA,aAAa,CAAC,YAAM;QAClBV,IAAI,CAACX,UAAU,CAACS,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC;OACjC,EAAEC,qBAAe,CAAC;EAEnBF,IAAAA,aAAa,CAAC,YAAM;EAClB;QACA,OAAOV,IAAI,CAACa,WAAW,YAAYC,QAAQ,GAAGd,IAAI,CAACa,WAAW,GAAG5B,SAAS;EAC5E,KAAC,CAAC;;EAEF;EACA,IAAA,IAAM8B,KAAK,GAAGC,iBAAW,CAAC,UAACC,MAAM,EAAA;QAAA,KAAAC,IAAAA,IAAA,GAAAnC,SAAA,CAAAC,MAAA,EAAKmC,IAAI,OAAAC,KAAA,CAAAF,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,EAAA;EAAJF,QAAAA,IAAI,CAAAE,IAAA,GAAAtC,CAAAA,CAAAA,GAAAA,SAAA,CAAAsC,IAAA,CAAA;EAAA;EAAA,MAAA,OACxCrB,IAAI,CAACiB,MAAM,CAAC,YAAYH,QAAQ,GAC5Bd,IAAI,CAACiB,MAAM,CAAC,CAAAK,KAAA,CAAZtB,IAAI,EAAYmB,IAAI,CAAC,GACrBlC,SAAS;OAAC;QACd,CAACe,IAAI,CAAC,CAAC;;EAET;EACA,IAAA,IAAMuB,YAAY,GAAGxB,YAAM,CAAC,EAAE,CAAC;EAC/BI,IAAAA,MAAM,CAACqB,IAAI,CAACC,IAAI,CAAC7B,KAAK,KAAA8B,MAAA,CAAAC,oBAAA,CAAMnC,WAAW,GAAAmC,oBAAA,CAAKjC,aAAa,CAAC,CAAA,CAAC,CAAC;OACzDW,MAAM,CAAC,UAAAC,CAAC,EAAA;QAAA,OAAIiB,YAAY,CAACZ,OAAO,CAACL,CAAC,CAAC,KAAKV,KAAK,CAACU,CAAC,CAAC;EAAA,KAAA,CAAC,CACjDsB,OAAO,CAAC,UAAAtB,CAAC,EAAA;QAAA,OAAIS,KAAK,CAACT,CAAC,EAAEV,KAAK,CAACU,CAAC,CAAC,CAAC;OAAC,CAAA;MACnCiB,YAAY,CAACZ,OAAO,GAAGf,KAAK;;EAE5B;MACAiC,yBAAmB,CAAChC,GAAG,EAAE,YAAA;QAAA,OAAMM,MAAM,CAACC,WAAW,CAC/CZ,WAAW,CAACgB,GAAG,CAAC,UAAAS,MAAM,EAAA;UAAA,OACpB,CACEA,MAAM,EACN,YAAA;EAAA,UAAA,KAAA,IAAAa,KAAA,GAAA/C,SAAA,CAAAC,MAAA,EAAImC,IAAI,GAAAC,IAAAA,KAAA,CAAAU,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJZ,YAAAA,IAAI,CAAAY,KAAA,CAAAhD,GAAAA,SAAA,CAAAgD,KAAA,CAAA;EAAA;YAAA,OAAKhB,KAAK,CAAAO,KAAA,CAAA,MAAA,EAAA,CAACL,MAAM,CAAAS,CAAAA,MAAA,CAAKP,IAAI,CAAC,CAAA;WACpC,CAAA;EAAA,OACH,CACF,CAAC;OAAE,EAAA,CAACJ,KAAK,CAAC,CAAC;EAEX,IAAA,oBAAOiB,KAAK,CAACC,aAAa,CAAC9C,kBAAkB,EAAE;EAAEU,MAAAA,GAAG,EAAEC;EAAM,KAAC,CAAC;EAChE,GAAC,CAAC;EACJ;;EAEA;;EAEA;EACA,SAASY,aAAaA,CAACwB,MAAM,EAA2B;EAAA,EAAA,IAAzBC,WAAW,GAAApD,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGqD,eAAS;EACpD,EAAA,IAAMC,WAAW,GAAGtC,YAAM,EAAE;EAC5B,EAAA,IAAMuC,YAAY,GAAGvC,YAAM,CAAC,KAAK,CAAC;EAClC,EAAA,IAAMwC,iBAAiB,GAAGxC,YAAM,CAAC,KAAK,CAAC;EACvC,EAAA,IAAAyC,SAAA,GAAsBC,cAAQ,CAAC,CAAC,CAAC;MAAAC,UAAA,GAAAC,gBAAA,CAAAH,SAAA,EAAA,CAAA,CAAA;EAA1BI,IAAGF,UAAA,CAAA,CAAA,CAAA;EAAEG,QAAAA,MAAM,GAAAH,UAAA,CAAA,CAAA;IAElB,IAAIJ,YAAY,CAAC3B,OAAO,EAAE;MACxB4B,iBAAiB,CAAC5B,OAAO,GAAG,IAAI;EAClC;EAEAwB,EAAAA,WAAW,CAAC,YAAM;EAChB;EACA,IAAA,IAAI,CAACG,YAAY,CAAC3B,OAAO,EAAE;EACzB0B,MAAAA,WAAW,CAAC1B,OAAO,GAAGuB,MAAM,EAAE;QAC9BI,YAAY,CAAC3B,OAAO,GAAG,IAAI;EAC7B;;EAEA;MACAkC,MAAM,CAAC,UAACD,GAAG,EAAA;QAAA,OAAKA,GAAG,GAAG,CAAC;OAAC,CAAA;EAExB,IAAA,OAAO,YAAM;EACX;EACA;EACA,MAAA,IAAI,CAACL,iBAAiB,CAAC5B,OAAO,EAAE;QAChC,IAAI0B,WAAW,CAAC1B,OAAO,EAAE0B,WAAW,CAAC1B,OAAO,EAAE;OAC/C;KACF,EAAE,EAAE,CAAC;EACR;;;;;;;;", "x_google_ignoreList": [0]}