!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).predicates={})}(this,(function(t){"use strict";const n=11102230246251565e-32,e=134217729,o=(3+8*n)*n;function r(t,n,e,o,r){let f,s,u,i,a=n[0],c=o[0],l=0,b=0;c>a==c>-a?(f=a,a=n[++l]):(f=c,c=o[++b]);let h=0;if(l<t&&b<e)for(c>a==c>-a?(s=a+f,u=f-(s-a),a=n[++l]):(s=c+f,u=f-(s-c),c=o[++b]),f=s,0!==u&&(r[h++]=u);l<t&&b<e;)c>a==c>-a?(s=f+a,i=s-f,u=f-(s-i)+(a-i),a=n[++l]):(s=f+c,i=s-f,u=f-(s-i)+(c-i),c=o[++b]),f=s,0!==u&&(r[h++]=u);for(;l<t;)s=f+a,i=s-f,u=f-(s-i)+(a-i),a=n[++l],f=s,0!==u&&(r[h++]=u);for(;b<e;)s=f+c,i=s-f,u=f-(s-i)+(c-i),c=o[++b],f=s,0!==u&&(r[h++]=u);return 0===f&&0!==h||(r[h++]=f),h}function f(t,n,o,r){let f,s,u,i,a,c,l,b,h,d,M;l=e*o,d=l-(l-o),M=o-d;let p=n[0];f=p*o,l=e*p,b=l-(l-p),h=p-b,u=h*M-(f-b*d-h*d-b*M);let y=0;0!==u&&(r[y++]=u);for(let x=1;x<t;x++)p=n[x],i=p*o,l=e*p,b=l-(l-p),h=p-b,a=h*M-(i-b*d-h*d-b*M),s=f+a,c=s-f,u=f-(s-c)+(a-c),0!==u&&(r[y++]=u),f=i+s,u=s-(f-i),0!==u&&(r[y++]=u);return 0===f&&0!==y||(r[y++]=f),y}function s(t){return new Float64Array(t)}const u=3330669073875473e-31,i=32047474274603644e-47,a=s(4),c=s(4),l=s(4),b=s(4),h=s(4),d=s(4),M=s(4),p=s(4),y=s(4),x=s(8),g=s(8),m=s(8),T=s(4),j=s(8),w=s(8),A=s(8),F=s(12);let k=s(192),q=s(192);function v(t,n,e){t=r(t,k,n,e,q);const o=k;return k=q,q=o,t}function z(t,n,o,r,f,s,u,i){let a,c,l,b,h,d,M,p,y,x,g,m,T,j,w;return 0===t?0===n?(u[0]=0,i[0]=0,1):(w=-n,x=w*o,c=e*w,l=c-(c-w),b=w-l,c=e*o,h=c-(c-o),d=o-h,u[0]=b*d-(x-l*h-b*h-l*d),u[1]=x,x=n*f,c=e*n,l=c-(c-n),b=n-l,c=e*f,h=c-(c-f),d=f-h,i[0]=b*d-(x-l*h-b*h-l*d),i[1]=x,2):0===n?(x=t*r,c=e*t,l=c-(c-t),b=t-l,c=e*r,h=c-(c-r),d=r-h,u[0]=b*d-(x-l*h-b*h-l*d),u[1]=x,w=-t,x=w*s,c=e*w,l=c-(c-w),b=w-l,c=e*s,h=c-(c-s),d=s-h,i[0]=b*d-(x-l*h-b*h-l*d),i[1]=x,2):(x=t*r,c=e*t,l=c-(c-t),b=t-l,c=e*r,h=c-(c-r),d=r-h,g=b*d-(x-l*h-b*h-l*d),m=n*o,c=e*n,l=c-(c-n),b=n-l,c=e*o,h=c-(c-o),d=o-h,T=b*d-(m-l*h-b*h-l*d),M=g-T,a=g-M,u[0]=g-(M+a)+(a-T),p=x+M,a=p-x,y=x-(p-a)+(M-a),M=y-m,a=y-M,u[1]=y-(M+a)+(a-m),j=p+M,a=j-p,u[2]=p-(j-a)+(M-a),u[3]=j,x=n*f,c=e*n,l=c-(c-n),b=n-l,c=e*f,h=c-(c-f),d=f-h,g=b*d-(x-l*h-b*h-l*d),m=t*s,c=e*t,l=c-(c-t),b=t-l,c=e*s,h=c-(c-s),d=s-h,T=b*d-(m-l*h-b*h-l*d),M=g-T,a=g-M,i[0]=g-(M+a)+(a-T),p=x+M,a=p-x,y=x-(p-a)+(M-a),M=y-m,a=y-M,i[1]=y-(M+a)+(a-m),j=p+M,a=j-p,i[2]=p-(j-a)+(M-a),i[3]=j,4)}function B(t,n,o,r,f){let s,u,i,a,c,l,b,h,d,M,p,y,x;return p=n*o,u=e*n,i=u-(u-n),a=n-i,u=e*o,c=u-(u-o),l=o-c,y=a*l-(p-i*c-a*c-i*l),u=e*r,c=u-(u-r),l=r-c,b=y*r,u=e*y,i=u-(u-y),a=y-i,T[0]=a*l-(b-i*c-a*c-i*l),h=p*r,u=e*p,i=u-(u-p),a=p-i,M=a*l-(h-i*c-a*c-i*l),d=b+M,s=d-b,T[1]=b-(d-s)+(M-s),x=h+d,T[2]=d-(x-h),T[3]=x,t=v(t,4,T),0!==f&&(u=e*f,c=u-(u-f),l=f-c,b=y*f,u=e*y,i=u-(u-y),a=y-i,T[0]=a*l-(b-i*c-a*c-i*l),h=p*f,u=e*p,i=u-(u-p),a=p-i,M=a*l-(h-i*c-a*c-i*l),d=b+M,s=d-b,T[1]=b-(d-s)+(M-s),x=h+d,T[2]=d-(x-h),T[3]=x,t=v(t,4,T)),t}t.orient3d=function(t,n,s,T,q,C,D,E,G,H,I,J){const K=t-H,L=T-H,N=D-H,O=n-I,P=q-I,Q=E-I,R=s-J,S=C-J,U=G-J,V=L*Q,W=N*P,X=N*O,Y=K*Q,Z=K*P,$=L*O,_=R*(V-W)+S*(X-Y)+U*(Z-$),tt=(Math.abs(V)+Math.abs(W))*Math.abs(R)+(Math.abs(X)+Math.abs(Y))*Math.abs(S)+(Math.abs(Z)+Math.abs($))*Math.abs(U),nt=7771561172376103e-31*tt;return _>nt||-_>nt?_:function(t,n,s,T,q,C,D,E,G,H,I,J,K){let L,N,O,P,Q,R,S,U,V,W,X,Y,Z,$,_,tt,nt,et,ot,rt,ft,st,ut,it;const at=t-H,ct=T-H,lt=D-H,bt=n-I,ht=q-I,dt=E-I,Mt=s-J,pt=C-J,yt=G-J;rt=ct*dt,Y=e*ct,Z=Y-(Y-ct),$=ct-Z,Y=e*dt,_=Y-(Y-dt),tt=dt-_,ft=$*tt-(rt-Z*_-$*_-Z*tt),st=lt*ht,Y=e*lt,Z=Y-(Y-lt),$=lt-Z,Y=e*ht,_=Y-(Y-ht),tt=ht-_,ut=$*tt-(st-Z*_-$*_-Z*tt),nt=ft-ut,X=ft-nt,a[0]=ft-(nt+X)+(X-ut),et=rt+nt,X=et-rt,ot=rt-(et-X)+(nt-X),nt=ot-st,X=ot-nt,a[1]=ot-(nt+X)+(X-st),it=et+nt,X=it-et,a[2]=et-(it-X)+(nt-X),a[3]=it,rt=lt*bt,Y=e*lt,Z=Y-(Y-lt),$=lt-Z,Y=e*bt,_=Y-(Y-bt),tt=bt-_,ft=$*tt-(rt-Z*_-$*_-Z*tt),st=at*dt,Y=e*at,Z=Y-(Y-at),$=at-Z,Y=e*dt,_=Y-(Y-dt),tt=dt-_,ut=$*tt-(st-Z*_-$*_-Z*tt),nt=ft-ut,X=ft-nt,c[0]=ft-(nt+X)+(X-ut),et=rt+nt,X=et-rt,ot=rt-(et-X)+(nt-X),nt=ot-st,X=ot-nt,c[1]=ot-(nt+X)+(X-st),it=et+nt,X=it-et,c[2]=et-(it-X)+(nt-X),c[3]=it,rt=at*ht,Y=e*at,Z=Y-(Y-at),$=at-Z,Y=e*ht,_=Y-(Y-ht),tt=ht-_,ft=$*tt-(rt-Z*_-$*_-Z*tt),st=ct*bt,Y=e*ct,Z=Y-(Y-ct),$=ct-Z,Y=e*bt,_=Y-(Y-bt),tt=bt-_,ut=$*tt-(st-Z*_-$*_-Z*tt),nt=ft-ut,X=ft-nt,l[0]=ft-(nt+X)+(X-ut),et=rt+nt,X=et-rt,ot=rt-(et-X)+(nt-X),nt=ot-st,X=ot-nt,l[1]=ot-(nt+X)+(X-st),it=et+nt,X=it-et,l[2]=et-(it-X)+(nt-X),l[3]=it,L=r(r(f(4,a,Mt,j),j,f(4,c,pt,w),w,A),A,f(4,l,yt,j),j,k);let xt=function(t,n){let e=n[0];for(let o=1;o<t;o++)e+=n[o];return e}(L,k),gt=u*K;if(xt>=gt||-xt>=gt)return xt;if(X=t-at,N=t-(at+X)+(X-H),X=T-ct,O=T-(ct+X)+(X-H),X=D-lt,P=D-(lt+X)+(X-H),X=n-bt,Q=n-(bt+X)+(X-I),X=q-ht,R=q-(ht+X)+(X-I),X=E-dt,S=E-(dt+X)+(X-I),X=s-Mt,U=s-(Mt+X)+(X-J),X=C-pt,V=C-(pt+X)+(X-J),X=G-yt,W=G-(yt+X)+(X-J),0===N&&0===O&&0===P&&0===Q&&0===R&&0===S&&0===U&&0===V&&0===W)return xt;if(gt=i*K+o*Math.abs(xt),xt+=Mt*(ct*S+dt*O-(ht*P+lt*R))+U*(ct*dt-ht*lt)+pt*(lt*Q+bt*P-(dt*N+at*S))+V*(lt*bt-dt*at)+yt*(at*R+ht*N-(bt*O+ct*Q))+W*(at*ht-bt*ct),xt>=gt||-xt>=gt)return xt;const mt=z(N,Q,ct,ht,lt,dt,b,h),Tt=z(O,R,lt,dt,at,bt,d,M),jt=z(P,S,at,bt,ct,ht,p,y),wt=r(Tt,d,jt,y,x);L=v(L,f(wt,x,Mt,A),A);const At=r(jt,p,mt,h,g);L=v(L,f(At,g,pt,A),A);const Ft=r(mt,b,Tt,M,m);return L=v(L,f(Ft,m,yt,A),A),0!==U&&(L=v(L,f(4,a,U,F),F),L=v(L,f(wt,x,U,A),A)),0!==V&&(L=v(L,f(4,c,V,F),F),L=v(L,f(At,g,V,A),A)),0!==W&&(L=v(L,f(4,l,W,F),F),L=v(L,f(Ft,m,W,A),A)),0!==N&&(0!==R&&(L=B(L,N,R,yt,W)),0!==S&&(L=B(L,-N,S,pt,V))),0!==O&&(0!==S&&(L=B(L,O,S,Mt,U)),0!==Q&&(L=B(L,-O,Q,yt,W))),0!==P&&(0!==Q&&(L=B(L,P,Q,pt,V)),0!==R&&(L=B(L,-P,R,Mt,U))),k[L-1]}(t,n,s,T,q,C,D,E,G,H,I,J,tt)},t.orient3dfast=function(t,n,e,o,r,f,s,u,i,a,c,l){const b=n-c,h=r-c,d=u-c,M=e-l,p=f-l,y=i-l;return(t-a)*(h*y-p*d)+(o-a)*(d*M-y*b)+(s-a)*(b*p-M*h)}}));
