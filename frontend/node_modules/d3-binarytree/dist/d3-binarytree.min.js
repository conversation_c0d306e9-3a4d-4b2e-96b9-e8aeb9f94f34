// https://github.com/vasturiano/d3-binarytree v1.0.2
!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{})}(this,(function(t){"use strict";function r(t,r,e){if(isNaN(r))return t;var n,i,o,s,h,a,u=t._root,f={data:e},l=t._x0,x=t._x1;if(!u)return t._root=f,t;for(;u.length;)if((s=r>=(i=(l+x)/2))?l=i:x=i,n=u,!(u=u[h=+s]))return n[h]=f,t;if(r===(o=+t._x.call(null,u.data)))return f.next=u,n?n[h]=f:t._root=f,t;do{n=n?n[h]=new Array(2):t._root=new Array(2),(s=r>=(i=(l+x)/2))?l=i:x=i}while((h=+s)==(a=+(o>=i)));return n[a]=u,n[h]=f,t}function e(t,r,e){this.node=t,this.x0=r,this.x1=e}function n(t){return t[0]}function i(t,r){var e=new o(null==r?n:r,NaN,NaN);return null==t?e:e.addAll(t)}function o(t,r,e){this._x=t,this._x0=r,this._x1=e,this._root=void 0}function s(t){for(var r={data:t.data},e=r;t=t.next;)e=e.next={data:t.data};return r}var h=i.prototype=o.prototype;h.copy=function(){var t,r,e=new o(this._x,this._x0,this._x1),n=this._root;if(!n)return e;if(!n.length)return e._root=s(n),e;for(t=[{source:n,target:e._root=new Array(2)}];n=t.pop();)for(var i=0;i<2;++i)(r=n.source[i])&&(r.length?t.push({source:r,target:n.target[i]=new Array(2)}):n.target[i]=s(r));return e},h.add=function(t){const e=+this._x.call(null,t);return r(this.cover(e),e,t)},h.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const e=t.length,n=new Float64Array(e);let i=1/0,o=-1/0;for(let r,s=0;s<e;++s)isNaN(r=+this._x.call(null,t[s]))||(n[s]=r,r<i&&(i=r),r>o&&(o=r));if(i>o)return this;this.cover(i).cover(o);for(let i=0;i<e;++i)r(this,n[i],t[i]);return this},h.cover=function(t){if(isNaN(t=+t))return this;var r=this._x0,e=this._x1;if(isNaN(r))e=(r=Math.floor(t))+1;else{for(var n,i,o=e-r||1,s=this._root;r>t||t>=e;)switch(i=+(t<r),(n=new Array(2))[i]=s,s=n,o*=2,i){case 0:e=r+o;break;case 1:r=e-o}this._root&&this._root.length&&(this._root=s)}return this._x0=r,this._x1=e,this},h.data=function(){var t=[];return this.visit((function(r){if(!r.length)do{t.push(r.data)}while(r=r.next)})),t},h.extent=function(t){return arguments.length?this.cover(+t[0][0]).cover(+t[1][0]):isNaN(this._x0)?void 0:[[this._x0],[this._x1]]},h.find=function(t,r){var n,i,o,s,h,a=this._x0,u=this._x1,f=[],l=this._root;for(l&&f.push(new e(l,a,u)),null==r?r=1/0:(a=t-r,u=t+r);s=f.pop();)if(!(!(l=s.node)||(i=s.x0)>u||(o=s.x1)<a))if(l.length){var x=(i+o)/2;f.push(new e(l[1],x,o),new e(l[0],i,x)),(h=+(t>=x))&&(s=f[f.length-1],f[f.length-1]=f[f.length-1-h],f[f.length-1-h]=s)}else{var _=Math.abs(t-+this._x.call(null,l.data));_<r&&(r=_,a=t-_,u=t+_,n=l.data)}return n},h.remove=function(t){if(isNaN(o=+this._x.call(null,t)))return this;var r,e,n,i,o,s,h,a,u,f=this._root,l=this._x0,x=this._x1;if(!f)return this;if(f.length)for(;;){if((h=o>=(s=(l+x)/2))?l=s:x=s,r=f,!(f=f[a=+h]))return this;if(!f.length)break;r[a+1&1]&&(e=r,u=a)}for(;f.data!==t;)if(n=f,!(f=f.next))return this;return(i=f.next)&&delete f.next,n?(i?n.next=i:delete n.next,this):r?(i?r[a]=i:delete r[a],(f=r[0]||r[1])&&f===(r[1]||r[0])&&!f.length&&(e?e[u]=f:this._root=f),this):(this._root=i,this)},h.removeAll=function(t){for(var r=0,e=t.length;r<e;++r)this.remove(t[r]);return this},h.root=function(){return this._root},h.size=function(){var t=0;return this.visit((function(r){if(!r.length)do{++t}while(r=r.next)})),t},h.visit=function(t){var r,n,i,o,s=[],h=this._root;for(h&&s.push(new e(h,this._x0,this._x1));r=s.pop();)if(!t(h=r.node,i=r.x0,o=r.x1)&&h.length){var a=(i+o)/2;(n=h[1])&&s.push(new e(n,a,o)),(n=h[0])&&s.push(new e(n,i,a))}return this},h.visitAfter=function(t){var r,n=[],i=[];for(this._root&&n.push(new e(this._root,this._x0,this._x1));r=n.pop();){var o=r.node;if(o.length){var s,h=r.x0,a=r.x1,u=(h+a)/2;(s=o[0])&&n.push(new e(s,h,u)),(s=o[1])&&n.push(new e(s,u,a))}i.push(r)}for(;r=i.pop();)t(r.node,r.x0,r.x1);return this},h.x=function(t){return arguments.length?(this._x=t,this):this._x},t.binarytree=i}));
