// https://github.com/vasturiano/d3-force-3d v3.0.6
!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("d3-binarytree"),require("d3-quadtree"),require("d3-octree"),require("d3-dispatch"),require("d3-timer")):"function"==typeof define&&define.amd?define(["exports","d3-binarytree","d3-quadtree","d3-octree","d3-dispatch","d3-timer"],t):t((n="undefined"!=typeof globalThis?globalThis:n||self).d3=n.d3||{},n.d3,n.d3,n.d3,n.d3,n.d3)}(this,(function(n,t,r,e,i,u){"use strict";function o(n){return function(){return n}}function f(n){return 1e-6*(n()-.5)}function a(n){return n.x+n.vx}function c(n){return n.y+n.vy}function l(n){return n.z+n.vz}function h(n){return n.index}function v(n,t){var r=n.get(t);if(!r)throw new Error("node not found: "+t);return r}const d=1664525,y=1013904223,s=4294967296;var g=3;function x(n){return n.x}function z(n){return n.y}function p(n){return n.z}var M=10,w=Math.PI*(3-Math.sqrt(5)),q=20*Math.PI/(9+Math.sqrt(221));n.forceCenter=function(n,t,r){var e,i=1;function u(){var u,o,f=e.length,a=0,c=0,l=0;for(u=0;u<f;++u)a+=(o=e[u]).x||0,c+=o.y||0,l+=o.z||0;for(a=(a/f-n)*i,c=(c/f-t)*i,l=(l/f-r)*i,u=0;u<f;++u)o=e[u],a&&(o.x-=a),c&&(o.y-=c),l&&(o.z-=l)}return null==n&&(n=0),null==t&&(t=0),null==r&&(r=0),u.initialize=function(n){e=n},u.x=function(t){return arguments.length?(n=+t,u):n},u.y=function(n){return arguments.length?(t=+n,u):t},u.z=function(n){return arguments.length?(r=+n,u):r},u.strength=function(n){return arguments.length?(i=+n,u):i},u},n.forceCollide=function(n){var i,u,h,v,d=1,y=1;function s(){for(var n,o,s,x,z,p,M,w,q=i.length,N=0;N<y;++N)for(o=(1===u?t.binarytree(i,a):2===u?r.quadtree(i,a,c):3===u?e.octree(i,a,c,l):null).visitAfter(g),n=0;n<q;++n)s=i[n],M=h[s.index],w=M*M,x=s.x+s.vx,u>1&&(z=s.y+s.vy),u>2&&(p=s.z+s.vz),o.visit(m);function m(n,t,r,e,i,o,a){var c=[t,r,e,i,o,a],l=c[0],h=c[1],y=c[2],g=c[u],q=c[u+1],N=c[u+2],m=n.data,A=n.r,b=M+A;if(!m)return l>x+b||g<x-b||u>1&&(h>z+b||q<z-b)||u>2&&(y>p+b||N<p-b);if(m.index>s.index){var k=x-m.x-m.vx,E=u>1?z-m.y-m.vy:0,j=u>2?p-m.z-m.vz:0,D=k*k+E*E+j*j;D<b*b&&(0===k&&(D+=(k=f(v))*k),u>1&&0===E&&(D+=(E=f(v))*E),u>2&&0===j&&(D+=(j=f(v))*j),D=(b-(D=Math.sqrt(D)))/D*d,s.vx+=(k*=D)*(b=(A*=A)/(w+A)),u>1&&(s.vy+=(E*=D)*b),u>2&&(s.vz+=(j*=D)*b),m.vx-=k*(b=1-b),u>1&&(m.vy-=E*b),u>2&&(m.vz-=j*b))}}}function g(n){if(n.data)return n.r=h[n.data.index];for(var t=n.r=0;t<Math.pow(2,u);++t)n[t]&&n[t].r>n.r&&(n.r=n[t].r)}function x(){if(i){var t,r,e=i.length;for(h=new Array(e),t=0;t<e;++t)r=i[t],h[r.index]=+n(r,t,i)}}return"function"!=typeof n&&(n=o(null==n?1:+n)),s.initialize=function(n,...t){i=n,v=t.find((n=>"function"==typeof n))||Math.random,u=t.find((n=>[1,2,3].includes(n)))||2,x()},s.iterations=function(n){return arguments.length?(y=+n,s):y},s.strength=function(n){return arguments.length?(d=+n,s):d},s.radius=function(t){return arguments.length?(n="function"==typeof t?t:o(+t),x(),s):n},s},n.forceLink=function(n){var t,r,e,i,u,a,c,l=h,d=function(n){return 1/Math.min(u[n.source.index],u[n.target.index])},y=o(30),s=1;function g(e){for(var u=0,o=n.length;u<s;++u)for(var l,h,v,d,y,g=0,x=0,z=0,p=0;g<o;++g)h=(l=n[g]).source,x=(v=l.target).x+v.vx-h.x-h.vx||f(c),i>1&&(z=v.y+v.vy-h.y-h.vy||f(c)),i>2&&(p=v.z+v.vz-h.z-h.vz||f(c)),x*=d=((d=Math.sqrt(x*x+z*z+p*p))-r[g])/d*e*t[g],z*=d,p*=d,v.vx-=x*(y=a[g]),i>1&&(v.vy-=z*y),i>2&&(v.vz-=p*y),h.vx+=x*(y=1-y),i>1&&(h.vy+=z*y),i>2&&(h.vz+=p*y)}function x(){if(e){var i,o,f=e.length,c=n.length,h=new Map(e.map(((n,t)=>[l(n,t,e),n])));for(i=0,u=new Array(f);i<c;++i)(o=n[i]).index=i,"object"!=typeof o.source&&(o.source=v(h,o.source)),"object"!=typeof o.target&&(o.target=v(h,o.target)),u[o.source.index]=(u[o.source.index]||0)+1,u[o.target.index]=(u[o.target.index]||0)+1;for(i=0,a=new Array(c);i<c;++i)o=n[i],a[i]=u[o.source.index]/(u[o.source.index]+u[o.target.index]);t=new Array(c),z(),r=new Array(c),p()}}function z(){if(e)for(var r=0,i=n.length;r<i;++r)t[r]=+d(n[r],r,n)}function p(){if(e)for(var t=0,i=n.length;t<i;++t)r[t]=+y(n[t],t,n)}return null==n&&(n=[]),g.initialize=function(n,...t){e=n,c=t.find((n=>"function"==typeof n))||Math.random,i=t.find((n=>[1,2,3].includes(n)))||2,x()},g.links=function(t){return arguments.length?(n=t,x(),g):n},g.id=function(n){return arguments.length?(l=n,g):l},g.iterations=function(n){return arguments.length?(s=+n,g):s},g.strength=function(n){return arguments.length?(d="function"==typeof n?n:o(+n),z(),g):d},g.distance=function(n){return arguments.length?(y="function"==typeof n?n:o(+n),p(),g):y},g},n.forceManyBody=function(){var n,i,u,a,c,l,h=o(-30),v=1,d=1/0,y=.81;function s(o){var f,a=n.length,l=(1===i?t.binarytree(n,x):2===i?r.quadtree(n,x,z):3===i?e.octree(n,x,z,p):null).visitAfter(M);for(c=o,f=0;f<a;++f)u=n[f],l.visit(w)}function g(){if(n){var t,r,e=n.length;for(l=new Array(e),t=0;t<e;++t)r=n[t],l[r.index]=+h(r,t,n)}}function M(n){var t,r,e,u,o,f,a=0,c=0,h=n.length;if(h){for(e=u=o=f=0;f<h;++f)(t=n[f])&&(r=Math.abs(t.value))&&(a+=t.value,c+=r,e+=r*(t.x||0),u+=r*(t.y||0),o+=r*(t.z||0));a*=Math.sqrt(4/h),n.x=e/c,i>1&&(n.y=u/c),i>2&&(n.z=o/c)}else{(t=n).x=t.data.x,i>1&&(t.y=t.data.y),i>2&&(t.z=t.data.z);do{a+=l[t.data.index]}while(t=t.next)}n.value=a}function w(n,t,r,e,o){if(!n.value)return!0;var h=[r,e,o][i-1],s=n.x-u.x,g=i>1?n.y-u.y:0,x=i>2?n.z-u.z:0,z=h-t,p=s*s+g*g+x*x;if(z*z/y<p)return p<d&&(0===s&&(p+=(s=f(a))*s),i>1&&0===g&&(p+=(g=f(a))*g),i>2&&0===x&&(p+=(x=f(a))*x),p<v&&(p=Math.sqrt(v*p)),u.vx+=s*n.value*c/p,i>1&&(u.vy+=g*n.value*c/p),i>2&&(u.vz+=x*n.value*c/p)),!0;if(!(n.length||p>=d)){(n.data!==u||n.next)&&(0===s&&(p+=(s=f(a))*s),i>1&&0===g&&(p+=(g=f(a))*g),i>2&&0===x&&(p+=(x=f(a))*x),p<v&&(p=Math.sqrt(v*p)));do{n.data!==u&&(z=l[n.data.index]*c/p,u.vx+=s*z,i>1&&(u.vy+=g*z),i>2&&(u.vz+=x*z))}while(n=n.next)}}return s.initialize=function(t,...r){n=t,a=r.find((n=>"function"==typeof n))||Math.random,i=r.find((n=>[1,2,3].includes(n)))||2,g()},s.strength=function(n){return arguments.length?(h="function"==typeof n?n:o(+n),g(),s):h},s.distanceMin=function(n){return arguments.length?(v=n*n,s):Math.sqrt(v)},s.distanceMax=function(n){return arguments.length?(d=n*n,s):Math.sqrt(d)},s.theta=function(n){return arguments.length?(y=n*n,s):Math.sqrt(y)},s},n.forceRadial=function(n,t,r,e){var i,u,f,a,c=o(.1);function l(n){for(var o=0,c=i.length;o<c;++o){var l=i[o],h=l.x-t||1e-6,v=(l.y||0)-r||1e-6,d=(l.z||0)-e||1e-6,y=Math.sqrt(h*h+v*v+d*d),s=(a[o]-y)*f[o]*n/y;l.vx+=h*s,u>1&&(l.vy+=v*s),u>2&&(l.vz+=d*s)}}function h(){if(i){var t,r=i.length;for(f=new Array(r),a=new Array(r),t=0;t<r;++t)a[t]=+n(i[t],t,i),f[t]=isNaN(a[t])?0:+c(i[t],t,i)}}return"function"!=typeof n&&(n=o(+n)),null==t&&(t=0),null==r&&(r=0),null==e&&(e=0),l.initialize=function(n,...t){i=n,u=t.find((n=>[1,2,3].includes(n)))||2,h()},l.strength=function(n){return arguments.length?(c="function"==typeof n?n:o(+n),h(),l):c},l.radius=function(t){return arguments.length?(n="function"==typeof t?t:o(+t),h(),l):n},l.x=function(n){return arguments.length?(t=+n,l):t},l.y=function(n){return arguments.length?(r=+n,l):r},l.z=function(n){return arguments.length?(e=+n,l):e},l},n.forceSimulation=function(n,t){t=t||2;var r,e=Math.min(g,Math.max(1,Math.round(t))),o=1,f=.001,a=1-Math.pow(f,1/300),c=0,l=.6,h=new Map,v=u.timer(p),x=i.dispatch("tick","end"),z=function(){let n=1;return()=>(n=(d*n+y)%s)/s}();function p(){N(),x.call("tick",r),o<f&&(v.stop(),x.call("end",r))}function N(t){var i,u,f=n.length;void 0===t&&(t=1);for(var v=0;v<t;++v)for(o+=(c-o)*a,h.forEach((function(n){n(o)})),i=0;i<f;++i)null==(u=n[i]).fx?u.x+=u.vx*=l:(u.x=u.fx,u.vx=0),e>1&&(null==u.fy?u.y+=u.vy*=l:(u.y=u.fy,u.vy=0)),e>2&&(null==u.fz?u.z+=u.vz*=l:(u.z=u.fz,u.vz=0));return r}function m(){for(var t,r=0,i=n.length;r<i;++r){if((t=n[r]).index=r,null!=t.fx&&(t.x=t.fx),null!=t.fy&&(t.y=t.fy),null!=t.fz&&(t.z=t.fz),isNaN(t.x)||e>1&&isNaN(t.y)||e>2&&isNaN(t.z)){var u=M*(e>2?Math.cbrt(.5+r):e>1?Math.sqrt(.5+r):r),o=r*w,f=r*q;1===e?t.x=u:2===e?(t.x=u*Math.cos(o),t.y=u*Math.sin(o)):(t.x=u*Math.sin(o)*Math.cos(f),t.y=u*Math.cos(o),t.z=u*Math.sin(o)*Math.sin(f))}(isNaN(t.vx)||e>1&&isNaN(t.vy)||e>2&&isNaN(t.vz))&&(t.vx=0,e>1&&(t.vy=0),e>2&&(t.vz=0))}}function A(t){return t.initialize&&t.initialize(n,z,e),t}return null==n&&(n=[]),m(),r={tick:N,restart:function(){return v.restart(p),r},stop:function(){return v.stop(),r},numDimensions:function(n){return arguments.length?(e=Math.min(g,Math.max(1,Math.round(n))),h.forEach(A),r):e},nodes:function(t){return arguments.length?(n=t,m(),h.forEach(A),r):n},alpha:function(n){return arguments.length?(o=+n,r):o},alphaMin:function(n){return arguments.length?(f=+n,r):f},alphaDecay:function(n){return arguments.length?(a=+n,r):+a},alphaTarget:function(n){return arguments.length?(c=+n,r):c},velocityDecay:function(n){return arguments.length?(l=1-n,r):1-l},randomSource:function(n){return arguments.length?(z=n,h.forEach(A),r):z},force:function(n,t){return arguments.length>1?(null==t?h.delete(n):h.set(n,A(t)),r):h.get(n)},find:function(){var t,r,i,u,o,f,a=Array.prototype.slice.call(arguments),c=a.shift()||0,l=(e>1?a.shift():null)||0,h=(e>2?a.shift():null)||0,v=a.shift()||1/0,d=0,y=n.length;for(v*=v,d=0;d<y;++d)(u=(t=c-(o=n[d]).x)*t+(r=l-(o.y||0))*r+(i=h-(o.z||0))*i)<v&&(f=o,v=u);return f},on:function(n,t){return arguments.length>1?(x.on(n,t),r):x.on(n)}}},n.forceX=function(n){var t,r,e,i=o(.1);function u(n){for(var i,u=0,o=t.length;u<o;++u)(i=t[u]).vx+=(e[u]-i.x)*r[u]*n}function f(){if(t){var u,o=t.length;for(r=new Array(o),e=new Array(o),u=0;u<o;++u)r[u]=isNaN(e[u]=+n(t[u],u,t))?0:+i(t[u],u,t)}}return"function"!=typeof n&&(n=o(null==n?0:+n)),u.initialize=function(n){t=n,f()},u.strength=function(n){return arguments.length?(i="function"==typeof n?n:o(+n),f(),u):i},u.x=function(t){return arguments.length?(n="function"==typeof t?t:o(+t),f(),u):n},u},n.forceY=function(n){var t,r,e,i=o(.1);function u(n){for(var i,u=0,o=t.length;u<o;++u)(i=t[u]).vy+=(e[u]-i.y)*r[u]*n}function f(){if(t){var u,o=t.length;for(r=new Array(o),e=new Array(o),u=0;u<o;++u)r[u]=isNaN(e[u]=+n(t[u],u,t))?0:+i(t[u],u,t)}}return"function"!=typeof n&&(n=o(null==n?0:+n)),u.initialize=function(n){t=n,f()},u.strength=function(n){return arguments.length?(i="function"==typeof n?n:o(+n),f(),u):i},u.y=function(t){return arguments.length?(n="function"==typeof t?t:o(+t),f(),u):n},u},n.forceZ=function(n){var t,r,e,i=o(.1);function u(n){for(var i,u=0,o=t.length;u<o;++u)(i=t[u]).vz+=(e[u]-i.z)*r[u]*n}function f(){if(t){var u,o=t.length;for(r=new Array(o),e=new Array(o),u=0;u<o;++u)r[u]=isNaN(e[u]=+n(t[u],u,t))?0:+i(t[u],u,t)}}return"function"!=typeof n&&(n=o(null==n?0:+n)),u.initialize=function(n){t=n,f()},u.strength=function(n){return arguments.length?(i="function"==typeof n?n:o(+n),f(),u):i},u.z=function(t){return arguments.length?(n="function"==typeof t?t:o(+t),f(),u):n},u}}));
