// Version 1.28.0 react-force-graph-2d - https://github.com/vasturiano/react-force-graph
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n(require("react")):"function"==typeof define&&define.amd?define(["react"],n):(t="undefined"!=typeof globalThis?globalThis:t||self).ForceGraph2D=n(t.React)}(this,function(n){"use strict";function e(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:String(n)}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function r(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;s=!1}else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||o(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t){return function(t){if(Array.isArray(t))return a(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||o(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,n){if(t){if("string"==typeof t)return a(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?a(t,n):void 0}}function a(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function u(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function s(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||c(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,n){if(t){if("string"==typeof t)return u(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?u(t,n):void 0}}function f(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n.useEffect,r=n.useRef(),i=n.useRef(!1),o=n.useRef(!1),a=s(n.useState(0),2);a[0];var u=a[1];i.current&&(o.current=!0),e(function(){return i.current||(r.current=t(),i.current=!0),u(function(t){return t+1}),function(){o.current&&r.current&&r.current()}},[])}var h="http://www.w3.org/1999/xhtml",p={svg:"http://www.w3.org/2000/svg",xhtml:h,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function d(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),p.hasOwnProperty(n)?{space:p[n],local:t}:t}function y(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===h&&n.documentElement.namespaceURI===h?n.createElement(t):n.createElementNS(e,t)}}function g(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function v(t){var n=d(t);return(n.local?g:y)(n)}function _(){}function m(t){return null==t?_:function(){return this.querySelector(t)}}function b(){return[]}function x(t){return null==t?b:function(){return this.querySelectorAll(t)}}function w(t){return function(){return function(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}(t.apply(this,arguments))}}function k(t){return function(){return this.matches(t)}}function S(t){return function(n){return n.matches(t)}}var A=Array.prototype.find;function M(){return this.firstElementChild}var O=Array.prototype.filter;function z(){return Array.from(this.children)}function E(t){return new Array(t.length)}function C(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function P(t,n,e,r,i,o){for(var a,u=0,s=n.length,l=o.length;u<l;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new C(t,o[u]);for(;u<s;++u)(a=n[u])&&(i[u]=a)}function j(t,n,e,r,i,o,a){var u,s,l,c=new Map,f=n.length,h=o.length,p=new Array(f);for(u=0;u<f;++u)(s=n[u])&&(p[u]=l=a.call(s,s.__data__,u,n)+"",c.has(l)?i[u]=s:c.set(l,s));for(u=0;u<h;++u)l=a.call(t,o[u],u,o)+"",(s=c.get(l))?(r[u]=s,s.__data__=o[u],c.delete(l)):e[u]=new C(t,o[u]);for(u=0;u<f;++u)(s=n[u])&&c.get(p[u])===s&&(i[u]=s)}function T(t){return t.__data__}function N(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function R(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function D(t){return function(){this.removeAttribute(t)}}function I(t){return function(){this.removeAttributeNS(t.space,t.local)}}function U(t,n){return function(){this.setAttribute(t,n)}}function F(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function L(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function $(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}function q(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function B(t){return function(){this.style.removeProperty(t)}}function H(t,n,e){return function(){this.style.setProperty(t,n,e)}}function V(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function G(t,n){return t.style.getPropertyValue(n)||q(t).getComputedStyle(t,null).getPropertyValue(n)}function X(t){return function(){delete this[t]}}function W(t,n){return function(){this[t]=n}}function Y(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function Z(t){return t.trim().split(/^|\s+/)}function Q(t){return t.classList||new J(t)}function J(t){this._node=t,this._names=Z(t.getAttribute("class")||"")}function K(t,n){for(var e=Q(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function tt(t,n){for(var e=Q(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function nt(t){return function(){K(this,t)}}function et(t){return function(){tt(this,t)}}function rt(t,n){return function(){(n.apply(this,arguments)?K:tt)(this,t)}}function it(){this.textContent=""}function ot(t){return function(){this.textContent=t}}function at(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}function ut(){this.innerHTML=""}function st(t){return function(){this.innerHTML=t}}function lt(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}function ct(){this.nextSibling&&this.parentNode.appendChild(this)}function ft(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ht(){return null}function pt(){var t=this.parentNode;t&&t.removeChild(this)}function dt(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function yt(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function gt(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function vt(t,n,e){return function(){var r,i=this.__on,o=function(t){return function(n){t.call(this,n,this.__data__)}}(n);if(i)for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),void(r.value=n);this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function _t(t,n,e){var r=q(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}function mt(t,n){return function(){return _t(this,t,n)}}function bt(t,n){return function(){return _t(this,t,n.apply(this,arguments))}}C.prototype={constructor:C,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},J.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var xt=[null];function wt(t,n){this._groups=t,this._parents=n}function kt(){return new wt([[document.documentElement]],xt)}function St(t){return"string"==typeof t?new wt([[document.querySelector(t)]],[document.documentElement]):new wt([[t]],xt)}function At(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}wt.prototype=kt.prototype={constructor:wt,select:function(t){"function"!=typeof t&&(t=m(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a,u=n[i],s=u.length,l=r[i]=new Array(s),c=0;c<s;++c)(o=u[c])&&(a=t.call(o,o.__data__,c,u))&&("__data__"in o&&(a.__data__=o.__data__),l[c]=a);return new wt(r,this._parents)},selectAll:function(t){t="function"==typeof t?w(t):x(t);for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var a,u=n[o],s=u.length,l=0;l<s;++l)(a=u[l])&&(r.push(t.call(a,a.__data__,l,u)),i.push(a));return new wt(r,i)},selectChild:function(t){return this.select(null==t?M:function(t){return function(){return A.call(this.children,t)}}("function"==typeof t?t:S(t)))},selectChildren:function(t){return this.selectAll(null==t?z:function(t){return function(){return O.call(this.children,t)}}("function"==typeof t?t:S(t)))},filter:function(t){"function"!=typeof t&&(t=k(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new wt(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,T);var e=n?j:P,r=this._parents,i=this._groups;"function"!=typeof t&&(t=function(t){return function(){return t}}(t));for(var o=i.length,a=new Array(o),u=new Array(o),s=new Array(o),l=0;l<o;++l){var c=r[l],f=i[l],h=f.length,p=N(t.call(c,c&&c.__data__,l,r)),d=p.length,y=u[l]=new Array(d),g=a[l]=new Array(d);e(c,f,y,g,s[l]=new Array(h),p,n);for(var v,_,m=0,b=0;m<d;++m)if(v=y[m]){for(m>=b&&(b=m+1);!(_=g[b])&&++b<d;);v._next=_||null}}return(a=new wt(a,r))._enter=u,a._exit=s,a},enter:function(){return new wt(this._enter||this._groups.map(E),this._parents)},exit:function(){return new wt(this._exit||this._groups.map(E),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,a=Math.min(i,o),u=new Array(i),s=0;s<a;++s)for(var l,c=e[s],f=r[s],h=c.length,p=u[s]=new Array(h),d=0;d<h;++d)(l=c[d]||f[d])&&(p[d]=l);for(;s<i;++s)u[s]=e[s];return new wt(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=R);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var a,u=e[o],s=u.length,l=i[o]=new Array(s),c=0;c<s;++c)(a=u[c])&&(l[c]=a);l.sort(n)}return new wt(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(const n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,n){var e=d(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?I:D:"function"==typeof n?e.local?$:L:e.local?F:U)(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?B:"function"==typeof n?V:H)(t,n,null==e?"":e)):G(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?X:"function"==typeof n?Y:W)(t,n)):this.node()[t]},classed:function(t,n){var e=Z(t+"");if(arguments.length<2){for(var r=Q(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?rt:n?nt:et)(e,n))},text:function(t){return arguments.length?this.each(null==t?it:("function"==typeof t?at:ot)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?ut:("function"==typeof t?lt:st)(t)):this.node().innerHTML},raise:function(){return this.each(ct)},lower:function(){return this.each(ft)},append:function(t){var n="function"==typeof t?t:v(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:v(t),r=null==n?ht:"function"==typeof n?n:m(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(pt)},clone:function(t){return this.select(t?yt:dt)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=function(t){return t.trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}})}(t+""),a=o.length;if(!(arguments.length<2)){for(u=n?vt:gt,r=0;r<a;++r)this.each(u(o[r],n,e));return this}var u=this.node().__on;if(u)for(var s,l=0,c=u.length;l<c;++l)for(r=0,s=u[l];r<a;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value},dispatch:function(t,n){return this.each(("function"==typeof n?bt:mt)(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};var Mt={value:()=>{}};function Ot(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new zt(r)}function zt(t){this._=t}function Et(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}function Ct(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=Mt,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}zt.prototype=Ot.prototype={constructor:zt,on:function(t,n){var e,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}})),a=-1,u=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++a<u;)if(e=(t=o[a]).type)i[e]=Ct(i[e],t.name,n);else if(null==n)for(e in i)i[e]=Ct(i[e],t.name,null);return this}for(;++a<u;)if((e=(t=o[a]).type)&&(e=Et(i[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new zt(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};const Pt={passive:!1},jt={capture:!0,passive:!1};function Tt(t){t.stopImmediatePropagation()}function Nt(t){t.preventDefault(),t.stopImmediatePropagation()}function Rt(t){var n=t.document.documentElement,e=St(t).on("dragstart.drag",Nt,jt);"onselectstart"in n?e.on("selectstart.drag",Nt,jt):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function Dt(t,n){var e=t.document.documentElement,r=St(t).on("dragstart.drag",null);n&&(r.on("click.drag",Nt,jt),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}var It=t=>()=>t;function Ut(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:a,y:u,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function Ft(t){return!t.ctrlKey&&!t.button}function Lt(){return this.parentNode}function $t(t,n){return null==n?{x:t.x,y:t.y}:n}function qt(){return navigator.maxTouchPoints||"ontouchstart"in this}function Bt(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function Ht(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function Vt(){}Ut.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var Gt=.7,Xt=1/Gt,Wt="\\s*([+-]?\\d+)\\s*",Yt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Zt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Qt=/^#([0-9a-f]{3,8})$/,Jt=new RegExp(`^rgb\\(${Wt},${Wt},${Wt}\\)$`),Kt=new RegExp(`^rgb\\(${Zt},${Zt},${Zt}\\)$`),tn=new RegExp(`^rgba\\(${Wt},${Wt},${Wt},${Yt}\\)$`),nn=new RegExp(`^rgba\\(${Zt},${Zt},${Zt},${Yt}\\)$`),en=new RegExp(`^hsl\\(${Yt},${Zt},${Zt}\\)$`),rn=new RegExp(`^hsla\\(${Yt},${Zt},${Zt},${Yt}\\)$`),on={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function an(){return this.rgb().formatHex()}function un(){return this.rgb().formatRgb()}function sn(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=Qt.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?ln(n):3===e?new hn(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?cn(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?cn(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=Jt.exec(t))?new hn(n[1],n[2],n[3],1):(n=Kt.exec(t))?new hn(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=tn.exec(t))?cn(n[1],n[2],n[3],n[4]):(n=nn.exec(t))?cn(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=en.exec(t))?_n(n[1],n[2]/100,n[3]/100,1):(n=rn.exec(t))?_n(n[1],n[2]/100,n[3]/100,n[4]):on.hasOwnProperty(t)?ln(on[t]):"transparent"===t?new hn(NaN,NaN,NaN,0):null}function ln(t){return new hn(t>>16&255,t>>8&255,255&t,1)}function cn(t,n,e,r){return r<=0&&(t=n=e=NaN),new hn(t,n,e,r)}function fn(t,n,e,r){return 1===arguments.length?function(t){return t instanceof Vt||(t=sn(t)),t?new hn((t=t.rgb()).r,t.g,t.b,t.opacity):new hn}(t):new hn(t,n,e,null==r?1:r)}function hn(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function pn(){return`#${vn(this.r)}${vn(this.g)}${vn(this.b)}`}function dn(){const t=yn(this.opacity);return`${1===t?"rgb(":"rgba("}${gn(this.r)}, ${gn(this.g)}, ${gn(this.b)}${1===t?")":`, ${t})`}`}function yn(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function gn(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function vn(t){return((t=gn(t))<16?"0":"")+t.toString(16)}function _n(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new bn(t,n,e,r)}function mn(t){if(t instanceof bn)return new bn(t.h,t.s,t.l,t.opacity);if(t instanceof Vt||(t=sn(t)),!t)return new bn;if(t instanceof bn)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,u=o-i,s=(o+i)/2;return u?(a=n===o?(e-r)/u+6*(e<r):e===o?(r-n)/u+2:(n-e)/u+4,u/=s<.5?o+i:2-o-i,a*=60):u=s>0&&s<1?0:a,new bn(a,u,s,t.opacity)}function bn(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function xn(t){return(t=(t||0)%360)<0?t+360:t}function wn(t){return Math.max(0,Math.min(1,t||0))}function kn(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}Bt(Vt,sn,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:an,formatHex:an,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return mn(this).formatHsl()},formatRgb:un,toString:un}),Bt(hn,fn,Ht(Vt,{brighter(t){return t=null==t?Xt:Math.pow(Xt,t),new hn(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Gt:Math.pow(Gt,t),new hn(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new hn(gn(this.r),gn(this.g),gn(this.b),yn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:pn,formatHex:pn,formatHex8:function(){return`#${vn(this.r)}${vn(this.g)}${vn(this.b)}${vn(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:dn,toString:dn})),Bt(bn,function(t,n,e,r){return 1===arguments.length?mn(t):new bn(t,n,e,null==r?1:r)},Ht(Vt,{brighter(t){return t=null==t?Xt:Math.pow(Xt,t),new bn(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Gt:Math.pow(Gt,t),new bn(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new hn(kn(t>=240?t-240:t+120,i,r),kn(t,i,r),kn(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new bn(xn(this.h),wn(this.s),wn(this.l),yn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=yn(this.opacity);return`${1===t?"hsl(":"hsla("}${xn(this.h)}, ${100*wn(this.s)}%, ${100*wn(this.l)}%${1===t?")":`, ${t})`}`}}));var Sn=t=>()=>t;function An(t){return 1===(t=+t)?Mn:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):Sn(isNaN(n)?e:n)}}function Mn(t,n){var e=n-t;return e?function(t,n){return function(e){return t+e*n}}(t,e):Sn(isNaN(t)?n:t)}var On=function t(n){var e=An(n);function r(t,n){var r=e((t=fn(t)).r,(n=fn(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),a=Mn(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return r.gamma=t,r}(1);function zn(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}var En=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Cn=new RegExp(En.source,"g");function Pn(t,n){var e,r,i,o=En.lastIndex=Cn.lastIndex=0,a=-1,u=[],s=[];for(t+="",n+="";(e=En.exec(t))&&(r=Cn.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),u[a]?u[a]+=i:u[++a]=i),(e=e[0])===(r=r[0])?u[a]?u[a]+=r:u[++a]=r:(u[++a]=null,s.push({i:a,x:zn(e,r)})),o=Cn.lastIndex;return o<n.length&&(i=n.slice(o),u[a]?u[a]+=i:u[++a]=i),u.length<2?s[0]?function(t){return function(n){return t(n)+""}}(s[0].x):function(t){return function(){return t}}(n):(n=s.length,function(t){for(var e,r=0;r<n;++r)u[(e=s[r]).i]=e.x(t);return u.join("")})}var jn,Tn=180/Math.PI,Nn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Rn(t,n,e,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(s=t*e+n*r)&&(e-=t*s,r-=n*s),(u=Math.sqrt(e*e+r*r))&&(e/=u,r/=u,s/=u),t*r<n*e&&(t=-t,n=-n,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*Tn,skewX:Math.atan(s)*Tn,scaleX:a,scaleY:u}}function Dn(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u=[],s=[];return o=t(o),a=t(a),function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,n,null,e);u.push({i:s-4,x:zn(t,i)},{i:s-2,x:zn(r,o)})}else(i||o)&&a.push("translate("+i+n+o+e)}(o.translateX,o.translateY,a.translateX,a.translateY,u,s),function(t,n,e,o){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),o.push({i:e.push(i(e)+"rotate(",null,r)-2,x:zn(t,n)})):n&&e.push(i(e)+"rotate("+n+r)}(o.rotate,a.rotate,u,s),function(t,n,e,o){t!==n?o.push({i:e.push(i(e)+"skewX(",null,r)-2,x:zn(t,n)}):n&&e.push(i(e)+"skewX("+n+r)}(o.skewX,a.skewX,u,s),function(t,n,e,r,o,a){if(t!==e||n!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:zn(t,e)},{i:u-2,x:zn(n,r)})}else 1===e&&1===r||o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,u,s),o=a=null,function(t){for(var n,e=-1,r=s.length;++e<r;)u[(n=s[e]).i]=n.x(t);return u.join("")}}}var In=Dn(function(t){const n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?Nn:Rn(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),Un=Dn(function(t){return null==t?Nn:(jn||(jn=document.createElementNS("http://www.w3.org/2000/svg","g")),jn.setAttribute("transform",t),(t=jn.transform.baseVal.consolidate())?Rn((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):Nn)},", ",")",")");function Fn(t){return((t=Math.exp(t))+1/t)/2}var Ln,$n,qn=function t(n,e,r){function i(t,i){var o,a,u=t[0],s=t[1],l=t[2],c=i[0],f=i[1],h=i[2],p=c-u,d=f-s,y=p*p+d*d;if(y<1e-12)a=Math.log(h/l)/n,o=function(t){return[u+t*p,s+t*d,l*Math.exp(n*t*a)]};else{var g=Math.sqrt(y),v=(h*h-l*l+r*y)/(2*l*e*g),_=(h*h-l*l-r*y)/(2*h*e*g),m=Math.log(Math.sqrt(v*v+1)-v),b=Math.log(Math.sqrt(_*_+1)-_);a=(b-m)/n,o=function(t){var r=t*a,i=Fn(m),o=l/(e*g)*(i*function(t){return((t=Math.exp(2*t))-1)/(t+1)}(n*r+m)-function(t){return((t=Math.exp(t))-1/t)/2}(m));return[u+o*p,s+o*d,l*i/Fn(n*r+m)]}}return o.duration=1e3*a*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4),Bn=0,Hn=0,Vn=0,Gn=0,Xn=0,Wn=0,Yn="object"==typeof performance&&performance.now?performance:Date,Zn="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Qn(){return Xn||(Zn(Jn),Xn=Yn.now()+Wn)}function Jn(){Xn=0}function Kn(){this._call=this._time=this._next=null}function te(t,n,e){var r=new Kn;return r.restart(t,n,e),r}function ne(){Xn=(Gn=Yn.now())+Wn,Bn=Hn=0;try{!function(){Qn(),++Bn;for(var t,n=Ln;n;)(t=Xn-n._time)>=0&&n._call.call(void 0,t),n=n._next;--Bn}()}finally{Bn=0,function(){var t,n,e=Ln,r=1/0;for(;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:Ln=n);$n=t,re(r)}(),Xn=0}}function ee(){var t=Yn.now(),n=t-Gn;n>1e3&&(Wn-=n,Gn=t)}function re(t){Bn||(Hn&&(Hn=clearTimeout(Hn)),t-Xn>24?(t<1/0&&(Hn=setTimeout(ne,t-Yn.now()-Wn)),Vn&&(Vn=clearInterval(Vn))):(Vn||(Gn=Yn.now(),Vn=setInterval(ee,1e3)),Bn=1,Zn(ne)))}function ie(t,n,e){var r=new Kn;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}Kn.prototype=te.prototype={constructor:Kn,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?Qn():+e)+(null==n?0:+n),this._next||$n===this||($n?$n._next=this:Ln=this,$n=this),this._call=t,this._time=e,re()},stop:function(){this._call&&(this._call=null,this._time=1/0,re())}};var oe=Ot("start","end","cancel","interrupt"),ae=[];function ue(t,n,e,r,i,o){var a=t.__transition;if(a){if(e in a)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(t){e.state=1,e.timer.restart(a,e.delay,e.time),e.delay<=t&&a(t-e.delay)}function a(o){var l,c,f,h;if(1!==e.state)return s();for(l in i)if((h=i[l]).name===e.name){if(3===h.state)return ie(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[l]):+l<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[l])}if(ie(function(){3===e.state&&(e.state=4,e.timer.restart(u,e.delay,e.time),u(o))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(e.state=3,r=new Array(f=e.tween.length),l=0,c=-1;l<f;++l)(h=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++c]=h);r.length=c+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(s),e.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),s())}function s(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=te(o,0,e.time)}(t,e,{name:n,index:r,group:i,on:oe,tween:ae,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function se(t,n){var e=ce(t,n);if(e.state>0)throw new Error("too late; already scheduled");return e}function le(t,n){var e=ce(t,n);if(e.state>3)throw new Error("too late; already running");return e}function ce(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function fe(t,n){var e,r,i,o=t.__transition,a=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name===n?(r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]):a=!1;a&&delete t.__transition}}function he(t,n){var e,r;return function(){var i=le(this,t),o=i.tween;if(o!==e)for(var a=0,u=(r=e=o).length;a<u;++a)if(r[a].name===n){(r=r.slice()).splice(a,1);break}i.tween=r}}function pe(t,n,e){var r,i;if("function"!=typeof e)throw new Error;return function(){var o=le(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=u;break}s===l&&i.push(u)}o.tween=i}}function de(t,n,e){var r=t._id;return t.each(function(){var t=le(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return ce(t,r).value[n]}}function ye(t,n){var e;return("number"==typeof n?zn:n instanceof sn?On:(e=sn(n))?(n=e,On):Pn)(t,n)}function ge(t){return function(){this.removeAttribute(t)}}function ve(t){return function(){this.removeAttributeNS(t.space,t.local)}}function _e(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=n(r=a,e)}}function me(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=n(r=a,e)}}function be(t,n,e){var r,i,o;return function(){var a,u,s=e(this);if(null!=s)return(a=this.getAttribute(t))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s));this.removeAttribute(t)}}function xe(t,n,e){var r,i,o;return function(){var a,u,s=e(this);if(null!=s)return(a=this.getAttributeNS(t.space,t.local))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s));this.removeAttributeNS(t.space,t.local)}}function we(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}(t,i)),e}return i._value=n,i}function ke(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}(t,i)),e}return i._value=n,i}function Se(t,n){return function(){se(this,t).delay=+n.apply(this,arguments)}}function Ae(t,n){return n=+n,function(){se(this,t).delay=n}}function Me(t,n){return function(){le(this,t).duration=+n.apply(this,arguments)}}function Oe(t,n){return n=+n,function(){le(this,t).duration=n}}var ze=kt.prototype.constructor;function Ee(t){return function(){this.style.removeProperty(t)}}var Ce=0;function Pe(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function je(){return++Ce}var Te=kt.prototype;Pe.prototype={constructor:Pe,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=m(t));for(var r=this._groups,i=r.length,o=new Array(i),a=0;a<i;++a)for(var u,s,l=r[a],c=l.length,f=o[a]=new Array(c),h=0;h<c;++h)(u=l[h])&&(s=t.call(u,u.__data__,h,l))&&("__data__"in u&&(s.__data__=u.__data__),f[h]=s,ue(f[h],n,e,h,f,ce(u,e)));return new Pe(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=x(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var s,l=r[u],c=l.length,f=0;f<c;++f)if(s=l[f]){for(var h,p=t.call(s,s.__data__,f,l),d=ce(s,e),y=0,g=p.length;y<g;++y)(h=p[y])&&ue(h,n,e,y,p,d);o.push(p),a.push(s)}return new Pe(o,a,n,e)},selectChild:Te.selectChild,selectChildren:Te.selectChildren,filter:function(t){"function"!=typeof t&&(t=k(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new Pe(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),a=new Array(r),u=0;u<o;++u)for(var s,l=n[u],c=e[u],f=l.length,h=a[u]=new Array(f),p=0;p<f;++p)(s=l[p]||c[p])&&(h[p]=s);for(;u<r;++u)a[u]=n[u];return new Pe(a,this._parents,this._name,this._id)},selection:function(){return new ze(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=je(),r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)if(a=u[l]){var c=ce(a,n);ue(a,t,e,l,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new Pe(r,this._parents,t,e)},call:Te.call,nodes:Te.nodes,node:Te.node,size:Te.size,empty:Te.empty,each:Te.each,on:function(t,n){var e=this._id;return arguments.length<2?ce(this.node(),e).on.on(t):this.each(function(t,n,e){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})}(n)?se:le;return function(){var a=o(this,t),u=a.on;u!==r&&(i=(r=u).copy()).on(n,e),a.on=i}}(e,t,n))},attr:function(t,n){var e=d(t),r="transform"===e?Un:ye;return this.attrTween(t,"function"==typeof n?(e.local?xe:be)(e,r,de(this,"attr."+t,n)):null==n?(e.local?ve:ge)(e):(e.local?me:_e)(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var r=d(t);return this.tween(e,(r.local?we:ke)(r,n))},style:function(t,n,e){var r="transform"==(t+="")?In:ye;return null==n?this.styleTween(t,function(t,n){var e,r,i;return function(){var o=G(this,t),a=(this.style.removeProperty(t),G(this,t));return o===a?null:o===e&&a===r?i:i=n(e=o,r=a)}}(t,r)).on("end.style."+t,Ee(t)):"function"==typeof n?this.styleTween(t,function(t,n,e){var r,i,o;return function(){var a=G(this,t),u=e(this),s=u+"";return null==u&&(this.style.removeProperty(t),s=u=G(this,t)),a===s?null:a===r&&s===i?o:(i=s,o=n(r=a,u))}}(t,r,de(this,"style."+t,n))).each(function(t,n){var e,r,i,o,a="style."+n,u="end."+a;return function(){var s=le(this,t),l=s.on,c=null==s.value[a]?o||(o=Ee(n)):void 0;l===e&&i===c||(r=(e=l).copy()).on(u,i=c),s.on=r}}(this._id,t)):this.styleTween(t,function(t,n,e){var r,i,o=e+"";return function(){var a=G(this,t);return a===o?null:a===r?i:i=n(r=a,e)}}(t,r,n),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}(t,o,e)),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(de(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){return function(n){this.textContent=t.call(this,n)}}(r)),n}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}(this._id))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=ce(this.node(),e).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?he:pe)(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?Se:Ae)(n,t)):ce(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?Me:Oe)(n,t)):ce(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw new Error;return function(){le(this,t).ease=n}}(n,t)):ce(this.node(),n).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,n){return function(){var e=n.apply(this,arguments);if("function"!=typeof e)throw new Error;le(this,t).ease=e}}(this._id,t))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,a){var u={value:a},s={value:function(){0===--i&&o()}};e.each(function(){var e=le(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(u),n._.interrupt.push(u),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:Te[Symbol.iterator]};var Ne={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function Re(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}kt.prototype.interrupt=function(t){return this.each(function(){fe(this,t)})},kt.prototype.transition=function(t){var n,e;t instanceof Pe?(n=t._id,t=t._name):(n=je(),(e=Ne).time=Qn(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)(a=u[l])&&ue(a,t,n,l,u,e||Re(a,n));return new Pe(r,this._parents,t,n)};var De=t=>()=>t;function Ie(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function Ue(t,n,e){this.k=t,this.x=n,this.y=e}Ue.prototype={constructor:Ue,scale:function(t){return 1===t?this:new Ue(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new Ue(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Fe=new Ue(1,0,0);function Le(t){for(;!t.__zoom;)if(!(t=t.parentNode))return Fe;return t.__zoom}function $e(t){t.stopImmediatePropagation()}function qe(t){t.preventDefault(),t.stopImmediatePropagation()}function Be(t){return!(t.ctrlKey&&"wheel"!==t.type||t.button)}function He(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function Ve(){return this.__zoom||Fe}function Ge(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function Xe(){return navigator.maxTouchPoints||"ontouchstart"in this}function We(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],a=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function Ye(){var t,n,e,r=Be,i=He,o=We,a=Ge,u=Xe,s=[0,1/0],l=[[-1/0,-1/0],[1/0,1/0]],c=250,f=qn,h=Ot("start","zoom","end"),p=0,d=10;function y(t){t.property("__zoom",Ve).on("wheel.zoom",w,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",S).filter(u).on("touchstart.zoom",A).on("touchmove.zoom",M).on("touchend.zoom touchcancel.zoom",O).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(t,n){return(n=Math.max(s[0],Math.min(s[1],n)))===t.k?t:new Ue(n,t.x,t.y)}function v(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new Ue(t.k,r,i)}function _(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function m(t,n,e,r){t.on("start.zoom",function(){b(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(r).end()}).tween("zoom",function(){var t=this,o=arguments,a=b(t,o).event(r),u=i.apply(t,o),s=null==e?_(u):"function"==typeof e?e.apply(t,o):e,l=Math.max(u[1][0]-u[0][0],u[1][1]-u[0][1]),c=t.__zoom,h="function"==typeof n?n.apply(t,o):n,p=f(c.invert(s).concat(l/c.k),h.invert(s).concat(l/h.k));return function(t){if(1===t)t=h;else{var n=p(t),e=l/n[2];t=new Ue(e,s[0]-n[0]*e,s[1]-n[1]*e)}a.zoom(null,t)}})}function b(t,n,e){return!e&&t.__zooming||new x(t,n)}function x(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function w(t,...n){if(r.apply(this,arguments)){var e=b(this,n).event(t),i=this.__zoom,u=Math.max(s[0],Math.min(s[1],i.k*Math.pow(2,a.apply(this,arguments)))),c=At(t);if(e.wheel)e.mouse[0][0]===c[0]&&e.mouse[0][1]===c[1]||(e.mouse[1]=i.invert(e.mouse[0]=c)),clearTimeout(e.wheel);else{if(i.k===u)return;e.mouse=[c,i.invert(c)],fe(this),e.start()}qe(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",o(v(g(i,u),e.mouse[0],e.mouse[1]),e.extent,l))}}function k(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,a=b(this,n,!0).event(t),u=St(t.view).on("mousemove.zoom",function(t){if(qe(t),!a.moved){var n=t.clientX-c,e=t.clientY-f;a.moved=n*n+e*e>p}a.event(t).zoom("mouse",o(v(a.that.__zoom,a.mouse[0]=At(t,i),a.mouse[1]),a.extent,l))},!0).on("mouseup.zoom",function(t){u.on("mousemove.zoom mouseup.zoom",null),Dt(t.view,a.moved),qe(t),a.event(t).end()},!0),s=At(t,i),c=t.clientX,f=t.clientY;Rt(t.view),$e(t),a.mouse=[s,this.__zoom.invert(s)],fe(this),a.start()}}function S(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,a=At(t.changedTouches?t.changedTouches[0]:t,this),u=e.invert(a),s=e.k*(t.shiftKey?.5:2),f=o(v(g(e,s),a,u),i.apply(this,n),l);qe(t),c>0?St(this).transition().duration(c).call(m,f,a,t):St(this).call(y.transform,f,a,t)}}function A(e,...i){if(r.apply(this,arguments)){var o,a,u,s,l=e.touches,c=l.length,f=b(this,i,e.changedTouches.length===c).event(e);for($e(e),a=0;a<c;++a)s=[s=At(u=l[a],this),this.__zoom.invert(s),u.identifier],f.touch0?f.touch1||f.touch0[2]===s[2]||(f.touch1=s,f.taps=0):(f.touch0=s,o=!0,f.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(f.taps<2&&(n=s[0],t=setTimeout(function(){t=null},500)),fe(this),f.start())}}function M(t,...n){if(this.__zooming){var e,r,i,a,u=b(this,n).event(t),s=t.changedTouches,c=s.length;for(qe(t),e=0;e<c;++e)i=At(r=s[e],this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var f=u.touch0[0],h=u.touch0[1],p=u.touch1[0],d=u.touch1[1],y=(y=p[0]-f[0])*y+(y=p[1]-f[1])*y,_=(_=d[0]-h[0])*_+(_=d[1]-h[1])*_;r=g(r,Math.sqrt(y/_)),i=[(f[0]+p[0])/2,(f[1]+p[1])/2],a=[(h[0]+d[0])/2,(h[1]+d[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],a=u.touch0[1]}u.zoom("touch",o(v(r,i,a),u.extent,l))}}function O(t,...r){if(this.__zooming){var i,o,a=b(this,r).event(t),u=t.changedTouches,s=u.length;for($e(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<s;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=At(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<d)){var l=St(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return y.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",Ve),t!==i?m(t,n,e,r):i.interrupt().each(function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},y.scaleBy=function(t,n,e,r){y.scaleTo(t,function(){return this.__zoom.k*("function"==typeof n?n.apply(this,arguments):n)},e,r)},y.scaleTo=function(t,n,e,r){y.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,a=null==e?_(t):"function"==typeof e?e.apply(this,arguments):e,u=r.invert(a),s="function"==typeof n?n.apply(this,arguments):n;return o(v(g(r,s),a,u),t,l)},e,r)},y.translateBy=function(t,n,e,r){y.transform(t,function(){return o(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),l)},null,r)},y.translateTo=function(t,n,e,r,a){y.transform(t,function(){var t=i.apply(this,arguments),a=this.__zoom,u=null==r?_(t):"function"==typeof r?r.apply(this,arguments):r;return o(Fe.translate(u[0],u[1]).scale(a.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,l)},r,a)},x.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1===++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0===--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=St(this.that).datum();h.call(t,this.that,new Ie(t,{sourceEvent:this.sourceEvent,target:y,transform:this.that.__zoom,dispatch:h}),n)}},y.wheelDelta=function(t){return arguments.length?(a="function"==typeof t?t:De(+t),y):a},y.filter=function(t){return arguments.length?(r="function"==typeof t?t:De(!!t),y):r},y.touchable=function(t){return arguments.length?(u="function"==typeof t?t:De(!!t),y):u},y.extent=function(t){return arguments.length?(i="function"==typeof t?t:De([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),y):i},y.scaleExtent=function(t){return arguments.length?(s[0]=+t[0],s[1]=+t[1],y):[s[0],s[1]]},y.translateExtent=function(t){return arguments.length?(l[0][0]=+t[0][0],l[1][0]=+t[1][0],l[0][1]=+t[0][1],l[1][1]=+t[1][1],y):[[l[0][0],l[0][1]],[l[1][0],l[1][1]]]},y.constrain=function(t){return arguments.length?(o=t,y):o},y.duration=function(t){return arguments.length?(c=+t,y):c},y.interpolate=function(t){return arguments.length?(f=t,y):f},y.on=function(){var t=h.on.apply(h,arguments);return t===h?y:t},y.clickDistance=function(t){return arguments.length?(p=(t=+t)*t,y):Math.sqrt(p)},y.tapDistance=function(t){return arguments.length?(d=+t,y):d},y}Le.prototype=Ue.prototype;class Ze extends Map{constructor(t,n=Je){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(const[n,e]of t)this.set(n,e)}get(t){return super.get(Qe(this,t))}has(t){return super.has(Qe(this,t))}set(t,n){return super.set(function({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}(this,t),n)}delete(t){return super.delete(function({_intern:t,_key:n},e){const r=n(e);t.has(r)&&(e=t.get(r),t.delete(r));return e}(this,t))}}function Qe({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):e}function Je(t){return null!==t&&"object"==typeof t?t.valueOf():t}function Ke(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e<i||void 0===e&&i>=i)&&(e=i)}return e}function tr(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e>i||void 0===e&&i>=i)&&(e=i)}return e}var nr="object"==typeof global&&global&&global.Object===Object&&global,er="object"==typeof self&&self&&self.Object===Object&&self,rr=nr||er||Function("return this")(),ir=rr.Symbol,or=Object.prototype,ar=or.hasOwnProperty,ur=or.toString,sr=ir?ir.toStringTag:void 0;var lr=Object.prototype.toString;var cr=ir?ir.toStringTag:void 0;function fr(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":cr&&cr in Object(t)?function(t){var n=ar.call(t,sr),e=t[sr];try{t[sr]=void 0;var r=!0}catch(t){}var i=ur.call(t);return r&&(n?t[sr]=e:delete t[sr]),i}(t):function(t){return lr.call(t)}(t)}var hr=/\s/;var pr=/^\s+/;function dr(t){return t?t.slice(0,function(t){for(var n=t.length;n--&&hr.test(t.charAt(n)););return n}(t)+1).replace(pr,""):t}function yr(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}var gr=/^[-+]0x[0-9a-f]+$/i,vr=/^0b[01]+$/i,_r=/^0o[0-7]+$/i,mr=parseInt;function br(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return null!=t&&"object"==typeof t}(t)&&"[object Symbol]"==fr(t)}(t))return NaN;if(yr(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=yr(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=dr(t);var e=vr.test(t);return e||_r.test(t)?mr(t.slice(2),e?2:8):gr.test(t)?NaN:+t}var xr=function(){return rr.Date.now()},wr=Math.max,kr=Math.min;function Sr(t,n,e){var r,i,o,a,u,s,l=0,c=!1,f=!1,h=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function p(n){var e=r,o=i;return r=i=void 0,l=n,a=t.apply(o,e)}function d(t){var e=t-s;return void 0===s||e>=n||e<0||f&&t-l>=o}function y(){var t=xr();if(d(t))return g(t);u=setTimeout(y,function(t){var e=n-(t-s);return f?kr(e,o-(t-l)):e}(t))}function g(t){return u=void 0,h&&r?p(t):(r=i=void 0,a)}function v(){var t=xr(),e=d(t);if(r=arguments,i=this,s=t,e){if(void 0===u)return function(t){return l=t,u=setTimeout(y,n),c?p(t):a}(s);if(f)return clearTimeout(u),u=setTimeout(y,n),p(s)}return void 0===u&&(u=setTimeout(y,n)),a}return n=br(n)||0,yr(e)&&(c=!!e.leading,o=(f="maxWait"in e)?wr(br(e.maxWait)||0,n):o,h="trailing"in e?!!e.trailing:h),v.cancel=function(){void 0!==u&&clearTimeout(u),l=0,r=s=i=u=void 0},v.flush=function(){return void 0===u?a:g(xr())},v}var Ar=Object.freeze({Linear:Object.freeze({None:function(t){return t},In:function(t){return t},Out:function(t){return t},InOut:function(t){return t}}),Quadratic:Object.freeze({In:function(t){return t*t},Out:function(t){return t*(2-t)},InOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}}),Cubic:Object.freeze({In:function(t){return t*t*t},Out:function(t){return--t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}}),Quartic:Object.freeze({In:function(t){return t*t*t*t},Out:function(t){return 1- --t*t*t*t},InOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}}),Quintic:Object.freeze({In:function(t){return t*t*t*t*t},Out:function(t){return--t*t*t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}}),Sinusoidal:Object.freeze({In:function(t){return 1-Math.sin((1-t)*Math.PI/2)},Out:function(t){return Math.sin(t*Math.PI/2)},InOut:function(t){return.5*(1-Math.sin(Math.PI*(.5-t)))}}),Exponential:Object.freeze({In:function(t){return 0===t?0:Math.pow(1024,t-1)},Out:function(t){return 1===t?1:1-Math.pow(2,-10*t)},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))}}),Circular:Object.freeze({In:function(t){return 1-Math.sqrt(1-t*t)},Out:function(t){return Math.sqrt(1- --t*t)},InOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}}),Elastic:Object.freeze({In:function(t){return 0===t?0:1===t?1:-Math.pow(2,10*(t-1))*Math.sin(5*(t-1.1)*Math.PI)},Out:function(t){return 0===t?0:1===t?1:Math.pow(2,-10*t)*Math.sin(5*(t-.1)*Math.PI)+1},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?-.5*Math.pow(2,10*(t-1))*Math.sin(5*(t-1.1)*Math.PI):.5*Math.pow(2,-10*(t-1))*Math.sin(5*(t-1.1)*Math.PI)+1}}),Back:Object.freeze({In:function(t){var n=1.70158;return 1===t?1:t*t*((n+1)*t-n)},Out:function(t){var n=1.70158;return 0===t?0:--t*t*((n+1)*t+n)+1},InOut:function(t){var n=2.5949095;return(t*=2)<1?t*t*((n+1)*t-n)*.5:.5*((t-=2)*t*((n+1)*t+n)+2)}}),Bounce:Object.freeze({In:function(t){return 1-Ar.Bounce.Out(1-t)},Out:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},InOut:function(t){return t<.5?.5*Ar.Bounce.In(2*t):.5*Ar.Bounce.Out(2*t-1)+.5}}),generatePow:function(t){return void 0===t&&(t=4),t=(t=t<Number.EPSILON?Number.EPSILON:t)>1e4?1e4:t,{In:function(n){return Math.pow(n,t)},Out:function(n){return 1-Math.pow(1-n,t)},InOut:function(n){return n<.5?Math.pow(2*n,t)/2:(1-Math.pow(2-2*n,t))/2+.5}}}}),Mr=function(){return performance.now()},Or=function(){function t(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._tweens={},this._tweensAddedDuringUpdate={},this.add.apply(this,t)}return t.prototype.getAll=function(){var t=this;return Object.keys(this._tweens).map(function(n){return t._tweens[n]})},t.prototype.removeAll=function(){this._tweens={}},t.prototype.add=function(){for(var t,n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];for(var r=0,i=n;r<i.length;r++){var o=i[r];null===(t=o._group)||void 0===t||t.remove(o),o._group=this,this._tweens[o.getId()]=o,this._tweensAddedDuringUpdate[o.getId()]=o}},t.prototype.remove=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var e=0,r=t;e<r.length;e++){var i=r[e];i._group=void 0,delete this._tweens[i.getId()],delete this._tweensAddedDuringUpdate[i.getId()]}},t.prototype.allStopped=function(){return this.getAll().every(function(t){return!t.isPlaying()})},t.prototype.update=function(t,n){void 0===t&&(t=Mr()),void 0===n&&(n=!0);var e=Object.keys(this._tweens);if(0!==e.length)for(;e.length>0;){this._tweensAddedDuringUpdate={};for(var r=0;r<e.length;r++){var i=this._tweens[e[r]],o=!n;i&&!1===i.update(t,o)&&!n&&this.remove(i)}e=Object.keys(this._tweensAddedDuringUpdate)}},t}(),zr={Linear:function(t,n){var e=t.length-1,r=e*n,i=Math.floor(r),o=zr.Utils.Linear;return n<0?o(t[0],t[1],r):n>1?o(t[e],t[e-1],e-r):o(t[i],t[i+1>e?e:i+1],r-i)},Utils:{Linear:function(t,n,e){return(n-t)*e+t}}},Er=function(){function t(){}return t.nextId=function(){return t._nextId++},t._nextId=0,t}(),Cr=new Or,Pr=function(){function t(t,n){this._isPaused=!1,this._pauseStart=0,this._valuesStart={},this._valuesEnd={},this._valuesStartRepeat={},this._duration=1e3,this._isDynamic=!1,this._initialRepeat=0,this._repeat=0,this._yoyo=!1,this._isPlaying=!1,this._reversed=!1,this._delayTime=0,this._startTime=0,this._easingFunction=Ar.Linear.None,this._interpolationFunction=zr.Linear,this._chainedTweens=[],this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._id=Er.nextId(),this._isChainStopped=!1,this._propertiesAreSetUp=!1,this._goToEnd=!1,this._object=t,"object"==typeof n?(this._group=n,n.add(this)):!0===n&&(this._group=Cr,Cr.add(this))}return t.prototype.getId=function(){return this._id},t.prototype.isPlaying=function(){return this._isPlaying},t.prototype.isPaused=function(){return this._isPaused},t.prototype.getDuration=function(){return this._duration},t.prototype.to=function(t,n){if(void 0===n&&(n=1e3),this._isPlaying)throw new Error("Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.");return this._valuesEnd=t,this._propertiesAreSetUp=!1,this._duration=n<0?0:n,this},t.prototype.duration=function(t){return void 0===t&&(t=1e3),this._duration=t<0?0:t,this},t.prototype.dynamic=function(t){return void 0===t&&(t=!1),this._isDynamic=t,this},t.prototype.start=function(t,n){if(void 0===t&&(t=Mr()),void 0===n&&(n=!1),this._isPlaying)return this;if(this._repeat=this._initialRepeat,this._reversed)for(var e in this._reversed=!1,this._valuesStartRepeat)this._swapEndStartRepeatValues(e),this._valuesStart[e]=this._valuesStartRepeat[e];if(this._isPlaying=!0,this._isPaused=!1,this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._isChainStopped=!1,this._startTime=t,this._startTime+=this._delayTime,!this._propertiesAreSetUp||n){if(this._propertiesAreSetUp=!0,!this._isDynamic){var r={};for(var i in this._valuesEnd)r[i]=this._valuesEnd[i];this._valuesEnd=r}this._setupProperties(this._object,this._valuesStart,this._valuesEnd,this._valuesStartRepeat,n)}return this},t.prototype.startFromCurrentValues=function(t){return this.start(t,!0)},t.prototype._setupProperties=function(t,n,e,r,i){for(var o in e){var a=t[o],u=Array.isArray(a),s=u?"array":typeof a,l=!u&&Array.isArray(e[o]);if("undefined"!==s&&"function"!==s){if(l){if(0===(g=e[o]).length)continue;for(var c=[a],f=0,h=g.length;f<h;f+=1){var p=this._handleRelativeValue(a,g[f]);if(isNaN(p)){l=!1,console.warn("Found invalid interpolation list. Skipping.");break}c.push(p)}l&&(e[o]=c)}if("object"!==s&&!u||!a||l)(void 0===n[o]||i)&&(n[o]=a),u||(n[o]*=1),r[o]=l?e[o].slice().reverse():n[o]||0;else{n[o]=u?[]:{};var d=a;for(var y in d)n[o][y]=d[y];r[o]=u?[]:{};var g=e[o];if(!this._isDynamic){var v={};for(var y in g)v[y]=g[y];e[o]=g=v}this._setupProperties(d,n[o],g,r[o],i)}}}},t.prototype.stop=function(){return this._isChainStopped||(this._isChainStopped=!0,this.stopChainedTweens()),this._isPlaying?(this._isPlaying=!1,this._isPaused=!1,this._onStopCallback&&this._onStopCallback(this._object),this):this},t.prototype.end=function(){return this._goToEnd=!0,this.update(this._startTime+this._duration),this},t.prototype.pause=function(t){return void 0===t&&(t=Mr()),this._isPaused||!this._isPlaying||(this._isPaused=!0,this._pauseStart=t),this},t.prototype.resume=function(t){return void 0===t&&(t=Mr()),this._isPaused&&this._isPlaying?(this._isPaused=!1,this._startTime+=t-this._pauseStart,this._pauseStart=0,this):this},t.prototype.stopChainedTweens=function(){for(var t=0,n=this._chainedTweens.length;t<n;t++)this._chainedTweens[t].stop();return this},t.prototype.group=function(t){return t?(t.add(this),this):(console.warn("tween.group() without args has been removed, use group.add(tween) instead."),this)},t.prototype.remove=function(){var t;return null===(t=this._group)||void 0===t||t.remove(this),this},t.prototype.delay=function(t){return void 0===t&&(t=0),this._delayTime=t,this},t.prototype.repeat=function(t){return void 0===t&&(t=0),this._initialRepeat=t,this._repeat=t,this},t.prototype.repeatDelay=function(t){return this._repeatDelayTime=t,this},t.prototype.yoyo=function(t){return void 0===t&&(t=!1),this._yoyo=t,this},t.prototype.easing=function(t){return void 0===t&&(t=Ar.Linear.None),this._easingFunction=t,this},t.prototype.interpolation=function(t){return void 0===t&&(t=zr.Linear),this._interpolationFunction=t,this},t.prototype.chain=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return this._chainedTweens=t,this},t.prototype.onStart=function(t){return this._onStartCallback=t,this},t.prototype.onEveryStart=function(t){return this._onEveryStartCallback=t,this},t.prototype.onUpdate=function(t){return this._onUpdateCallback=t,this},t.prototype.onRepeat=function(t){return this._onRepeatCallback=t,this},t.prototype.onComplete=function(t){return this._onCompleteCallback=t,this},t.prototype.onStop=function(t){return this._onStopCallback=t,this},t.prototype.update=function(n,e){var r,i,o=this;if(void 0===n&&(n=Mr()),void 0===e&&(e=t.autoStartOnUpdate),this._isPaused)return!0;if(!this._goToEnd&&!this._isPlaying){if(!e)return!1;this.start(n,!0)}if(this._goToEnd=!1,n<this._startTime)return!0;!1===this._onStartCallbackFired&&(this._onStartCallback&&this._onStartCallback(this._object),this._onStartCallbackFired=!0),!1===this._onEveryStartCallbackFired&&(this._onEveryStartCallback&&this._onEveryStartCallback(this._object),this._onEveryStartCallbackFired=!0);var a=n-this._startTime,u=this._duration+(null!==(r=this._repeatDelayTime)&&void 0!==r?r:this._delayTime),s=this._duration+this._repeat*u,l=function(){if(0===o._duration)return 1;if(a>s)return 1;var t=Math.trunc(a/u),n=a-t*u,e=Math.min(n/o._duration,1);return 0===e&&a===o._duration?1:e}(),c=this._easingFunction(l);if(this._updateProperties(this._object,this._valuesStart,this._valuesEnd,c),this._onUpdateCallback&&this._onUpdateCallback(this._object,l),0===this._duration||a>=this._duration){if(this._repeat>0){var f=Math.min(Math.trunc((a-this._duration)/u)+1,this._repeat);for(i in isFinite(this._repeat)&&(this._repeat-=f),this._valuesStartRepeat)this._yoyo||"string"!=typeof this._valuesEnd[i]||(this._valuesStartRepeat[i]=this._valuesStartRepeat[i]+parseFloat(this._valuesEnd[i])),this._yoyo&&this._swapEndStartRepeatValues(i),this._valuesStart[i]=this._valuesStartRepeat[i];return this._yoyo&&(this._reversed=!this._reversed),this._startTime+=u*f,this._onRepeatCallback&&this._onRepeatCallback(this._object),this._onEveryStartCallbackFired=!1,!0}this._onCompleteCallback&&this._onCompleteCallback(this._object);for(var h=0,p=this._chainedTweens.length;h<p;h++)this._chainedTweens[h].start(this._startTime+this._duration,!1);return this._isPlaying=!1,!1}return!0},t.prototype._updateProperties=function(t,n,e,r){for(var i in e)if(void 0!==n[i]){var o=n[i]||0,a=e[i],u=Array.isArray(t[i]),s=Array.isArray(a);!u&&s?t[i]=this._interpolationFunction(a,r):"object"==typeof a&&a?this._updateProperties(t[i],o,a,r):"number"==typeof(a=this._handleRelativeValue(o,a))&&(t[i]=o+(a-o)*r)}},t.prototype._handleRelativeValue=function(t,n){return"string"!=typeof n?n:"+"===n.charAt(0)||"-"===n.charAt(0)?t+parseFloat(n):parseFloat(n)},t.prototype._swapEndStartRepeatValues=function(t){var n=this._valuesStartRepeat[t],e=this._valuesEnd[t];this._valuesStartRepeat[t]="string"==typeof e?this._valuesStartRepeat[t]+parseFloat(e):this._valuesEnd[t],this._valuesEnd[t]=n},t.autoStartOnUpdate=!1,t}();Er.nextId;var jr=Cr;function Tr(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Nr(t,n,e){return Object.defineProperty(t,"prototype",{writable:!1}),t}function Rr(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||function(t,n){if(t){if("string"==typeof t)return Tr(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Tr(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}jr.getAll.bind(jr),jr.removeAll.bind(jr),jr.add.bind(jr),jr.remove.bind(jr),jr.update.bind(jr);var Dr=Nr(function t(n,e){var r=e.default,i=void 0===r?null:r,o=e.triggerUpdate,a=void 0===o||o,u=e.onChange,s=void 0===u?function(t,n){}:u;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.name=n,this.defaultVal=i,this.triggerUpdate=a,this.onChange=s});function Ir(t){var n=t.stateInit,e=void 0===n?function(){return{}}:n,r=t.props,i=void 0===r?{}:r,o=t.methods,a=void 0===o?{}:o,u=t.aliases,s=void 0===u?{}:u,l=t.init,c=void 0===l?function(){}:l,f=t.update,h=void 0===f?function(){}:f,p=Object.keys(i).map(function(t){return new Dr(t,i[t])});return function t(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=!!(this instanceof t?this.constructor:void 0),u=o?r.shift():void 0,l=r[0],f=void 0===l?{}:l,d=Object.assign({},e instanceof Function?e(f):e,{initialised:!1}),y={};function g(t){return v(t,f),_(),g}var v=function(t,n){c.call(g,t,d,n),d.initialised=!0},_=Sr(function(){d.initialised&&(h.call(g,d,y),y={})},1);return p.forEach(function(t){g[t.name]=function(t){var n=t.name,e=t.triggerUpdate,r=void 0!==e&&e,i=t.onChange,o=void 0===i?function(t,n){}:i,a=t.defaultVal,u=void 0===a?null:a;return function(t){var e=d[n];if(!arguments.length)return e;var i=void 0===t?u:t;return d[n]=i,o.call(g,i,d,e),!y.hasOwnProperty(n)&&(y[n]=e),r&&_(),g}}(t)}),Object.keys(a).forEach(function(t){g[t]=function(){for(var n,e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return(n=a[t]).call.apply(n,[g,d].concat(r))}}),Object.entries(s).forEach(function(t){var n=Rr(t,2),e=n[0],r=n[1];return g[e]=g[r]}),g.resetProps=function(){return p.forEach(function(t){g[t.name](t.defaultVal)}),g},g.resetProps(),d._rerender=_,o&&u&&g(u),g}}var Ur=function(t){return"function"==typeof t?t:"string"==typeof t?function(n){return n[t]}:function(n){return t}};function Fr(t){return Fr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fr(t)}var Lr=/^\s+/,$r=/\s+$/;function qr(t,n){if(n=n||{},(t=t||"")instanceof qr)return t;if(!(this instanceof qr))return new qr(t,n);var e=function(t){var n={r:0,g:0,b:0},e=1,r=null,i=null,o=null,a=!1,u=!1;"string"==typeof t&&(t=function(t){t=t.replace(Lr,"").replace($r,"").toLowerCase();var n,e=!1;if(oi[t])t=oi[t],e=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(n=_i.rgb.exec(t))return{r:n[1],g:n[2],b:n[3]};if(n=_i.rgba.exec(t))return{r:n[1],g:n[2],b:n[3],a:n[4]};if(n=_i.hsl.exec(t))return{h:n[1],s:n[2],l:n[3]};if(n=_i.hsla.exec(t))return{h:n[1],s:n[2],l:n[3],a:n[4]};if(n=_i.hsv.exec(t))return{h:n[1],s:n[2],v:n[3]};if(n=_i.hsva.exec(t))return{h:n[1],s:n[2],v:n[3],a:n[4]};if(n=_i.hex8.exec(t))return{r:ci(n[1]),g:ci(n[2]),b:ci(n[3]),a:di(n[4]),format:e?"name":"hex8"};if(n=_i.hex6.exec(t))return{r:ci(n[1]),g:ci(n[2]),b:ci(n[3]),format:e?"name":"hex"};if(n=_i.hex4.exec(t))return{r:ci(n[1]+""+n[1]),g:ci(n[2]+""+n[2]),b:ci(n[3]+""+n[3]),a:di(n[4]+""+n[4]),format:e?"name":"hex8"};if(n=_i.hex3.exec(t))return{r:ci(n[1]+""+n[1]),g:ci(n[2]+""+n[2]),b:ci(n[3]+""+n[3]),format:e?"name":"hex"};return!1}(t));"object"==Fr(t)&&(mi(t.r)&&mi(t.g)&&mi(t.b)?(n=function(t,n,e){return{r:255*si(t,255),g:255*si(n,255),b:255*si(e,255)}}(t.r,t.g,t.b),a=!0,u="%"===String(t.r).substr(-1)?"prgb":"rgb"):mi(t.h)&&mi(t.s)&&mi(t.v)?(r=hi(t.s),i=hi(t.v),n=function(t,n,e){t=6*si(t,360),n=si(n,100),e=si(e,100);var r=Math.floor(t),i=t-r,o=e*(1-n),a=e*(1-i*n),u=e*(1-(1-i)*n),s=r%6,l=[e,a,o,o,u,e][s],c=[u,e,e,a,o,o][s],f=[o,o,u,e,e,a][s];return{r:255*l,g:255*c,b:255*f}}(t.h,r,i),a=!0,u="hsv"):mi(t.h)&&mi(t.s)&&mi(t.l)&&(r=hi(t.s),o=hi(t.l),n=function(t,n,e){var r,i,o;function a(t,n,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(n-t)*e:e<.5?n:e<2/3?t+(n-t)*(2/3-e)*6:t}if(t=si(t,360),n=si(n,100),e=si(e,100),0===n)r=i=o=e;else{var u=e<.5?e*(1+n):e+n-e*n,s=2*e-u;r=a(s,u,t+1/3),i=a(s,u,t),o=a(s,u,t-1/3)}return{r:255*r,g:255*i,b:255*o}}(t.h,r,o),a=!0,u="hsl"),t.hasOwnProperty("a")&&(e=t.a));return e=ui(e),{ok:a,format:t.format||u,r:Math.min(255,Math.max(n.r,0)),g:Math.min(255,Math.max(n.g,0)),b:Math.min(255,Math.max(n.b,0)),a:e}}(t);this._originalInput=t,this._r=e.r,this._g=e.g,this._b=e.b,this._a=e.a,this._roundA=Math.round(100*this._a)/100,this._format=n.format||e.format,this._gradientType=n.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=e.ok}function Br(t,n,e){t=si(t,255),n=si(n,255),e=si(e,255);var r,i,o=Math.max(t,n,e),a=Math.min(t,n,e),u=(o+a)/2;if(o==a)r=i=0;else{var s=o-a;switch(i=u>.5?s/(2-o-a):s/(o+a),o){case t:r=(n-e)/s+(n<e?6:0);break;case n:r=(e-t)/s+2;break;case e:r=(t-n)/s+4}r/=6}return{h:r,s:i,l:u}}function Hr(t,n,e){t=si(t,255),n=si(n,255),e=si(e,255);var r,i,o=Math.max(t,n,e),a=Math.min(t,n,e),u=o,s=o-a;if(i=0===o?0:s/o,o==a)r=0;else{switch(o){case t:r=(n-e)/s+(n<e?6:0);break;case n:r=(e-t)/s+2;break;case e:r=(t-n)/s+4}r/=6}return{h:r,s:i,v:u}}function Vr(t,n,e,r){var i=[fi(Math.round(t).toString(16)),fi(Math.round(n).toString(16)),fi(Math.round(e).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function Gr(t,n,e,r){return[fi(pi(r)),fi(Math.round(t).toString(16)),fi(Math.round(n).toString(16)),fi(Math.round(e).toString(16))].join("")}function Xr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.s-=n/100,e.s=li(e.s),qr(e)}function Wr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.s+=n/100,e.s=li(e.s),qr(e)}function Yr(t){return qr(t).desaturate(100)}function Zr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.l+=n/100,e.l=li(e.l),qr(e)}function Qr(t,n){n=0===n?0:n||10;var e=qr(t).toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-n/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-n/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-n/100*255))),qr(e)}function Jr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.l-=n/100,e.l=li(e.l),qr(e)}function Kr(t,n){var e=qr(t).toHsl(),r=(e.h+n)%360;return e.h=r<0?360+r:r,qr(e)}function ti(t){var n=qr(t).toHsl();return n.h=(n.h+180)%360,qr(n)}function ni(t,n){if(isNaN(n)||n<=0)throw new Error("Argument to polyad must be a positive number");for(var e=qr(t).toHsl(),r=[qr(t)],i=360/n,o=1;o<n;o++)r.push(qr({h:(e.h+o*i)%360,s:e.s,l:e.l}));return r}function ei(t){var n=qr(t).toHsl(),e=n.h;return[qr(t),qr({h:(e+72)%360,s:n.s,l:n.l}),qr({h:(e+216)%360,s:n.s,l:n.l})]}function ri(t,n,e){n=n||6,e=e||30;var r=qr(t).toHsl(),i=360/e,o=[qr(t)];for(r.h=(r.h-(i*n>>1)+720)%360;--n;)r.h=(r.h+i)%360,o.push(qr(r));return o}function ii(t,n){n=n||6;for(var e=qr(t).toHsv(),r=e.h,i=e.s,o=e.v,a=[],u=1/n;n--;)a.push(qr({h:r,s:i,v:o})),o=(o+u)%1;return a}qr.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,n,e,r=this.toRgb();return t=r.r/255,n=r.g/255,e=r.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))},setAlpha:function(t){return this._a=ui(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=Hr(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=Hr(this._r,this._g,this._b),n=Math.round(360*t.h),e=Math.round(100*t.s),r=Math.round(100*t.v);return 1==this._a?"hsv("+n+", "+e+"%, "+r+"%)":"hsva("+n+", "+e+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=Br(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=Br(this._r,this._g,this._b),n=Math.round(360*t.h),e=Math.round(100*t.s),r=Math.round(100*t.l);return 1==this._a?"hsl("+n+", "+e+"%, "+r+"%)":"hsla("+n+", "+e+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return Vr(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,n,e,r,i){var o=[fi(Math.round(t).toString(16)),fi(Math.round(n).toString(16)),fi(Math.round(e).toString(16)),fi(pi(r))];if(i&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1))return o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0);return o.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*si(this._r,255))+"%",g:Math.round(100*si(this._g,255))+"%",b:Math.round(100*si(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*si(this._r,255))+"%, "+Math.round(100*si(this._g,255))+"%, "+Math.round(100*si(this._b,255))+"%)":"rgba("+Math.round(100*si(this._r,255))+"%, "+Math.round(100*si(this._g,255))+"%, "+Math.round(100*si(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(ai[Vr(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var n="#"+Gr(this._r,this._g,this._b,this._a),e=n,r=this._gradientType?"GradientType = 1, ":"";if(t){var i=qr(t);e="#"+Gr(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+n+",endColorstr="+e+")"},toString:function(t){var n=!!t;t=t||this._format;var e=!1,r=this._a<1&&this._a>=0;return n||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(e=this.toRgbString()),"prgb"===t&&(e=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(e=this.toHexString()),"hex3"===t&&(e=this.toHexString(!0)),"hex4"===t&&(e=this.toHex8String(!0)),"hex8"===t&&(e=this.toHex8String()),"name"===t&&(e=this.toName()),"hsl"===t&&(e=this.toHslString()),"hsv"===t&&(e=this.toHsvString()),e||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return qr(this.toString())},_applyModification:function(t,n){var e=t.apply(null,[this].concat([].slice.call(n)));return this._r=e._r,this._g=e._g,this._b=e._b,this.setAlpha(e._a),this},lighten:function(){return this._applyModification(Zr,arguments)},brighten:function(){return this._applyModification(Qr,arguments)},darken:function(){return this._applyModification(Jr,arguments)},desaturate:function(){return this._applyModification(Xr,arguments)},saturate:function(){return this._applyModification(Wr,arguments)},greyscale:function(){return this._applyModification(Yr,arguments)},spin:function(){return this._applyModification(Kr,arguments)},_applyCombination:function(t,n){return t.apply(null,[this].concat([].slice.call(n)))},analogous:function(){return this._applyCombination(ri,arguments)},complement:function(){return this._applyCombination(ti,arguments)},monochromatic:function(){return this._applyCombination(ii,arguments)},splitcomplement:function(){return this._applyCombination(ei,arguments)},triad:function(){return this._applyCombination(ni,[3])},tetrad:function(){return this._applyCombination(ni,[4])}},qr.fromRatio=function(t,n){if("object"==Fr(t)){var e={};for(var r in t)t.hasOwnProperty(r)&&(e[r]="a"===r?t[r]:hi(t[r]));t=e}return qr(t,n)},qr.equals=function(t,n){return!(!t||!n)&&qr(t).toRgbString()==qr(n).toRgbString()},qr.random=function(){return qr.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},qr.mix=function(t,n,e){e=0===e?0:e||50;var r=qr(t).toRgb(),i=qr(n).toRgb(),o=e/100;return qr({r:(i.r-r.r)*o+r.r,g:(i.g-r.g)*o+r.g,b:(i.b-r.b)*o+r.b,a:(i.a-r.a)*o+r.a})},
// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)
// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)
qr.readability=function(t,n){var e=qr(t),r=qr(n);return(Math.max(e.getLuminance(),r.getLuminance())+.05)/(Math.min(e.getLuminance(),r.getLuminance())+.05)},qr.isReadable=function(t,n,e){var r,i,o=qr.readability(t,n);switch(i=!1,(r=function(t){var n,e;n=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),e=(t.size||"small").toLowerCase(),"AA"!==n&&"AAA"!==n&&(n="AA");"small"!==e&&"large"!==e&&(e="small");return{level:n,size:e}}(e)).level+r.size){case"AAsmall":case"AAAlarge":i=o>=4.5;break;case"AAlarge":i=o>=3;break;case"AAAsmall":i=o>=7}return i},qr.mostReadable=function(t,n,e){var r,i,o,a,u=null,s=0;i=(e=e||{}).includeFallbackColors,o=e.level,a=e.size;for(var l=0;l<n.length;l++)(r=qr.readability(t,n[l]))>s&&(s=r,u=qr(n[l]));return qr.isReadable(t,u,{level:o,size:a})||!i?u:(e.includeFallbackColors=!1,qr.mostReadable(t,["#fff","#000"],e))};var oi=qr.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},ai=qr.hexNames=function(t){var n={};for(var e in t)t.hasOwnProperty(e)&&(n[t[e]]=e);return n}(oi);function ui(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function si(t,n){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var e=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=Math.min(n,Math.max(0,parseFloat(t))),e&&(t=parseInt(t*n,10)/100),Math.abs(t-n)<1e-6?1:t%n/parseFloat(n)}function li(t){return Math.min(1,Math.max(0,t))}function ci(t){return parseInt(t,16)}function fi(t){return 1==t.length?"0"+t:""+t}function hi(t){return t<=1&&(t=100*t+"%"),t}function pi(t){return Math.round(255*parseFloat(t)).toString(16)}function di(t){return ci(t)/255}var yi,gi,vi,_i=(gi="[\\s|\\(]+("+(yi="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+yi+")[,|\\s]+("+yi+")\\s*\\)?",vi="[\\s|\\(]+("+yi+")[,|\\s]+("+yi+")[,|\\s]+("+yi+")[,|\\s]+("+yi+")\\s*\\)?",{CSS_UNIT:new RegExp(yi),rgb:new RegExp("rgb"+gi),rgba:new RegExp("rgba"+vi),hsl:new RegExp("hsl"+gi),hsla:new RegExp("hsla"+vi),hsv:new RegExp("hsv"+gi),hsva:new RegExp("hsva"+vi),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function mi(t){return!!_i.CSS_UNIT.exec(t)}function bi(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function xi(t,n,e){if("function"==typeof t?t===n:t.has(n))return arguments.length<3?n:e;throw new TypeError("Private element is not present on this object")}function wi(t,n){return t.get(xi(t,n))}function ki(t,n,e){(function(t,n){if(n.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,n),n.set(t,e)}function Si(t,n,e){return t.set(xi(t,n),e),e}function Ai(t,n,e){return n&&function(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,Oi(r.key),r)}}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Mi(t){return function(t){if(Array.isArray(t))return bi(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return bi(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?bi(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oi(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof n?n:n+""}var zi,Ei,Ci,Pi,ji,Ti,Ni,Ri,Di,Ii,Ui,Fi,Li=function(t,n,e){return(t<<16)+(n<<8)+e},$i=function(t,n){return 123*t%Math.pow(2,n)},qi=new WeakMap,Bi=new WeakMap,Hi=function(){return Ai(function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),ki(this,qi,void 0),ki(this,Bi,void 0),Si(Bi,this,n),this.reset()},[{key:"reset",value:function(){Si(qi,this,["__reserved for background__"])}},{key:"register",value:function(t){if(wi(qi,this).length>=Math.pow(2,24-wi(Bi,this)))return null;var n,e=wi(qi,this).length,r=$i(e,wi(Bi,this)),i=(n=e+(r<<24-wi(Bi,this)),"#".concat(Math.min(n,Math.pow(2,24)).toString(16).padStart(6,"0")));return wi(qi,this).push(t),i}},{key:"lookup",value:function(t){if(!t)return null;var n="string"==typeof t?function(t){var n=qr(t).toRgb(),e=n.r,r=n.g,i=n.b;return Li(e,r,i)}(t):Li.apply(void 0,Mi(t));if(!n)return null;var e=n&Math.pow(2,24-wi(Bi,this))-1,r=n>>24-wi(Bi,this)&Math.pow(2,wi(Bi,this))-1;return $i(e,wi(Bi,this))!==r||e>=wi(qi,this).length?null:wi(qi,this)[e]}}])}(),Vi={},Gi=[],Xi=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Wi=Array.isArray;function Yi(t,n){for(var e in n)t[e]=n[e];return t}function Zi(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function Qi(t,n,e,r,i){var o={type:t,props:n,key:e,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==i?++Ci:i,__i:-1,__u:0};return null==i&&null!=Ei.vnode&&Ei.vnode(o),o}function Ji(t){return t.children}function Ki(t,n){this.props=t,this.context=n}function to(t,n){if(null==n)return t.__?to(t.__,t.__i+1):null;for(var e;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e)return e.__e;return"function"==typeof t.type?to(t):null}function no(t){var n,e;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e){t.__e=t.__c.base=e.__e;break}return no(t)}}function eo(t){(!t.__d&&(t.__d=!0)&&ji.push(t)&&!ro.__r++||Ti!=Ei.debounceRendering)&&((Ti=Ei.debounceRendering)||Ni)(ro)}function ro(){for(var t,n,e,r,i,o,a,u=1;ji.length;)ji.length>u&&ji.sort(Ri),t=ji.shift(),u=ji.length,t.__d&&(e=void 0,i=(r=(n=t).__v).__e,o=[],a=[],n.__P&&((e=Yi({},r)).__v=r.__v+1,Ei.vnode&&Ei.vnode(e),fo(n.__P,e,r,n.__n,n.__P.namespaceURI,32&r.__u?[i]:null,o,null==i?to(r):i,!!(32&r.__u),a),e.__v=r.__v,e.__.__k[e.__i]=e,ho(o,e,a),e.__e!=i&&no(e)));ro.__r=0}function io(t,n,e,r,i,o,a,u,s,l,c){var f,h,p,d,y,g,v=r&&r.__k||Gi,_=n.length;for(s=oo(e,n,v,s,_),f=0;f<_;f++)null!=(p=e.__k[f])&&(h=-1==p.__i?Vi:v[p.__i]||Vi,p.__i=f,g=fo(t,p,h,i,o,a,u,s,l,c),d=p.__e,p.ref&&h.ref!=p.ref&&(h.ref&&go(h.ref,null,p),c.push(p.ref,p.__c||d,p)),null==y&&null!=d&&(y=d),4&p.__u||h.__k===p.__k?s=ao(p,s,t):"function"==typeof p.type&&void 0!==g?s=g:d&&(s=d.nextSibling),p.__u&=-7);return e.__e=y,s}function oo(t,n,e,r,i){var o,a,u,s,l,c=e.length,f=c,h=0;for(t.__k=new Array(i),o=0;o<i;o++)null!=(a=n[o])&&"boolean"!=typeof a&&"function"!=typeof a?(s=o+h,(a=t.__k[o]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?Qi(null,a,null,null,null):Wi(a)?Qi(Ji,{children:a},null,null,null):null==a.constructor&&a.__b>0?Qi(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=t,a.__b=t.__b+1,u=null,-1!=(l=a.__i=uo(a,e,s,f))&&(f--,(u=e[l])&&(u.__u|=2)),null==u||null==u.__v?(-1==l&&(i>c?h--:i<c&&h++),"function"!=typeof a.type&&(a.__u|=4)):l!=s&&(l==s-1?h--:l==s+1?h++:(l>s?h--:h++,a.__u|=4))):t.__k[o]=null;if(f)for(o=0;o<c;o++)null!=(u=e[o])&&!(2&u.__u)&&(u.__e==r&&(r=to(u)),vo(u,u));return r}function ao(t,n,e){var r,i;if("function"==typeof t.type){for(r=t.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=t,n=ao(r[i],n,e));return n}t.__e!=n&&(n&&t.type&&!e.contains(n)&&(n=to(t)),e.insertBefore(t.__e,n||null),n=t.__e);do{n=n&&n.nextSibling}while(null!=n&&8==n.nodeType);return n}function uo(t,n,e,r){var i,o,a=t.key,u=t.type,s=n[e];if(null===s&&null==t.key||s&&a==s.key&&u==s.type&&!(2&s.__u))return e;if(r>(null==s||2&s.__u?0:1))for(i=e-1,o=e+1;i>=0||o<n.length;){if(i>=0){if((s=n[i])&&!(2&s.__u)&&a==s.key&&u==s.type)return i;i--}if(o<n.length){if((s=n[o])&&!(2&s.__u)&&a==s.key&&u==s.type)return o;o++}}return-1}function so(t,n,e){"-"==n[0]?t.setProperty(n,null==e?"":e):t[n]=null==e?"":"number"!=typeof e||Xi.test(n)?e:e+"px"}function lo(t,n,e,r,i){var o,a;t:if("style"==n)if("string"==typeof e)t.style.cssText=e;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(n in r)e&&n in e||so(t.style,n,"");if(e)for(n in e)r&&e[n]==r[n]||so(t.style,n,e[n])}else if("o"==n[0]&&"n"==n[1])o=n!=(n=n.replace(Di,"$1")),a=n.toLowerCase(),n=a in t||"onFocusOut"==n||"onFocusIn"==n?a.slice(2):n.slice(2),t.l||(t.l={}),t.l[n+o]=e,e?r?e.u=r.u:(e.u=Ii,t.addEventListener(n,o?Fi:Ui,o)):t.removeEventListener(n,o?Fi:Ui,o);else{if("http://www.w3.org/2000/svg"==i)n=n.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=n&&"height"!=n&&"href"!=n&&"list"!=n&&"form"!=n&&"tabIndex"!=n&&"download"!=n&&"rowSpan"!=n&&"colSpan"!=n&&"role"!=n&&"popover"!=n&&n in t)try{t[n]=null==e?"":e;break t}catch(t){}"function"==typeof e||(null==e||!1===e&&"-"!=n[4]?t.removeAttribute(n):t.setAttribute(n,"popover"==n&&1==e?"":e))}}function co(t){return function(n){if(this.l){var e=this.l[n.type+t];if(null==n.t)n.t=Ii++;else if(n.t<e.u)return;return e(Ei.event?Ei.event(n):n)}}}function fo(t,n,e,r,i,o,a,u,s,l){var c,f,h,p,d,y,g,v,_,m,b,x,w,k,S,A,M,O=n.type;if(null!=n.constructor)return null;128&e.__u&&(s=!!(32&e.__u),o=[u=n.__e=e.__e]),(c=Ei.__b)&&c(n);t:if("function"==typeof O)try{if(v=n.props,_="prototype"in O&&O.prototype.render,m=(c=O.contextType)&&r[c.__c],b=c?m?m.props.value:c.__:r,e.__c?g=(f=n.__c=e.__c).__=f.__E:(_?n.__c=f=new O(v,b):(n.__c=f=new Ki(v,b),f.constructor=O,f.render=_o),m&&m.sub(f),f.props=v,f.state||(f.state={}),f.context=b,f.__n=r,h=f.__d=!0,f.__h=[],f._sb=[]),_&&null==f.__s&&(f.__s=f.state),_&&null!=O.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=Yi({},f.__s)),Yi(f.__s,O.getDerivedStateFromProps(v,f.__s))),p=f.props,d=f.state,f.__v=n,h)_&&null==O.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),_&&null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(_&&null==O.getDerivedStateFromProps&&v!==p&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(v,b),!f.__e&&null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(v,f.__s,b)||n.__v==e.__v){for(n.__v!=e.__v&&(f.props=v,f.state=f.__s,f.__d=!1),n.__e=e.__e,n.__k=e.__k,n.__k.some(function(t){t&&(t.__=n)}),x=0;x<f._sb.length;x++)f.__h.push(f._sb[x]);f._sb=[],f.__h.length&&a.push(f);break t}null!=f.componentWillUpdate&&f.componentWillUpdate(v,f.__s,b),_&&null!=f.componentDidUpdate&&f.__h.push(function(){f.componentDidUpdate(p,d,y)})}if(f.context=b,f.props=v,f.__P=t,f.__e=!1,w=Ei.__r,k=0,_){for(f.state=f.__s,f.__d=!1,w&&w(n),c=f.render(f.props,f.state,f.context),S=0;S<f._sb.length;S++)f.__h.push(f._sb[S]);f._sb=[]}else do{f.__d=!1,w&&w(n),c=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++k<25);f.state=f.__s,null!=f.getChildContext&&(r=Yi(Yi({},r),f.getChildContext())),_&&!h&&null!=f.getSnapshotBeforeUpdate&&(y=f.getSnapshotBeforeUpdate(p,d)),A=c,null!=c&&c.type===Ji&&null==c.key&&(A=po(c.props.children)),u=io(t,Wi(A)?A:[A],n,e,r,i,o,a,u,s,l),f.base=n.__e,n.__u&=-161,f.__h.length&&a.push(f),g&&(f.__E=f.__=null)}catch(t){if(n.__v=null,s||null!=o)if(t.then){for(n.__u|=s?160:128;u&&8==u.nodeType&&u.nextSibling;)u=u.nextSibling;o[o.indexOf(u)]=null,n.__e=u}else for(M=o.length;M--;)Zi(o[M]);else n.__e=e.__e,n.__k=e.__k;Ei.__e(t,n,e)}else null==o&&n.__v==e.__v?(n.__k=e.__k,n.__e=e.__e):u=n.__e=yo(e.__e,n,e,r,i,o,a,s,l);return(c=Ei.diffed)&&c(n),128&n.__u?void 0:u}function ho(t,n,e){for(var r=0;r<e.length;r++)go(e[r],e[++r],e[++r]);Ei.__c&&Ei.__c(n,t),t.some(function(n){try{t=n.__h,n.__h=[],t.some(function(t){t.call(n)})}catch(t){Ei.__e(t,n.__v)}})}function po(t){return"object"!=typeof t||null==t||t.__b&&t.__b>0?t:Wi(t)?t.map(po):Yi({},t)}function yo(t,n,e,r,i,o,a,u,s){var l,c,f,h,p,d,y,g=e.props,v=n.props,_=n.type;if("svg"==_?i="http://www.w3.org/2000/svg":"math"==_?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=o)for(l=0;l<o.length;l++)if((p=o[l])&&"setAttribute"in p==!!_&&(_?p.localName==_:3==p.nodeType)){t=p,o[l]=null;break}if(null==t){if(null==_)return document.createTextNode(v);t=document.createElementNS(i,_,v.is&&v),u&&(Ei.__m&&Ei.__m(n,o),u=!1),o=null}if(null==_)g===v||u&&t.data==v||(t.data=v);else{if(o=o&&zi.call(t.childNodes),g=e.props||Vi,!u&&null!=o)for(g={},l=0;l<t.attributes.length;l++)g[(p=t.attributes[l]).name]=p.value;for(l in g)if(p=g[l],"children"==l);else if("dangerouslySetInnerHTML"==l)f=p;else if(!(l in v)){if("value"==l&&"defaultValue"in v||"checked"==l&&"defaultChecked"in v)continue;lo(t,l,null,p,i)}for(l in v)p=v[l],"children"==l?h=p:"dangerouslySetInnerHTML"==l?c=p:"value"==l?d=p:"checked"==l?y=p:u&&"function"!=typeof p||g[l]===p||lo(t,l,p,g[l],i);if(c)u||f&&(c.__html==f.__html||c.__html==t.innerHTML)||(t.innerHTML=c.__html),n.__k=[];else if(f&&(t.innerHTML=""),io("template"==n.type?t.content:t,Wi(h)?h:[h],n,e,r,"foreignObject"==_?"http://www.w3.org/1999/xhtml":i,o,a,o?o[0]:e.__k&&to(e,0),u,s),null!=o)for(l=o.length;l--;)Zi(o[l]);u||(l="value","progress"==_&&null==d?t.removeAttribute("value"):null!=d&&(d!==t[l]||"progress"==_&&!d||"option"==_&&d!=g[l])&&lo(t,l,d,g[l],i),l="checked",null!=y&&y!=t[l]&&lo(t,l,y,g[l],i))}return t}function go(t,n,e){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==n||(t.__u=t(n))}else t.current=n}catch(t){Ei.__e(t,e)}}function vo(t,n,e){var r,i;if(Ei.unmount&&Ei.unmount(t),(r=t.ref)&&(r.current&&r.current!=t.__e||go(r,null,n)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){Ei.__e(t,n)}r.base=r.__P=null}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&vo(r[i],n,e||"function"!=typeof t.type);e||Zi(t.__e),t.__c=t.__=t.__e=void 0}function _o(t,n,e){return this.constructor(t,e)}function mo(t,n,e){var r,i,o;n==document&&(n=document.documentElement),Ei.__&&Ei.__(t,n),r=!1?null:n.__k,i=[],o=[],fo(n,t=n.__k=function(t,n,e){var r,i,o,a={};for(o in n)"key"==o?r=n[o]:"ref"==o?i=n[o]:a[o]=n[o];if(arguments.length>2&&(a.children=arguments.length>3?zi.call(arguments,2):e),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===a[o]&&(a[o]=t.defaultProps[o]);return Qi(t,a,r,i,null)}(Ji,null,[t]),r||Vi,Vi,n.namespaceURI,r?null:n.firstChild?zi.call(n.childNodes):null,i,r?r.__e:n.firstChild,false,o),ho(i,t,o)}function bo(t,n,e){var r,i,o,a,u=Yi({},t.props);for(o in t.type&&t.type.defaultProps&&(a=t.type.defaultProps),n)"key"==o?r=n[o]:"ref"==o?i=n[o]:u[o]=void 0===n[o]&&null!=a?a[o]:n[o];return arguments.length>2&&(u.children=arguments.length>3?zi.call(arguments,2):e),Qi(t.type,u,r||t.key,i||t.ref,null)}function xo(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function wo(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function ko(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),e.push.apply(e,r)}return e}function So(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||function(t,n){if(t){if("string"==typeof t)return xo(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?xo(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ao(t){return Ao="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ao(t)}zi=Gi.slice,Ei={__e:function(t,n,e,r){for(var i,o,a;n=n.__;)if((i=n.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(t)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),a=i.__d),a)return i.__E=i}catch(n){t=n}throw t}},Ci=0,Pi=function(t){return null!=t&&null==t.constructor},Ki.prototype.setState=function(t,n){var e;e=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=Yi({},this.state),"function"==typeof t&&(t=t(Yi({},e),this.props)),t&&Yi(e,t),null!=t&&this.__v&&(n&&this._sb.push(n),eo(this))},Ki.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),eo(this))},Ki.prototype.render=Ji,ji=[],Ni="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Ri=function(t,n){return t.__v.__b-n.__v.__b},ro.__r=0,Di=/(PointerCapture)$|Capture$/i,Ii=0,Ui=co(!1),Fi=co(!0);var Mo=function(t){if("object"!==Ao(t))return t;var n,e=bo(t);e.props&&(e.props=function(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?ko(Object(e),!0).forEach(function(n){wo(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ko(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}({},e.props),null!=e&&null!==(n=e.props)&&void 0!==n&&n.children&&(e.props.children=Array.isArray(e.props.children)?e.props.children.map(Mo):Mo(e.props.children)));return e};!function(t,n){void 0===n&&(n={});var e=n.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===e&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".float-tooltip-kap {\n  position: absolute;\n  width: max-content; /* prevent shrinking near right edge */\n  max-width: max(50%, 150px);\n  padding: 3px 5px;\n  border-radius: 3px;\n  font: 12px sans-serif;\n  color: #eee;\n  background: rgba(0,0,0,0.6);\n  pointer-events: none;\n}\n");var Oo=Ir({props:{content:{default:!1},offsetX:{triggerUpdate:!1},offsetY:{triggerUpdate:!1}},init:function(t,n){var e=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).style,r=void 0===e?{}:e,i=St(!!t&&"object"===Ao(t)&&!!t.node&&"function"==typeof t.node?t.node():t);"static"===i.style("position")&&i.style("position","relative"),n.tooltipEl=i.append("div").attr("class","float-tooltip-kap"),Object.entries(r).forEach(function(t){var e=So(t,2),r=e[0],i=e[1];return n.tooltipEl.style(r,i)}),n.tooltipEl.style("left","-10000px").style("display","none");var o="tooltip-".concat(Math.round(1e12*Math.random()));n.mouseInside=!1,i.on("mousemove.".concat(o),function(t){n.mouseInside=!0;var e=At(t),r=i.node(),o=r.offsetWidth,a=r.offsetHeight,u=[null===n.offsetX||void 0===n.offsetX?"-".concat(e[0]/o*100,"%"):"number"==typeof n.offsetX?"calc(-50% + ".concat(n.offsetX,"px)"):n.offsetX,null===n.offsetY||void 0===n.offsetY?a>130&&a-e[1]<100?"calc(-100% - 6px)":"21px":"number"==typeof n.offsetY?n.offsetY<0?"calc(-100% - ".concat(Math.abs(n.offsetY),"px)"):"".concat(n.offsetY,"px"):n.offsetY];n.tooltipEl.style("left",e[0]+"px").style("top",e[1]+"px").style("transform","translate(".concat(u.join(","),")")),n.content&&n.tooltipEl.style("display","inline")}),i.on("mouseover.".concat(o),function(){n.mouseInside=!0,n.content&&n.tooltipEl.style("display","inline")}),i.on("mouseout.".concat(o),function(){n.mouseInside=!1,n.tooltipEl.style("display","none")})},update:function(t){var n,e;t.tooltipEl.style("display",t.content&&t.mouseInside?"inline":"none"),t.content?t.content instanceof HTMLElement?(t.tooltipEl.text(""),t.tooltipEl.append(function(){return t.content})):"string"==typeof t.content?t.tooltipEl.html(t.content):!function(t){return Pi(bo(t))}(t.content)?(t.tooltipEl.style("display","none"),console.warn("Tooltip content is invalid, skipping.",t.content,t.content.toString())):(t.tooltipEl.text(""),n=t.content,delete(e=t.tooltipEl.node()).__k,mo(Mo(n),e)):t.tooltipEl.text("")}});function zo(t,n,e){var r,i=1;function o(){var o,a,u=r.length,s=0,l=0,c=0;for(o=0;o<u;++o)s+=(a=r[o]).x||0,l+=a.y||0,c+=a.z||0;for(s=(s/u-t)*i,l=(l/u-n)*i,c=(c/u-e)*i,o=0;o<u;++o)a=r[o],s&&(a.x-=s),l&&(a.y-=l),c&&(a.z-=c)}return null==t&&(t=0),null==n&&(n=0),null==e&&(e=0),o.initialize=function(t){r=t},o.x=function(n){return arguments.length?(t=+n,o):t},o.y=function(t){return arguments.length?(n=+t,o):n},o.z=function(t){return arguments.length?(e=+t,o):e},o.strength=function(t){return arguments.length?(i=+t,o):i},o}function Eo(t,n,e){if(isNaN(n))return t;var r,i,o,a,u,s,l=t._root,c={data:e},f=t._x0,h=t._x1;if(!l)return t._root=c,t;for(;l.length;)if((a=n>=(i=(f+h)/2))?f=i:h=i,r=l,!(l=l[u=+a]))return r[u]=c,t;if(n===(o=+t._x.call(null,l.data)))return c.next=l,r?r[u]=c:t._root=c,t;do{r=r?r[u]=new Array(2):t._root=new Array(2),(a=n>=(i=(f+h)/2))?f=i:h=i}while((u=+a)===(s=+(o>=i)));return r[s]=l,r[u]=c,t}function Co(t,n,e){this.node=t,this.x0=n,this.x1=e}function Po(t){return t[0]}function jo(t,n){var e=new To(null==n?Po:n,NaN,NaN);return null==t?e:e.addAll(t)}function To(t,n,e){this._x=t,this._x0=n,this._x1=e,this._root=void 0}function No(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var Ro=jo.prototype=To.prototype;function Do(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,a,u,s,l,c,f,h,p=t._root,d={data:r},y=t._x0,g=t._y0,v=t._x1,_=t._y1;if(!p)return t._root=d,t;for(;p.length;)if((l=n>=(o=(y+v)/2))?y=o:v=o,(c=e>=(a=(g+_)/2))?g=a:_=a,i=p,!(p=p[f=c<<1|l]))return i[f]=d,t;if(u=+t._x.call(null,p.data),s=+t._y.call(null,p.data),n===u&&e===s)return d.next=p,i?i[f]=d:t._root=d,t;do{i=i?i[f]=new Array(4):t._root=new Array(4),(l=n>=(o=(y+v)/2))?y=o:v=o,(c=e>=(a=(g+_)/2))?g=a:_=a}while((f=c<<1|l)==(h=(s>=a)<<1|u>=o));return i[h]=p,i[f]=d,t}function Io(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function Uo(t){return t[0]}function Fo(t){return t[1]}function Lo(t,n,e){var r=new $o(null==n?Uo:n,null==e?Fo:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function $o(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function qo(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}Ro.copy=function(){var t,n,e=new To(this._x,this._x0,this._x1),r=this._root;if(!r)return e;if(!r.length)return e._root=No(r),e;for(t=[{source:r,target:e._root=new Array(2)}];r=t.pop();)for(var i=0;i<2;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(2)}):r.target[i]=No(n));return e},Ro.add=function(t){const n=+this._x.call(null,t);return Eo(this.cover(n),n,t)},Ro.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n);let r=1/0,i=-1/0;for(let o,a=0;a<n;++a)isNaN(o=+this._x.call(null,t[a]))||(e[a]=o,o<r&&(r=o),o>i&&(i=o));if(r>i)return this;this.cover(r).cover(i);for(let r=0;r<n;++r)Eo(this,e[r],t[r]);return this},Ro.cover=function(t){if(isNaN(t=+t))return this;var n=this._x0,e=this._x1;if(isNaN(n))e=(n=Math.floor(t))+1;else{for(var r,i,o=e-n||1,a=this._root;n>t||t>=e;)switch(i=+(t<n),(r=new Array(2))[i]=a,a=r,o*=2,i){case 0:e=n+o;break;case 1:n=e-o}this._root&&this._root.length&&(this._root=a)}return this._x0=n,this._x1=e,this},Ro.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},Ro.extent=function(t){return arguments.length?this.cover(+t[0][0]).cover(+t[1][0]):isNaN(this._x0)?void 0:[[this._x0],[this._x1]]},Ro.find=function(t,n){var e,r,i,o,a,u=this._x0,s=this._x1,l=[],c=this._root;for(c&&l.push(new Co(c,u,s)),null==n?n=1/0:(u=t-n,s=t+n);o=l.pop();)if(!(!(c=o.node)||(r=o.x0)>s||(i=o.x1)<u))if(c.length){var f=(r+i)/2;l.push(new Co(c[1],f,i),new Co(c[0],r,f)),(a=+(t>=f))&&(o=l[l.length-1],l[l.length-1]=l[l.length-1-a],l[l.length-1-a]=o)}else{var h=Math.abs(t-+this._x.call(null,c.data));h<n&&(n=h,u=t-h,s=t+h,e=c.data)}return e},Ro.remove=function(t){if(isNaN(o=+this._x.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c=this._root,f=this._x0,h=this._x1;if(!c)return this;if(c.length)for(;;){if((u=o>=(a=(f+h)/2))?f=a:h=a,n=c,!(c=c[s=+u]))return this;if(!c.length)break;n[s+1&1]&&(e=n,l=s)}for(;c.data!==t;)if(r=c,!(c=c.next))return this;return(i=c.next)&&delete c.next,r?(i?r.next=i:delete r.next,this):n?(i?n[s]=i:delete n[s],(c=n[0]||n[1])&&c===(n[1]||n[0])&&!c.length&&(e?e[l]=c:this._root=c),this):(this._root=i,this)},Ro.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},Ro.root=function(){return this._root},Ro.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},Ro.visit=function(t){var n,e,r,i,o=[],a=this._root;for(a&&o.push(new Co(a,this._x0,this._x1));n=o.pop();)if(!t(a=n.node,r=n.x0,i=n.x1)&&a.length){var u=(r+i)/2;(e=a[1])&&o.push(new Co(e,u,i)),(e=a[0])&&o.push(new Co(e,r,u))}return this},Ro.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Co(this._root,this._x0,this._x1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.x1,s=(a+u)/2;(o=i[0])&&e.push(new Co(o,a,s)),(o=i[1])&&e.push(new Co(o,s,u))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.x1);return this},Ro.x=function(t){return arguments.length?(this._x=t,this):this._x};var Bo=Lo.prototype=$o.prototype;function Ho(t,n,e,r,i){if(isNaN(n)||isNaN(e)||isNaN(r))return t;var o,a,u,s,l,c,f,h,p,d,y,g,v=t._root,_={data:i},m=t._x0,b=t._y0,x=t._z0,w=t._x1,k=t._y1,S=t._z1;if(!v)return t._root=_,t;for(;v.length;)if((h=n>=(a=(m+w)/2))?m=a:w=a,(p=e>=(u=(b+k)/2))?b=u:k=u,(d=r>=(s=(x+S)/2))?x=s:S=s,o=v,!(v=v[y=d<<2|p<<1|h]))return o[y]=_,t;if(l=+t._x.call(null,v.data),c=+t._y.call(null,v.data),f=+t._z.call(null,v.data),n===l&&e===c&&r===f)return _.next=v,o?o[y]=_:t._root=_,t;do{o=o?o[y]=new Array(8):t._root=new Array(8),(h=n>=(a=(m+w)/2))?m=a:w=a,(p=e>=(u=(b+k)/2))?b=u:k=u,(d=r>=(s=(x+S)/2))?x=s:S=s}while((y=d<<2|p<<1|h)==(g=(f>=s)<<2|(c>=u)<<1|l>=a));return o[g]=v,o[y]=_,t}function Vo(t,n,e,r,i,o,a){this.node=t,this.x0=n,this.y0=e,this.z0=r,this.x1=i,this.y1=o,this.z1=a}Bo.copy=function(){var t,n,e=new $o(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=qo(r),e;for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=qo(n));return e},Bo.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t);return Do(this.cover(n,e),n,e,t)},Bo.addAll=function(t){var n,e,r,i,o=t.length,a=new Array(o),u=new Array(o),s=1/0,l=1/0,c=-1/0,f=-1/0;for(e=0;e<o;++e)isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n))||(a[e]=r,u[e]=i,r<s&&(s=r),r>c&&(c=r),i<l&&(l=i),i>f&&(f=i));if(s>c||l>f)return this;for(this.cover(s,l).cover(c,f),e=0;e<o;++e)Do(this,a[e],u[e],t[e]);return this},Bo.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var a,u,s=i-e||1,l=this._root;e>t||t>=i||r>n||n>=o;)switch(u=(n<r)<<1|t<e,(a=new Array(4))[u]=l,l=a,s*=2,u){case 0:i=e+s,o=r+s;break;case 1:e=i-s,o=r+s;break;case 2:i=e+s,r=o-s;break;case 3:e=i-s,r=o-s}this._root&&this._root.length&&(this._root=l)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},Bo.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},Bo.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},Bo.find=function(t,n,e){var r,i,o,a,u,s,l,c=this._x0,f=this._y0,h=this._x1,p=this._y1,d=[],y=this._root;for(y&&d.push(new Io(y,c,f,h,p)),null==e?e=1/0:(c=t-e,f=n-e,h=t+e,p=n+e,e*=e);s=d.pop();)if(!(!(y=s.node)||(i=s.x0)>h||(o=s.y0)>p||(a=s.x1)<c||(u=s.y1)<f))if(y.length){var g=(i+a)/2,v=(o+u)/2;d.push(new Io(y[3],g,v,a,u),new Io(y[2],i,v,g,u),new Io(y[1],g,o,a,v),new Io(y[0],i,o,g,v)),(l=(n>=v)<<1|t>=g)&&(s=d[d.length-1],d[d.length-1]=d[d.length-1-l],d[d.length-1-l]=s)}else{var _=t-+this._x.call(null,y.data),m=n-+this._y.call(null,y.data),b=_*_+m*m;if(b<e){var x=Math.sqrt(e=b);c=t-x,f=n-x,h=t+x,p=n+x,r=y.data}}return r},Bo.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c,f,h,p=this._root,d=this._x0,y=this._y0,g=this._x1,v=this._y1;if(!p)return this;if(p.length)for(;;){if((l=o>=(u=(d+g)/2))?d=u:g=u,(c=a>=(s=(y+v)/2))?y=s:v=s,n=p,!(p=p[f=c<<1|l]))return this;if(!p.length)break;(n[f+1&3]||n[f+2&3]||n[f+3&3])&&(e=n,h=f)}for(;p.data!==t;)if(r=p,!(p=p.next))return this;return(i=p.next)&&delete p.next,r?(i?r.next=i:delete r.next,this):n?(i?n[f]=i:delete n[f],(p=n[0]||n[1]||n[2]||n[3])&&p===(n[3]||n[2]||n[1]||n[0])&&!p.length&&(e?e[h]=p:this._root=p),this):(this._root=i,this)},Bo.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},Bo.root=function(){return this._root},Bo.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},Bo.visit=function(t){var n,e,r,i,o,a,u=[],s=this._root;for(s&&u.push(new Io(s,this._x0,this._y0,this._x1,this._y1));n=u.pop();)if(!t(s=n.node,r=n.x0,i=n.y0,o=n.x1,a=n.y1)&&s.length){var l=(r+o)/2,c=(i+a)/2;(e=s[3])&&u.push(new Io(e,l,c,o,a)),(e=s[2])&&u.push(new Io(e,r,c,l,a)),(e=s[1])&&u.push(new Io(e,l,i,o,c)),(e=s[0])&&u.push(new Io(e,r,i,l,c))}return this},Bo.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Io(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,s=n.x1,l=n.y1,c=(a+s)/2,f=(u+l)/2;(o=i[0])&&e.push(new Io(o,a,u,c,f)),(o=i[1])&&e.push(new Io(o,c,u,s,f)),(o=i[2])&&e.push(new Io(o,a,f,c,l)),(o=i[3])&&e.push(new Io(o,c,f,s,l))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},Bo.x=function(t){return arguments.length?(this._x=t,this):this._x},Bo.y=function(t){return arguments.length?(this._y=t,this):this._y};const Go=(t,n,e,r,i,o)=>Math.sqrt((t-r)**2+(n-i)**2+(e-o)**2);function Xo(t){return t[0]}function Wo(t){return t[1]}function Yo(t){return t[2]}function Zo(t,n,e,r){var i=new Qo(null==n?Xo:n,null==e?Wo:e,null==r?Yo:r,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function Qo(t,n,e,r,i,o,a,u,s){this._x=t,this._y=n,this._z=e,this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=u,this._z1=s,this._root=void 0}function Jo(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var Ko=Zo.prototype=Qo.prototype;function ta(t){return function(){return t}}function na(t){return 1e-6*(t()-.5)}function ea(t){return t.index}function ra(t,n){var e=t.get(n);if(!e)throw new Error("node not found: "+n);return e}function ia(t){var n,e,r,i,o,a,u,s=ea,l=function(t){return 1/Math.min(o[t.source.index],o[t.target.index])},c=ta(30),f=1;function h(r){for(var o=0,s=t.length;o<f;++o)for(var l,c,h,p,d,y=0,g=0,v=0,_=0;y<s;++y)c=(l=t[y]).source,g=(h=l.target).x+h.vx-c.x-c.vx||na(u),i>1&&(v=h.y+h.vy-c.y-c.vy||na(u)),i>2&&(_=h.z+h.vz-c.z-c.vz||na(u)),g*=p=((p=Math.sqrt(g*g+v*v+_*_))-e[y])/p*r*n[y],v*=p,_*=p,h.vx-=g*(d=a[y]),i>1&&(h.vy-=v*d),i>2&&(h.vz-=_*d),c.vx+=g*(d=1-d),i>1&&(c.vy+=v*d),i>2&&(c.vz+=_*d)}function p(){if(r){var i,u,l=r.length,c=t.length,f=new Map(r.map((t,n)=>[s(t,n,r),t]));for(i=0,o=new Array(l);i<c;++i)(u=t[i]).index=i,"object"!=typeof u.source&&(u.source=ra(f,u.source)),"object"!=typeof u.target&&(u.target=ra(f,u.target)),o[u.source.index]=(o[u.source.index]||0)+1,o[u.target.index]=(o[u.target.index]||0)+1;for(i=0,a=new Array(c);i<c;++i)u=t[i],a[i]=o[u.source.index]/(o[u.source.index]+o[u.target.index]);n=new Array(c),d(),e=new Array(c),y()}}function d(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+l(t[e],e,t)}function y(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+c(t[n],n,t)}return null==t&&(t=[]),h.initialize=function(t,...n){r=t,u=n.find(t=>"function"==typeof t)||Math.random,i=n.find(t=>[1,2,3].includes(t))||2,p()},h.links=function(n){return arguments.length?(t=n,p(),h):t},h.id=function(t){return arguments.length?(s=t,h):s},h.iterations=function(t){return arguments.length?(f=+t,h):f},h.strength=function(t){return arguments.length?(l="function"==typeof t?t:ta(+t),d(),h):l},h.distance=function(t){return arguments.length?(c="function"==typeof t?t:ta(+t),y(),h):c},h}Ko.copy=function(){var t,n,e=new Qo(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),r=this._root;if(!r)return e;if(!r.length)return e._root=Jo(r),e;for(t=[{source:r,target:e._root=new Array(8)}];r=t.pop();)for(var i=0;i<8;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(8)}):r.target[i]=Jo(n));return e},Ko.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t),r=+this._z.call(null,t);return Ho(this.cover(n,e,r),n,e,r,t)},Ko.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n),r=new Float64Array(n),i=new Float64Array(n);let o=1/0,a=1/0,u=1/0,s=-1/0,l=-1/0,c=-1/0;for(let f,h,p,d,y=0;y<n;++y)isNaN(h=+this._x.call(null,f=t[y]))||isNaN(p=+this._y.call(null,f))||isNaN(d=+this._z.call(null,f))||(e[y]=h,r[y]=p,i[y]=d,h<o&&(o=h),h>s&&(s=h),p<a&&(a=p),p>l&&(l=p),d<u&&(u=d),d>c&&(c=d));if(o>s||a>l||u>c)return this;this.cover(o,a,u).cover(s,l,c);for(let o=0;o<n;++o)Ho(this,e[o],r[o],i[o],t[o]);return this},Ko.cover=function(t,n,e){if(isNaN(t=+t)||isNaN(n=+n)||isNaN(e=+e))return this;var r=this._x0,i=this._y0,o=this._z0,a=this._x1,u=this._y1,s=this._z1;if(isNaN(r))a=(r=Math.floor(t))+1,u=(i=Math.floor(n))+1,s=(o=Math.floor(e))+1;else{for(var l,c,f=a-r||1,h=this._root;r>t||t>=a||i>n||n>=u||o>e||e>=s;)switch(c=(e<o)<<2|(n<i)<<1|t<r,(l=new Array(8))[c]=h,h=l,f*=2,c){case 0:a=r+f,u=i+f,s=o+f;break;case 1:r=a-f,u=i+f,s=o+f;break;case 2:a=r+f,i=u-f,s=o+f;break;case 3:r=a-f,i=u-f,s=o+f;break;case 4:a=r+f,u=i+f,o=s-f;break;case 5:r=a-f,u=i+f,o=s-f;break;case 6:a=r+f,i=u-f,o=s-f;break;case 7:r=a-f,i=u-f,o=s-f}this._root&&this._root.length&&(this._root=h)}return this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=u,this._z1=s,this},Ko.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},Ko.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},Ko.find=function(t,n,e,r){var i,o,a,u,s,l,c,f,h,p=this._x0,d=this._y0,y=this._z0,g=this._x1,v=this._y1,_=this._z1,m=[],b=this._root;for(b&&m.push(new Vo(b,p,d,y,g,v,_)),null==r?r=1/0:(p=t-r,d=n-r,y=e-r,g=t+r,v=n+r,_=e+r,r*=r);f=m.pop();)if(!(!(b=f.node)||(o=f.x0)>g||(a=f.y0)>v||(u=f.z0)>_||(s=f.x1)<p||(l=f.y1)<d||(c=f.z1)<y))if(b.length){var x=(o+s)/2,w=(a+l)/2,k=(u+c)/2;m.push(new Vo(b[7],x,w,k,s,l,c),new Vo(b[6],o,w,k,x,l,c),new Vo(b[5],x,a,k,s,w,c),new Vo(b[4],o,a,k,x,w,c),new Vo(b[3],x,w,u,s,l,k),new Vo(b[2],o,w,u,x,l,k),new Vo(b[1],x,a,u,s,w,k),new Vo(b[0],o,a,u,x,w,k)),(h=(e>=k)<<2|(n>=w)<<1|t>=x)&&(f=m[m.length-1],m[m.length-1]=m[m.length-1-h],m[m.length-1-h]=f)}else{var S=t-+this._x.call(null,b.data),A=n-+this._y.call(null,b.data),M=e-+this._z.call(null,b.data),O=S*S+A*A+M*M;if(O<r){var z=Math.sqrt(r=O);p=t-z,d=n-z,y=e-z,g=t+z,v=n+z,_=e+z,i=b.data}}return i},Ko.findAllWithinRadius=function(t,n,e,r){const i=[],o=t-r,a=n-r,u=e-r,s=t+r,l=n+r,c=e+r;return this.visit((f,h,p,d,y,g,v)=>{if(!f.length)do{const o=f.data;Go(t,n,e,this._x(o),this._y(o),this._z(o))<=r&&i.push(o)}while(f=f.next);return h>s||p>l||d>c||y<o||g<a||v<u}),i},Ko.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t))||isNaN(u=+this._z.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c,f,h,p,d,y,g=this._root,v=this._x0,_=this._y0,m=this._z0,b=this._x1,x=this._y1,w=this._z1;if(!g)return this;if(g.length)for(;;){if((f=o>=(s=(v+b)/2))?v=s:b=s,(h=a>=(l=(_+x)/2))?_=l:x=l,(p=u>=(c=(m+w)/2))?m=c:w=c,n=g,!(g=g[d=p<<2|h<<1|f]))return this;if(!g.length)break;(n[d+1&7]||n[d+2&7]||n[d+3&7]||n[d+4&7]||n[d+5&7]||n[d+6&7]||n[d+7&7])&&(e=n,y=d)}for(;g.data!==t;)if(r=g,!(g=g.next))return this;return(i=g.next)&&delete g.next,r?(i?r.next=i:delete r.next,this):n?(i?n[d]=i:delete n[d],(g=n[0]||n[1]||n[2]||n[3]||n[4]||n[5]||n[6]||n[7])&&g===(n[7]||n[6]||n[5]||n[4]||n[3]||n[2]||n[1]||n[0])&&!g.length&&(e?e[y]=g:this._root=g),this):(this._root=i,this)},Ko.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},Ko.root=function(){return this._root},Ko.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},Ko.visit=function(t){var n,e,r,i,o,a,u,s,l=[],c=this._root;for(c&&l.push(new Vo(c,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=l.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.z0,a=n.x1,u=n.y1,s=n.z1)&&c.length){var f=(r+a)/2,h=(i+u)/2,p=(o+s)/2;(e=c[7])&&l.push(new Vo(e,f,h,p,a,u,s)),(e=c[6])&&l.push(new Vo(e,r,h,p,f,u,s)),(e=c[5])&&l.push(new Vo(e,f,i,p,a,h,s)),(e=c[4])&&l.push(new Vo(e,r,i,p,f,h,s)),(e=c[3])&&l.push(new Vo(e,f,h,o,a,u,p)),(e=c[2])&&l.push(new Vo(e,r,h,o,f,u,p)),(e=c[1])&&l.push(new Vo(e,f,i,o,a,h,p)),(e=c[0])&&l.push(new Vo(e,r,i,o,f,h,p))}return this},Ko.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Vo(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,s=n.z0,l=n.x1,c=n.y1,f=n.z1,h=(a+l)/2,p=(u+c)/2,d=(s+f)/2;(o=i[0])&&e.push(new Vo(o,a,u,s,h,p,d)),(o=i[1])&&e.push(new Vo(o,h,u,s,l,p,d)),(o=i[2])&&e.push(new Vo(o,a,p,s,h,c,d)),(o=i[3])&&e.push(new Vo(o,h,p,s,l,c,d)),(o=i[4])&&e.push(new Vo(o,a,u,d,h,p,f)),(o=i[5])&&e.push(new Vo(o,h,u,d,l,p,f)),(o=i[6])&&e.push(new Vo(o,a,p,d,h,c,f)),(o=i[7])&&e.push(new Vo(o,h,p,d,l,c,f))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.z0,n.x1,n.y1,n.z1);return this},Ko.x=function(t){return arguments.length?(this._x=t,this):this._x},Ko.y=function(t){return arguments.length?(this._y=t,this):this._y},Ko.z=function(t){return arguments.length?(this._z=t,this):this._z};const oa=4294967296;function aa(t){return t.x}function ua(t){return t.y}function sa(t){return t.z}var la=Math.PI*(3-Math.sqrt(5)),ca=20*Math.PI/(9+Math.sqrt(221));function fa(t,n){n=n||2;var e,r=Math.min(3,Math.max(1,Math.round(n))),i=1,o=.001,a=1-Math.pow(o,1/300),u=0,s=.6,l=new Map,c=te(p),f=Ot("tick","end"),h=function(){let t=1;return()=>(t=(1664525*t+1013904223)%oa)/oa}();function p(){d(),f.call("tick",e),i<o&&(c.stop(),f.call("end",e))}function d(n){var o,c,f=t.length;void 0===n&&(n=1);for(var h=0;h<n;++h)for(i+=(u-i)*a,l.forEach(function(t){t(i)}),o=0;o<f;++o)null==(c=t[o]).fx?c.x+=c.vx*=s:(c.x=c.fx,c.vx=0),r>1&&(null==c.fy?c.y+=c.vy*=s:(c.y=c.fy,c.vy=0)),r>2&&(null==c.fz?c.z+=c.vz*=s:(c.z=c.fz,c.vz=0));return e}function y(){for(var n,e=0,i=t.length;e<i;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),null!=n.fz&&(n.z=n.fz),isNaN(n.x)||r>1&&isNaN(n.y)||r>2&&isNaN(n.z)){var o=10*(r>2?Math.cbrt(.5+e):r>1?Math.sqrt(.5+e):e),a=e*la,u=e*ca;1===r?n.x=o:2===r?(n.x=o*Math.cos(a),n.y=o*Math.sin(a)):(n.x=o*Math.sin(a)*Math.cos(u),n.y=o*Math.cos(a),n.z=o*Math.sin(a)*Math.sin(u))}(isNaN(n.vx)||r>1&&isNaN(n.vy)||r>2&&isNaN(n.vz))&&(n.vx=0,r>1&&(n.vy=0),r>2&&(n.vz=0))}}function g(n){return n.initialize&&n.initialize(t,h,r),n}return null==t&&(t=[]),y(),e={tick:d,restart:function(){return c.restart(p),e},stop:function(){return c.stop(),e},numDimensions:function(t){return arguments.length?(r=Math.min(3,Math.max(1,Math.round(t))),l.forEach(g),e):r},nodes:function(n){return arguments.length?(t=n,y(),l.forEach(g),e):t},alpha:function(t){return arguments.length?(i=+t,e):i},alphaMin:function(t){return arguments.length?(o=+t,e):o},alphaDecay:function(t){return arguments.length?(a=+t,e):+a},alphaTarget:function(t){return arguments.length?(u=+t,e):u},velocityDecay:function(t){return arguments.length?(s=1-t,e):1-s},randomSource:function(t){return arguments.length?(h=t,l.forEach(g),e):h},force:function(t,n){return arguments.length>1?(null==n?l.delete(t):l.set(t,g(n)),e):l.get(t)},find:function(){var n,e,i,o,a,u,s=Array.prototype.slice.call(arguments),l=s.shift()||0,c=(r>1?s.shift():null)||0,f=(r>2?s.shift():null)||0,h=s.shift()||1/0,p=0,d=t.length;for(h*=h,p=0;p<d;++p)(o=(n=l-(a=t[p]).x)*n+(e=c-(a.y||0))*e+(i=f-(a.z||0))*i)<h&&(u=a,h=o);return u},on:function(t,n){return arguments.length>1?(f.on(t,n),e):f.on(t)}}}function ha(){var t,n,e,r,i,o,a=ta(-30),u=1,s=1/0,l=.81;function c(r){var o,a=t.length,u=(1===n?jo(t,aa):2===n?Lo(t,aa,ua):3===n?Zo(t,aa,ua,sa):null).visitAfter(h);for(i=r,o=0;o<a;++o)e=t[o],u.visit(p)}function f(){if(t){var n,e,r=t.length;for(o=new Array(r),n=0;n<r;++n)e=t[n],o[e.index]=+a(e,n,t)}}function h(t){var e,r,i,a,u,s,l=0,c=0,f=t.length;if(f){for(i=a=u=s=0;s<f;++s)(e=t[s])&&(r=Math.abs(e.value))&&(l+=e.value,c+=r,i+=r*(e.x||0),a+=r*(e.y||0),u+=r*(e.z||0));l*=Math.sqrt(4/f),t.x=i/c,n>1&&(t.y=a/c),n>2&&(t.z=u/c)}else{(e=t).x=e.data.x,n>1&&(e.y=e.data.y),n>2&&(e.z=e.data.z);do{l+=o[e.data.index]}while(e=e.next)}t.value=l}function p(t,a,c,f,h){if(!t.value)return!0;var p=[c,f,h][n-1],d=t.x-e.x,y=n>1?t.y-e.y:0,g=n>2?t.z-e.z:0,v=p-a,_=d*d+y*y+g*g;if(v*v/l<_)return _<s&&(0===d&&(_+=(d=na(r))*d),n>1&&0===y&&(_+=(y=na(r))*y),n>2&&0===g&&(_+=(g=na(r))*g),_<u&&(_=Math.sqrt(u*_)),e.vx+=d*t.value*i/_,n>1&&(e.vy+=y*t.value*i/_),n>2&&(e.vz+=g*t.value*i/_)),!0;if(!(t.length||_>=s)){(t.data!==e||t.next)&&(0===d&&(_+=(d=na(r))*d),n>1&&0===y&&(_+=(y=na(r))*y),n>2&&0===g&&(_+=(g=na(r))*g),_<u&&(_=Math.sqrt(u*_)));do{t.data!==e&&(v=o[t.data.index]*i/_,e.vx+=d*v,n>1&&(e.vy+=y*v),n>2&&(e.vz+=g*v))}while(t=t.next)}}return c.initialize=function(e,...i){t=e,r=i.find(t=>"function"==typeof t)||Math.random,n=i.find(t=>[1,2,3].includes(t))||2,f()},c.strength=function(t){return arguments.length?(a="function"==typeof t?t:ta(+t),f(),c):a},c.distanceMin=function(t){return arguments.length?(u=t*t,c):Math.sqrt(u)},c.distanceMax=function(t){return arguments.length?(s=t*t,c):Math.sqrt(s)},c.theta=function(t){return arguments.length?(l=t*t,c):Math.sqrt(l)},c}const{abs:pa,cos:da,sin:ya,acos:ga,atan2:va,sqrt:_a,pow:ma}=Math;function ba(t){return t<0?-ma(-t,1/3):ma(t,1/3)}const xa=Math.PI,wa=2*xa,ka=xa/2,Sa=Number.MAX_SAFE_INTEGER||9007199254740991,Aa=Number.MIN_SAFE_INTEGER||-9007199254740991,Ma={x:0,y:0,z:0},Oa={Tvalues:[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213],Cvalues:[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872],arcfn:function(t,n){const e=n(t);let r=e.x*e.x+e.y*e.y;return void 0!==e.z&&(r+=e.z*e.z),_a(r)},compute:function(t,n,e){if(0===t)return n[0].t=0,n[0];const r=n.length-1;if(1===t)return n[r].t=1,n[r];const i=1-t;let o=n;if(0===r)return n[0].t=t,n[0];if(1===r){const n={x:i*o[0].x+t*o[1].x,y:i*o[0].y+t*o[1].y,t:t};return e&&(n.z=i*o[0].z+t*o[1].z),n}if(r<4){let n,a,u,s=i*i,l=t*t,c=0;2===r?(o=[o[0],o[1],o[2],Ma],n=s,a=i*t*2,u=l):3===r&&(n=s*i,a=s*t*3,u=i*l*3,c=t*l);const f={x:n*o[0].x+a*o[1].x+u*o[2].x+c*o[3].x,y:n*o[0].y+a*o[1].y+u*o[2].y+c*o[3].y,t:t};return e&&(f.z=n*o[0].z+a*o[1].z+u*o[2].z+c*o[3].z),f}const a=JSON.parse(JSON.stringify(n));for(;a.length>1;){for(let n=0;n<a.length-1;n++)a[n]={x:a[n].x+(a[n+1].x-a[n].x)*t,y:a[n].y+(a[n+1].y-a[n].y)*t},void 0!==a[n].z&&(a[n].z=a[n].z+(a[n+1].z-a[n].z)*t);a.splice(a.length-1,1)}return a[0].t=t,a[0]},computeWithRatios:function(t,n,e,r){const i=1-t,o=e,a=n;let u,s=o[0],l=o[1],c=o[2],f=o[3];return s*=i,l*=t,2===a.length?(u=s+l,{x:(s*a[0].x+l*a[1].x)/u,y:(s*a[0].y+l*a[1].y)/u,z:!!r&&(s*a[0].z+l*a[1].z)/u,t:t}):(s*=i,l*=2*i,c*=t*t,3===a.length?(u=s+l+c,{x:(s*a[0].x+l*a[1].x+c*a[2].x)/u,y:(s*a[0].y+l*a[1].y+c*a[2].y)/u,z:!!r&&(s*a[0].z+l*a[1].z+c*a[2].z)/u,t:t}):(s*=i,l*=1.5*i,c*=3*i,f*=t*t*t,4===a.length?(u=s+l+c+f,{x:(s*a[0].x+l*a[1].x+c*a[2].x+f*a[3].x)/u,y:(s*a[0].y+l*a[1].y+c*a[2].y+f*a[3].y)/u,z:!!r&&(s*a[0].z+l*a[1].z+c*a[2].z+f*a[3].z)/u,t:t}):void 0))},derive:function(t,n){const e=[];for(let r=t,i=r.length,o=i-1;i>1;i--,o--){const t=[];for(let e,i=0;i<o;i++)e={x:o*(r[i+1].x-r[i].x),y:o*(r[i+1].y-r[i].y)},n&&(e.z=o*(r[i+1].z-r[i].z)),t.push(e);e.push(t),r=t}return e},between:function(t,n,e){return n<=t&&t<=e||Oa.approximately(t,n)||Oa.approximately(t,e)},approximately:function(t,n,e){return pa(t-n)<=(e||1e-6)},length:function(t){const n=Oa.Tvalues.length;let e=0;for(let r,i=0;i<n;i++)r=.5*Oa.Tvalues[i]+.5,e+=Oa.Cvalues[i]*Oa.arcfn(r,t);return.5*e},map:function(t,n,e,r,i){return r+(i-r)*((t-n)/(e-n))},lerp:function(t,n,e){const r={x:n.x+t*(e.x-n.x),y:n.y+t*(e.y-n.y)};return void 0!==n.z&&void 0!==e.z&&(r.z=n.z+t*(e.z-n.z)),r},pointToString:function(t){let n=t.x+"/"+t.y;return void 0!==t.z&&(n+="/"+t.z),n},pointsToString:function(t){return"["+t.map(Oa.pointToString).join(", ")+"]"},copy:function(t){return JSON.parse(JSON.stringify(t))},angle:function(t,n,e){const r=n.x-t.x,i=n.y-t.y,o=e.x-t.x,a=e.y-t.y;return va(r*a-i*o,r*o+i*a)},round:function(t,n){const e=""+t,r=e.indexOf(".");return parseFloat(e.substring(0,r+1+n))},dist:function(t,n){const e=t.x-n.x,r=t.y-n.y;return _a(e*e+r*r)},closest:function(t,n){let e,r,i=ma(2,63);return t.forEach(function(t,o){r=Oa.dist(n,t),r<i&&(i=r,e=o)}),{mdist:i,mpos:e}},abcratio:function(t,n){if(2!==n&&3!==n)return!1;if(void 0===t)t=.5;else if(0===t||1===t)return t;const e=ma(t,n)+ma(1-t,n);return pa((e-1)/e)},projectionratio:function(t,n){if(2!==n&&3!==n)return!1;if(void 0===t)t=.5;else if(0===t||1===t)return t;const e=ma(1-t,n);return e/(ma(t,n)+e)},lli8:function(t,n,e,r,i,o,a,u){const s=(t-e)*(o-u)-(n-r)*(i-a);return 0!=s&&{x:((t*r-n*e)*(i-a)-(t-e)*(i*u-o*a))/s,y:((t*r-n*e)*(o-u)-(n-r)*(i*u-o*a))/s}},lli4:function(t,n,e,r){const i=t.x,o=t.y,a=n.x,u=n.y,s=e.x,l=e.y,c=r.x,f=r.y;return Oa.lli8(i,o,a,u,s,l,c,f)},lli:function(t,n){return Oa.lli4(t,t.c,n,n.c)},makeline:function(t,n){return new Ia(t.x,t.y,(t.x+n.x)/2,(t.y+n.y)/2,n.x,n.y)},findbbox:function(t){let n=Sa,e=Sa,r=Aa,i=Aa;return t.forEach(function(t){const o=t.bbox();n>o.x.min&&(n=o.x.min),e>o.y.min&&(e=o.y.min),r<o.x.max&&(r=o.x.max),i<o.y.max&&(i=o.y.max)}),{x:{min:n,mid:(n+r)/2,max:r,size:r-n},y:{min:e,mid:(e+i)/2,max:i,size:i-e}}},shapeintersections:function(t,n,e,r,i){if(!Oa.bboxoverlap(n,r))return[];const o=[],a=[t.startcap,t.forward,t.back,t.endcap],u=[e.startcap,e.forward,e.back,e.endcap];return a.forEach(function(n){n.virtual||u.forEach(function(r){if(r.virtual)return;const a=n.intersects(r,i);a.length>0&&(a.c1=n,a.c2=r,a.s1=t,a.s2=e,o.push(a))})}),o},makeshape:function(t,n,e){const r=n.points.length,i=t.points.length,o=Oa.makeline(n.points[r-1],t.points[0]),a=Oa.makeline(t.points[i-1],n.points[0]),u={startcap:o,forward:t,back:n,endcap:a,bbox:Oa.findbbox([o,t,n,a]),intersections:function(t){return Oa.shapeintersections(u,u.bbox,t,t.bbox,e)}};return u},getminmax:function(t,n,e){if(!e)return{min:0,max:0};let r,i,o=Sa,a=Aa;-1===e.indexOf(0)&&(e=[0].concat(e)),-1===e.indexOf(1)&&e.push(1);for(let u=0,s=e.length;u<s;u++)r=e[u],i=t.get(r),i[n]<o&&(o=i[n]),i[n]>a&&(a=i[n]);return{min:o,mid:(o+a)/2,max:a,size:a-o}},align:function(t,n){const e=n.p1.x,r=n.p1.y,i=-va(n.p2.y-r,n.p2.x-e);return t.map(function(t){return{x:(t.x-e)*da(i)-(t.y-r)*ya(i),y:(t.x-e)*ya(i)+(t.y-r)*da(i)}})},roots:function(t,n){n=n||{p1:{x:0,y:0},p2:{x:1,y:0}};const e=t.length-1,r=Oa.align(t,n),i=function(t){return 0<=t&&t<=1};if(2===e){const t=r[0].y,n=r[1].y,e=r[2].y,o=t-2*n+e;if(0!==o){const r=-_a(n*n-t*e),a=-t+n;return[-(r+a)/o,-(-r+a)/o].filter(i)}return n!==e&&0===o?[(2*n-e)/(2*n-2*e)].filter(i):[]}const o=r[0].y,a=r[1].y,u=r[2].y;let s=3*a-o-3*u+r[3].y,l=3*o-6*a+3*u,c=-3*o+3*a,f=o;if(Oa.approximately(s,0)){if(Oa.approximately(l,0))return Oa.approximately(c,0)?[]:[-f/c].filter(i);const t=_a(c*c-4*l*f),n=2*l;return[(t-c)/n,(-c-t)/n].filter(i)}l/=s,c/=s,f/=s;const h=(3*c-l*l)/3,p=h/3,d=(2*l*l*l-9*l*c+27*f)/27,y=d/2,g=y*y+p*p*p;let v,_,m,b,x;if(g<0){const t=-h/3,n=_a(t*t*t),e=-d/(2*n),r=ga(e<-1?-1:e>1?1:e),o=2*ba(n);return m=o*da(r/3)-l/3,b=o*da((r+wa)/3)-l/3,x=o*da((r+2*wa)/3)-l/3,[m,b,x].filter(i)}if(0===g)return v=y<0?ba(-y):-ba(y),m=2*v-l/3,b=-v-l/3,[m,b].filter(i);{const t=_a(g);return v=ba(-y+t),_=ba(y+t),[v-_-l/3].filter(i)}},droots:function(t){if(3===t.length){const n=t[0],e=t[1],r=t[2],i=n-2*e+r;if(0!==i){const t=-_a(e*e-n*r),o=-n+e;return[-(t+o)/i,-(-t+o)/i]}return e!==r&&0===i?[(2*e-r)/(2*(e-r))]:[]}if(2===t.length){const n=t[0],e=t[1];return n!==e?[n/(n-e)]:[]}return[]},curvature:function(t,n,e,r,i){let o,a,u,s,l=0,c=0;const f=Oa.compute(t,n),h=Oa.compute(t,e),p=f.x*f.x+f.y*f.y;if(r?(o=_a(ma(f.y*h.z-h.y*f.z,2)+ma(f.z*h.x-h.z*f.x,2)+ma(f.x*h.y-h.x*f.y,2)),a=ma(p+f.z*f.z,1.5)):(o=f.x*h.y-f.y*h.x,a=ma(p,1.5)),0===o||0===a)return{k:0,r:0};if(l=o/a,c=a/o,!i){const i=Oa.curvature(t-.001,n,e,r,!0).k,o=Oa.curvature(t+.001,n,e,r,!0).k;s=(o-l+(l-i))/2,u=(pa(o-l)+pa(l-i))/2}return{k:l,r:c,dk:s,adk:u}},inflections:function(t){if(t.length<4)return[];const n=Oa.align(t,{p1:t[0],p2:t.slice(-1)[0]}),e=n[2].x*n[1].y,r=n[3].x*n[1].y,i=n[1].x*n[2].y,o=18*(-3*e+2*r+3*i-n[3].x*n[2].y),a=18*(3*e-r-3*i),u=18*(i-e);if(Oa.approximately(o,0)){if(!Oa.approximately(a,0)){let t=-u/a;if(0<=t&&t<=1)return[t]}return[]}const s=2*o;if(Oa.approximately(s,0))return[];const l=a*a-4*o*u;if(l<0)return[];const c=Math.sqrt(l);return[(c-a)/s,-(a+c)/s].filter(function(t){return 0<=t&&t<=1})},bboxoverlap:function(t,n){const e=["x","y"],r=e.length;for(let i,o,a,u,s=0;s<r;s++)if(i=e[s],o=t[i].mid,a=n[i].mid,u=(t[i].size+n[i].size)/2,pa(o-a)>=u)return!1;return!0},expandbox:function(t,n){n.x.min<t.x.min&&(t.x.min=n.x.min),n.y.min<t.y.min&&(t.y.min=n.y.min),n.z&&n.z.min<t.z.min&&(t.z.min=n.z.min),n.x.max>t.x.max&&(t.x.max=n.x.max),n.y.max>t.y.max&&(t.y.max=n.y.max),n.z&&n.z.max>t.z.max&&(t.z.max=n.z.max),t.x.mid=(t.x.min+t.x.max)/2,t.y.mid=(t.y.min+t.y.max)/2,t.z&&(t.z.mid=(t.z.min+t.z.max)/2),t.x.size=t.x.max-t.x.min,t.y.size=t.y.max-t.y.min,t.z&&(t.z.size=t.z.max-t.z.min)},pairiteration:function(t,n,e){const r=t.bbox(),i=n.bbox(),o=1e5,a=e||.5;if(r.x.size+r.y.size<a&&i.x.size+i.y.size<a)return[(o*(t._t1+t._t2)/2|0)/o+"/"+(o*(n._t1+n._t2)/2|0)/o];let u=t.split(.5),s=n.split(.5),l=[{left:u.left,right:s.left},{left:u.left,right:s.right},{left:u.right,right:s.right},{left:u.right,right:s.left}];l=l.filter(function(t){return Oa.bboxoverlap(t.left.bbox(),t.right.bbox())});let c=[];return 0===l.length||(l.forEach(function(t){c=c.concat(Oa.pairiteration(t.left,t.right,a))}),c=c.filter(function(t,n){return c.indexOf(t)===n})),c},getccenter:function(t,n,e){const r=n.x-t.x,i=n.y-t.y,o=e.x-n.x,a=e.y-n.y,u=r*da(ka)-i*ya(ka),s=r*ya(ka)+i*da(ka),l=o*da(ka)-a*ya(ka),c=o*ya(ka)+a*da(ka),f=(t.x+n.x)/2,h=(t.y+n.y)/2,p=(n.x+e.x)/2,d=(n.y+e.y)/2,y=f+u,g=h+s,v=p+l,_=d+c,m=Oa.lli8(f,h,y,g,p,d,v,_),b=Oa.dist(m,t);let x,w=va(t.y-m.y,t.x-m.x),k=va(n.y-m.y,n.x-m.x),S=va(e.y-m.y,e.x-m.x);return w<S?((w>k||k>S)&&(w+=wa),w>S&&(x=S,S=w,w=x)):S<k&&k<w?(x=S,S=w,w=x):S+=wa,m.s=w,m.e=S,m.r=b,m},numberSort:function(t,n){return t-n}};class za{constructor(t){this.curves=[],this._3d=!1,t&&(this.curves=t,this._3d=this.curves[0]._3d)}valueOf(){return this.toString()}toString(){return"["+this.curves.map(function(t){return Oa.pointsToString(t.points)}).join(", ")+"]"}addCurve(t){this.curves.push(t),this._3d=this._3d||t._3d}length(){return this.curves.map(function(t){return t.length()}).reduce(function(t,n){return t+n})}curve(t){return this.curves[t]}bbox(){const t=this.curves;for(var n=t[0].bbox(),e=1;e<t.length;e++)Oa.expandbox(n,t[e].bbox());return n}offset(t){const n=[];return this.curves.forEach(function(e){n.push(...e.offset(t))}),new za(n)}}const{abs:Ea,min:Ca,max:Pa,cos:ja,sin:Ta,acos:Na,sqrt:Ra}=Math,Da=Math.PI;class Ia{constructor(t){let n=t&&t.forEach?t:Array.from(arguments).slice(),e=!1;if("object"==typeof n[0]){e=n.length;const t=[];n.forEach(function(n){["x","y","z"].forEach(function(e){void 0!==n[e]&&t.push(n[e])})}),n=t}let r=!1;const i=n.length;if(e){if(e>4){if(1!==arguments.length)throw new Error("Only new Bezier(point[]) is accepted for 4th and higher order curves");r=!0}}else if(6!==i&&8!==i&&9!==i&&12!==i&&1!==arguments.length)throw new Error("Only new Bezier(point[]) is accepted for 4th and higher order curves");const o=this._3d=!r&&(9===i||12===i)||t&&t[0]&&void 0!==t[0].z,a=this.points=[];for(let t=0,e=o?3:2;t<i;t+=e){var u={x:n[t],y:n[t+1]};o&&(u.z=n[t+2]),a.push(u)}const s=this.order=a.length-1,l=this.dims=["x","y"];o&&l.push("z"),this.dimlen=l.length;const c=Oa.align(a,{p1:a[0],p2:a[s]}),f=Oa.dist(a[0],a[s]);this._linear=c.reduce((t,n)=>t+Ea(n.y),0)<f/50,this._lut=[],this._t1=0,this._t2=1,this.update()}static quadraticFromPoints(t,n,e,r){if(void 0===r&&(r=.5),0===r)return new Ia(n,n,e);if(1===r)return new Ia(t,n,n);const i=Ia.getABC(2,t,n,e,r);return new Ia(t,i.A,e)}static cubicFromPoints(t,n,e,r,i){void 0===r&&(r=.5);const o=Ia.getABC(3,t,n,e,r);void 0===i&&(i=Oa.dist(n,o.C));const a=i*(1-r)/r,u=Oa.dist(t,e),s=(e.x-t.x)/u,l=(e.y-t.y)/u,c=i*s,f=i*l,h=a*s,p=a*l,d=n.x-c,y=n.y-f,g=n.x+h,v=n.y+p,_=o.A,m=_.x+(d-_.x)/(1-r),b=_.y+(y-_.y)/(1-r),x=_.x+(g-_.x)/r,w=_.y+(v-_.y)/r,k={x:t.x+(m-t.x)/r,y:t.y+(b-t.y)/r},S={x:e.x+(x-e.x)/(1-r),y:e.y+(w-e.y)/(1-r)};return new Ia(t,k,S,e)}static getUtils(){return Oa}getUtils(){return Ia.getUtils()}static get PolyBezier(){return za}valueOf(){return this.toString()}toString(){return Oa.pointsToString(this.points)}toSVG(){if(this._3d)return!1;const t=this.points,n=["M",t[0].x,t[0].y,2===this.order?"Q":"C"];for(let e=1,r=t.length;e<r;e++)n.push(t[e].x),n.push(t[e].y);return n.join(" ")}setRatios(t){if(t.length!==this.points.length)throw new Error("incorrect number of ratio values");this.ratios=t,this._lut=[]}verify(){const t=this.coordDigest();t!==this._print&&(this._print=t,this.update())}coordDigest(){return this.points.map(function(t,n){return""+n+t.x+t.y+(t.z?t.z:0)}).join("")}update(){this._lut=[],this.dpoints=Oa.derive(this.points,this._3d),this.computedirection()}computedirection(){const t=this.points,n=Oa.angle(t[0],t[this.order],t[1]);this.clockwise=n>0}length(){return Oa.length(this.derivative.bind(this))}static getABC(t=2,n,e,r,i=.5){const o=Oa.projectionratio(i,t),a=1-o,u={x:o*n.x+a*r.x,y:o*n.y+a*r.y},s=Oa.abcratio(i,t);return{A:{x:e.x+(e.x-u.x)/s,y:e.y+(e.y-u.y)/s},B:e,C:u,S:n,E:r}}getABC(t,n){n=n||this.get(t);let e=this.points[0],r=this.points[this.order];return Ia.getABC(this.order,e,n,r,t)}getLUT(t){if(this.verify(),t=t||100,this._lut.length===t+1)return this._lut;this._lut=[],t++,this._lut=[];for(let n,e,r=0;r<t;r++)e=r/(t-1),n=this.compute(e),n.t=e,this._lut.push(n);return this._lut}on(n,e){e=e||5;const r=this.getLUT(),i=[];for(let t,o=0,a=0;o<r.length;o++)t=r[o],Oa.dist(t,n)<e&&(i.push(t),a+=o/r.length);return!!i.length&&(t/=i.length)}project(t){const n=this.getLUT(),e=n.length-1,r=Oa.closest(n,t),i=r.mpos,o=(i-1)/e,a=(i+1)/e,u=.1/e;let s,l=r.mdist,c=o,f=c;l+=1;for(let n;c<a+u;c+=u)s=this.compute(c),n=Oa.dist(t,s),n<l&&(l=n,f=c);return f=f<0?0:f>1?1:f,s=this.compute(f),s.t=f,s.d=l,s}get(t){return this.compute(t)}point(t){return this.points[t]}compute(t){return this.ratios?Oa.computeWithRatios(t,this.points,this.ratios,this._3d):Oa.compute(t,this.points,this._3d,this.ratios)}raise(){const t=this.points,n=[t[0]],e=t.length;for(let r,i,o=1;o<e;o++)r=t[o],i=t[o-1],n[o]={x:(e-o)/e*r.x+o/e*i.x,y:(e-o)/e*r.y+o/e*i.y};return n[e]=t[e-1],new Ia(n)}derivative(t){return Oa.compute(t,this.dpoints[0],this._3d)}dderivative(t){return Oa.compute(t,this.dpoints[1],this._3d)}align(){let t=this.points;return new Ia(Oa.align(t,{p1:t[0],p2:t[t.length-1]}))}curvature(t){return Oa.curvature(t,this.dpoints[0],this.dpoints[1],this._3d)}inflections(){return Oa.inflections(this.points)}normal(t){return this._3d?this.__normal3(t):this.__normal2(t)}__normal2(t){const n=this.derivative(t),e=Ra(n.x*n.x+n.y*n.y);return{t:t,x:-n.y/e,y:n.x/e}}__normal3(t){const n=this.derivative(t),e=this.derivative(t+.01),r=Ra(n.x*n.x+n.y*n.y+n.z*n.z),i=Ra(e.x*e.x+e.y*e.y+e.z*e.z);n.x/=r,n.y/=r,n.z/=r,e.x/=i,e.y/=i,e.z/=i;const o={x:e.y*n.z-e.z*n.y,y:e.z*n.x-e.x*n.z,z:e.x*n.y-e.y*n.x},a=Ra(o.x*o.x+o.y*o.y+o.z*o.z);o.x/=a,o.y/=a,o.z/=a;const u=[o.x*o.x,o.x*o.y-o.z,o.x*o.z+o.y,o.x*o.y+o.z,o.y*o.y,o.y*o.z-o.x,o.x*o.z-o.y,o.y*o.z+o.x,o.z*o.z];return{t:t,x:u[0]*n.x+u[1]*n.y+u[2]*n.z,y:u[3]*n.x+u[4]*n.y+u[5]*n.z,z:u[6]*n.x+u[7]*n.y+u[8]*n.z}}hull(t){let n=this.points,e=[],r=[],i=0;for(r[i++]=n[0],r[i++]=n[1],r[i++]=n[2],3===this.order&&(r[i++]=n[3]);n.length>1;){e=[];for(let o,a=0,u=n.length-1;a<u;a++)o=Oa.lerp(t,n[a],n[a+1]),r[i++]=o,e.push(o);n=e}return r}split(t,n){if(0===t&&n)return this.split(n).left;if(1===n)return this.split(t).right;const e=this.hull(t),r={left:2===this.order?new Ia([e[0],e[3],e[5]]):new Ia([e[0],e[4],e[7],e[9]]),right:2===this.order?new Ia([e[5],e[4],e[2]]):new Ia([e[9],e[8],e[6],e[3]]),span:e};return r.left._t1=Oa.map(0,0,1,this._t1,this._t2),r.left._t2=Oa.map(t,0,1,this._t1,this._t2),r.right._t1=Oa.map(t,0,1,this._t1,this._t2),r.right._t2=Oa.map(1,0,1,this._t1,this._t2),n?(n=Oa.map(n,t,1,0,1),r.right.split(n).left):r}extrema(){const t={};let n=[];return this.dims.forEach(function(e){let r=function(t){return t[e]},i=this.dpoints[0].map(r);t[e]=Oa.droots(i),3===this.order&&(i=this.dpoints[1].map(r),t[e]=t[e].concat(Oa.droots(i))),t[e]=t[e].filter(function(t){return t>=0&&t<=1}),n=n.concat(t[e].sort(Oa.numberSort))}.bind(this)),t.values=n.sort(Oa.numberSort).filter(function(t,e){return n.indexOf(t)===e}),t}bbox(){const t=this.extrema(),n={};return this.dims.forEach(function(e){n[e]=Oa.getminmax(this,e,t[e])}.bind(this)),n}overlaps(t){const n=this.bbox(),e=t.bbox();return Oa.bboxoverlap(n,e)}offset(t,n){if(void 0!==n){const e=this.get(t),r=this.normal(t),i={c:e,n:r,x:e.x+r.x*n,y:e.y+r.y*n};return this._3d&&(i.z=e.z+r.z*n),i}if(this._linear){const n=this.normal(0),e=this.points.map(function(e){const r={x:e.x+t*n.x,y:e.y+t*n.y};return e.z&&n.z&&(r.z=e.z+t*n.z),r});return[new Ia(e)]}return this.reduce().map(function(n){return n._linear?n.offset(t)[0]:n.scale(t)})}simple(){if(3===this.order){const t=Oa.angle(this.points[0],this.points[3],this.points[1]),n=Oa.angle(this.points[0],this.points[3],this.points[2]);if(t>0&&n<0||t<0&&n>0)return!1}const t=this.normal(0),n=this.normal(1);let e=t.x*n.x+t.y*n.y;return this._3d&&(e+=t.z*n.z),Ea(Na(e))<Da/3}reduce(){let t,n,e=0,r=0,i=.01,o=[],a=[],u=this.extrema().values;for(-1===u.indexOf(0)&&(u=[0].concat(u)),-1===u.indexOf(1)&&u.push(1),e=u[0],t=1;t<u.length;t++)r=u[t],n=this.split(e,r),n._t1=e,n._t2=r,o.push(n),e=r;return o.forEach(function(t){for(e=0,r=0;r<=1;)for(r=e+i;r<=1.01;r+=i)if(n=t.split(e,r),!n.simple()){if(r-=i,Ea(e-r)<i)return[];n=t.split(e,r),n._t1=Oa.map(e,0,1,t._t1,t._t2),n._t2=Oa.map(r,0,1,t._t1,t._t2),a.push(n),e=r;break}e<1&&(n=t.split(e,1),n._t1=Oa.map(e,0,1,t._t1,t._t2),n._t2=t._t2,a.push(n))}),a}translate(t,n,e){e="number"==typeof e?e:n;const r=this.order;let i=this.points.map((t,i)=>(1-i/r)*n+i/r*e);return new Ia(this.points.map((n,e)=>({x:n.x+t.x*i[e],y:n.y+t.y*i[e]})))}scale(t){const n=this.order;let e=!1;if("function"==typeof t&&(e=t),e&&2===n)return this.raise().scale(e);const r=this.clockwise,i=this.points;if(this._linear)return this.translate(this.normal(0),e?e(0):t,e?e(1):t);const o=e?e(0):t,a=e?e(1):t,u=[this.offset(0,10),this.offset(1,10)],s=[],l=Oa.lli4(u[0],u[0].c,u[1],u[1].c);if(!l)throw new Error("cannot scale this curve. Try reducing it first.");return[0,1].forEach(function(t){const e=s[t*n]=Oa.copy(i[t*n]);e.x+=(t?a:o)*u[t].n.x,e.y+=(t?a:o)*u[t].n.y}),e?([0,1].forEach(function(o){if(2!==n||!o){var a=i[o+1],u={x:a.x-l.x,y:a.y-l.y},c=e?e((o+1)/n):t;e&&!r&&(c=-c);var f=Ra(u.x*u.x+u.y*u.y);u.x/=f,u.y/=f,s[o+1]={x:a.x+c*u.x,y:a.y+c*u.y}}}),new Ia(s)):([0,1].forEach(t=>{if(2===n&&t)return;const e=s[t*n],r=this.derivative(t),o={x:e.x+r.x,y:e.y+r.y};s[t+1]=Oa.lli4(e,o,l,i[t+1])}),new Ia(s))}outline(t,n,e,r){if(n=void 0===n?t:n,this._linear){const i=this.normal(0),o=this.points[0],a=this.points[this.points.length-1];let u,s,l;void 0===e&&(e=t,r=n),u={x:o.x+i.x*t,y:o.y+i.y*t},l={x:a.x+i.x*e,y:a.y+i.y*e},s={x:(u.x+l.x)/2,y:(u.y+l.y)/2};const c=[u,s,l];u={x:o.x-i.x*n,y:o.y-i.y*n},l={x:a.x-i.x*r,y:a.y-i.y*r},s={x:(u.x+l.x)/2,y:(u.y+l.y)/2};const f=[l,s,u],h=Oa.makeline(f[2],c[0]),p=Oa.makeline(c[2],f[0]),d=[h,new Ia(c),p,new Ia(f)];return new za(d)}const i=this.reduce(),o=i.length,a=[];let u,s=[],l=0,c=this.length();const f=void 0!==e&&void 0!==r;function h(t,n,e,r,i){return function(o){const a=r/e,u=(r+i)/e,s=n-t;return Oa.map(o,0,1,t+a*s,t+u*s)}}i.forEach(function(i){const o=i.length();f?(a.push(i.scale(h(t,e,c,l,o))),s.push(i.scale(h(-n,-r,c,l,o)))):(a.push(i.scale(t)),s.push(i.scale(-n))),l+=o}),s=s.map(function(t){return u=t.points,u[3]?t.points=[u[3],u[2],u[1],u[0]]:t.points=[u[2],u[1],u[0]],t}).reverse();const p=a[0].points[0],d=a[o-1].points[a[o-1].points.length-1],y=s[o-1].points[s[o-1].points.length-1],g=s[0].points[0],v=Oa.makeline(y,p),_=Oa.makeline(d,g),m=[v].concat(a).concat([_]).concat(s);return new za(m)}outlineshapes(t,n,e){n=n||t;const r=this.outline(t,n).curves,i=[];for(let t=1,n=r.length;t<n/2;t++){const o=Oa.makeshape(r[t],r[n-t],e);o.startcap.virtual=t>1,o.endcap.virtual=t<n/2-1,i.push(o)}return i}intersects(t,n){return t?t.p1&&t.p2?this.lineIntersects(t):(t instanceof Ia&&(t=t.reduce()),this.curveintersects(this.reduce(),t,n)):this.selfintersects(n)}lineIntersects(t){const n=Ca(t.p1.x,t.p2.x),e=Ca(t.p1.y,t.p2.y),r=Pa(t.p1.x,t.p2.x),i=Pa(t.p1.y,t.p2.y);return Oa.roots(this.points,t).filter(t=>{var o=this.get(t);return Oa.between(o.x,n,r)&&Oa.between(o.y,e,i)})}selfintersects(t){const n=this.reduce(),e=n.length-2,r=[];for(let i,o,a,u=0;u<e;u++)o=n.slice(u,u+1),a=n.slice(u+2),i=this.curveintersects(o,a,t),r.push(...i);return r}curveintersects(t,n,e){const r=[];t.forEach(function(t){n.forEach(function(n){t.overlaps(n)&&r.push({left:t,right:n})})});let i=[];return r.forEach(function(t){const n=Oa.pairiteration(t.left,t.right,e);n.length>0&&(i=i.concat(n))}),i}arcs(t){return t=t||.5,this._iterate(t,[])}_error(t,n,e,r){const i=(r-e)/4,o=this.get(e+i),a=this.get(r-i),u=Oa.dist(t,n),s=Oa.dist(t,o),l=Oa.dist(t,a);return Ea(s-u)+Ea(l-u)}_iterate(t,n){let e,r=0,i=1;do{e=0,i=1;let o,a,u,s,l,c=this.get(r),f=!1,h=!1,p=i,d=1;do{if(h=f,s=u,p=(r+i)/2,o=this.get(p),a=this.get(i),u=Oa.getccenter(c,o,a),u.interval={start:r,end:i},f=this._error(u,c,r,i)<=t,l=h&&!f,l||(d=i),f){if(i>=1){if(u.interval.end=d=1,s=u,i>1){let t={x:u.x+u.r*ja(u.e),y:u.y+u.r*Ta(u.e)};u.e+=Oa.angle({x:u.x,y:u.y},t,this.get(1))}break}i+=(i-r)/2}else i=p}while(!l&&e++<100);if(e>=100)break;s=s||u,n.push(s),r=d}while(i<1);return n}}function Ua(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Fa(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||qa(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function La(t){return function(t){if(Array.isArray(t))return Ua(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||qa(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $a(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof n?n:n+""}function qa(t,n){if(t){if("string"==typeof t)return Ua(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ua(t,n):void 0}}var Ba=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],e=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=(n instanceof Array?n.length?n:[void 0]:[n]).map(function(t){return{keyAccessor:t,isProp:!(t instanceof Function)}}),o=t.reduce(function(t,n){var r=t,o=n;return i.forEach(function(t,n){var a,u=t.keyAccessor;if(t.isProp){var s=o,l=s[u],c=function(t,n){if(null==t)return{};var e,r,i=function(t,n){if(null==t)return{};var e={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(n.includes(r))continue;e[r]=t[r]}return e}(t,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)e=o[r],n.includes(e)||{}.propertyIsEnumerable.call(t,e)&&(i[e]=t[e])}return i}(s,[u].map($a));a=l,o=c}else a=u(o,n);n+1<i.length?(r.hasOwnProperty(a)||(r[a]={}),r=r[a]):e?(r.hasOwnProperty(a)||(r[a]=[]),r[a].push(o)):r[a]=o}),t},{});e instanceof Function&&function t(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;r===i.length?Object.keys(n).forEach(function(t){return n[t]=e(n[t])}):Object.values(n).forEach(function(n){return t(n,r+1)})}(o);var a=o;return r&&(a=[],function t(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.length===i.length?a.push({keys:e,vals:n}):Object.entries(n).forEach(function(n){var r=Fa(n,2),i=r[0],o=r[1];return t(o,[].concat(La(e),[i]))})}(o),n instanceof Array&&0===n.length&&1===a.length&&(a[0].keys=[])),a};function Ha(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}const Va=Symbol("implicit");var Ga=function(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928");function Xa(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Wa(t,n,e){if(Za())return Reflect.construct.apply(null,arguments);var r=[null];return r.push.apply(r,n),new(t.bind.apply(t,r))}function Ya(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function Za(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Za=function(){return!!t})()}function Qa(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),e.push.apply(e,r)}return e}function Ja(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?Qa(Object(e),!0).forEach(function(n){Ya(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Qa(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function Ka(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||eu(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tu(t){return function(t){if(Array.isArray(t))return Xa(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||eu(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nu(t){return nu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nu(t)}function eu(t,n){if(t){if("string"==typeof t)return Xa(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Xa(t,n):void 0}}!function(t,n){void 0===n&&(n={});var e=n.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===e&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".force-graph-container canvas {\n  display: block;\n  user-select: none;\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.force-graph-container .clickable {\n  cursor: pointer;\n}\n\n.force-graph-container .grabbable {\n  cursor: move;\n  cursor: grab;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n}\n\n.force-graph-container .grabbable:active {\n  cursor: grabbing;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n}\n");var ru=function t(){var n=new Ze,e=[],r=[],i=Va;function o(t){let o=n.get(t);if(void 0===o){if(i!==Va)return i;n.set(t,o=e.push(t)-1)}return r[o%r.length]}return o.domain=function(t){if(!arguments.length)return e.slice();e=[],n=new Ze;for(const r of t)n.has(r)||n.set(r,e.push(r)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(i=t,o):i},o.copy=function(){return t(e,r).unknown(i)},Ha.apply(o,arguments),o}(Ga);function iu(t,n,e){n&&"string"==typeof e&&t.filter(function(t){return!t[e]}).forEach(function(t){t[e]=ru(n(t))})}var ou=function(t,n){return n.onNeedsRedraw&&n.onNeedsRedraw()},au=function(t,n){if(!n.isShadow){var e=Ur(n.linkDirectionalParticles);n.graphData.links.forEach(function(t){var n=Math.round(Math.abs(e(t)));n?t.__photons=tu(Array(n)).map(function(){return{}}):delete t.__photons})}},uu=Ir({props:{graphData:{default:{nodes:[],links:[]},onChange:function(t,n){n.engineRunning=!1,au(0,n)}},dagMode:{onChange:function(t,n){!t&&(n.graphData.nodes||[]).forEach(function(t){return t.fx=t.fy=void 0})}},dagLevelDistance:{},dagNodeFilter:{default:function(t){return!0}},onDagError:{triggerUpdate:!1},nodeRelSize:{default:4,triggerUpdate:!1,onChange:ou},nodeId:{default:"id"},nodeVal:{default:"val",triggerUpdate:!1,onChange:ou},nodeColor:{default:"color",triggerUpdate:!1,onChange:ou},nodeAutoColorBy:{},nodeCanvasObject:{triggerUpdate:!1,onChange:ou},nodeCanvasObjectMode:{default:function(){return"replace"},triggerUpdate:!1,onChange:ou},nodeVisibility:{default:!0,triggerUpdate:!1,onChange:ou},linkSource:{default:"source"},linkTarget:{default:"target"},linkVisibility:{default:!0,triggerUpdate:!1,onChange:ou},linkColor:{default:"color",triggerUpdate:!1,onChange:ou},linkAutoColorBy:{},linkLineDash:{triggerUpdate:!1,onChange:ou},linkWidth:{default:1,triggerUpdate:!1,onChange:ou},linkCurvature:{default:0,triggerUpdate:!1,onChange:ou},linkCanvasObject:{triggerUpdate:!1,onChange:ou},linkCanvasObjectMode:{default:function(){return"replace"},triggerUpdate:!1,onChange:ou},linkDirectionalArrowLength:{default:0,triggerUpdate:!1,onChange:ou},linkDirectionalArrowColor:{triggerUpdate:!1,onChange:ou},linkDirectionalArrowRelPos:{default:.5,triggerUpdate:!1,onChange:ou},linkDirectionalParticles:{default:0,triggerUpdate:!1,onChange:au},linkDirectionalParticleSpeed:{default:.01,triggerUpdate:!1},linkDirectionalParticleOffset:{default:0,triggerUpdate:!1},linkDirectionalParticleWidth:{default:4,triggerUpdate:!1},linkDirectionalParticleColor:{triggerUpdate:!1},linkDirectionalParticleCanvasObject:{triggerUpdate:!1},globalScale:{default:1,triggerUpdate:!1},d3AlphaMin:{default:0,triggerUpdate:!1},d3AlphaDecay:{default:.0228,triggerUpdate:!1,onChange:function(t,n){n.forceLayout.alphaDecay(t)}},d3AlphaTarget:{default:0,triggerUpdate:!1,onChange:function(t,n){n.forceLayout.alphaTarget(t)}},d3VelocityDecay:{default:.4,triggerUpdate:!1,onChange:function(t,n){n.forceLayout.velocityDecay(t)}},warmupTicks:{default:0,triggerUpdate:!1},cooldownTicks:{default:1/0,triggerUpdate:!1},cooldownTime:{default:15e3,triggerUpdate:!1},onUpdate:{default:function(){},triggerUpdate:!1},onFinishUpdate:{default:function(){},triggerUpdate:!1},onEngineTick:{default:function(){},triggerUpdate:!1},onEngineStop:{default:function(){},triggerUpdate:!1},onNeedsRedraw:{triggerUpdate:!1},isShadow:{default:!1,triggerUpdate:!1}},methods:{d3Force:function(t,n,e){return void 0===e?t.forceLayout.force(n):(t.forceLayout.force(n,e),this)},d3ReheatSimulation:function(t){return t.forceLayout.alpha(1),this.resetCountdown(),this},resetCountdown:function(t){return t.cntTicks=0,t.startTickTime=new Date,t.engineRunning=!0,this},isEngineRunning:function(t){return!!t.engineRunning},tickFrame:function(t){var n,e,r,i,o,a;return!t.isShadow&&t.engineRunning&&(++t.cntTicks>t.cooldownTicks||new Date-t.startTickTime>t.cooldownTime||t.d3AlphaMin>0&&t.forceLayout.alpha()<t.d3AlphaMin?(t.engineRunning=!1,t.onEngineStop()):(t.forceLayout.tick(),t.onEngineTick())),function(){var n=Ur(t.linkVisibility),e=Ur(t.linkColor),r=Ur(t.linkWidth),i=Ur(t.linkLineDash),o=Ur(t.linkCurvature),a=Ur(t.linkCanvasObjectMode),u=t.ctx,s=2*t.isShadow,l=t.graphData.links.filter(n);l.forEach(function(t){var n=o(t);if(!n)return void(t.__controlPoints=null);var e=t.source,r=t.target;if(!(e&&r&&e.hasOwnProperty("x")&&r.hasOwnProperty("x")))return;var i=Math.sqrt(Math.pow(r.x-e.x,2)+Math.pow(r.y-e.y,2));if(i>0){var a=Math.atan2(r.y-e.y,r.x-e.x),u=i*n,s={x:(e.x+r.x)/2+u*Math.cos(a-Math.PI/2),y:(e.y+r.y)/2+u*Math.sin(a-Math.PI/2)};t.__controlPoints=[s.x,s.y]}else{var l=70*n;t.__controlPoints=[r.x,r.y-l,r.x+l,r.y]}});var c=[],f=[],h=l;if(t.linkCanvasObject){var p=[],d=[];l.forEach(function(t){return({before:c,after:f,replace:p}[a(t)]||d).push(t)}),h=[].concat(tu(c),f,d),c=c.concat(p)}u.save(),c.forEach(function(n){return t.linkCanvasObject(n,u,t.globalScale)}),u.restore();var y=Ba(h,[e,r,i]);u.save(),Object.entries(y).forEach(function(n){var e=Ka(n,2),r=e[0],o=e[1],a=r&&"undefined"!==r?r:"rgba(0,0,0,0.15)";Object.entries(o).forEach(function(n){var e=Ka(n,2),r=e[0],o=e[1],l=(r||1)/t.globalScale+s;Object.entries(o).forEach(function(t){var n=Ka(t,2);n[0];var e=n[1],r=i(e[0]);u.beginPath(),e.forEach(function(t){var n=t.source,e=t.target;if(n&&e&&n.hasOwnProperty("x")&&e.hasOwnProperty("x")){u.moveTo(n.x,n.y);var r=t.__controlPoints;r?u[2===r.length?"quadraticCurveTo":"bezierCurveTo"].apply(u,tu(r).concat([e.x,e.y])):u.lineTo(e.x,e.y)}}),u.strokeStyle=a,u.lineWidth=l,u.setLineDash(r||[]),u.stroke()})})}),u.restore(),u.save(),f.forEach(function(n){return t.linkCanvasObject(n,u,t.globalScale)}),u.restore()}(),!t.isShadow&&(n=Ur(t.linkDirectionalArrowLength),e=Ur(t.linkDirectionalArrowRelPos),r=Ur(t.linkVisibility),i=Ur(t.linkDirectionalArrowColor||t.linkColor),o=Ur(t.nodeVal),(a=t.ctx).save(),t.graphData.links.filter(r).forEach(function(r){var u=n(r);if(u&&!(u<0)){var s=r.source,l=r.target;if(s&&l&&s.hasOwnProperty("x")&&l.hasOwnProperty("x")){var c=Math.sqrt(Math.max(0,o(s)||1))*t.nodeRelSize,f=Math.sqrt(Math.max(0,o(l)||1))*t.nodeRelSize,h=Math.min(1,Math.max(0,e(r))),p=i(r)||"rgba(0,0,0,0.28)",d=u/1.6/2,y=r.__controlPoints&&Wa(Ia,[s.x,s.y].concat(tu(r.__controlPoints),[l.x,l.y])),g=y?function(t){return y.get(t)}:function(t){return{x:s.x+(l.x-s.x)*t||0,y:s.y+(l.y-s.y)*t||0}},v=y?y.length():Math.sqrt(Math.pow(l.x-s.x,2)+Math.pow(l.y-s.y,2)),_=c+u+(v-c-f-u)*h,m=g(_/v),b=g((_-u)/v),x=g((_-.8*u)/v),w=Math.atan2(m.y-b.y,m.x-b.x)-Math.PI/2;a.beginPath(),a.moveTo(m.x,m.y),a.lineTo(b.x+d*Math.cos(w),b.y+d*Math.sin(w)),a.lineTo(x.x,x.y),a.lineTo(b.x-d*Math.cos(w),b.y-d*Math.sin(w)),a.fillStyle=p,a.fill()}}}),a.restore()),!t.isShadow&&function(){var n=Ur(t.linkDirectionalParticles),e=Ur(t.linkDirectionalParticleSpeed),r=Ur(t.linkDirectionalParticleOffset),i=Ur(t.linkDirectionalParticleWidth),o=Ur(t.linkVisibility),a=Ur(t.linkDirectionalParticleColor||t.linkColor),u=t.ctx;u.save(),t.graphData.links.filter(o).forEach(function(o){var s=n(o);if(o.hasOwnProperty("__photons")&&o.__photons.length){var l=o.source,c=o.target;if(l&&c&&l.hasOwnProperty("x")&&c.hasOwnProperty("x")){var f=e(o),h=Math.abs(r(o)),p=o.__photons||[],d=Math.max(0,i(o)/2)/Math.sqrt(t.globalScale),y=a(o)||"rgba(0,0,0,0.28)";u.fillStyle=y;var g=o.__controlPoints?Wa(Ia,[l.x,l.y].concat(tu(o.__controlPoints),[c.x,c.y])):null,v=0,_=!1;p.forEach(function(n){var e=!!n.__singleHop;if(n.hasOwnProperty("__progressRatio")||(n.__progressRatio=e?0:(v+h)/s),!e&&v++,n.__progressRatio+=f,n.__progressRatio>=1){if(e)return void(_=!0);n.__progressRatio=n.__progressRatio%1}var r=n.__progressRatio,i=g?g.get(r):{x:l.x+(c.x-l.x)*r||0,y:l.y+(c.y-l.y)*r||0};t.linkDirectionalParticleCanvasObject?t.linkDirectionalParticleCanvasObject(i.x,i.y,o,u,t.globalScale):(u.beginPath(),u.arc(i.x,i.y,d,0,2*Math.PI,!1),u.fill())}),_&&(o.__photons=o.__photons.filter(function(t){return!t.__singleHop||t.__progressRatio<=1}))}}}),u.restore()}(),function(){var n=Ur(t.nodeVisibility),e=Ur(t.nodeVal),r=Ur(t.nodeColor),i=Ur(t.nodeCanvasObjectMode),o=t.ctx,a=t.isShadow/t.globalScale,u=t.graphData.nodes.filter(n);o.save(),u.forEach(function(n){var u=i(n);if(!t.nodeCanvasObject||"before"!==u&&"replace"!==u||(t.nodeCanvasObject(n,o,t.globalScale),"replace"!==u)){var s=Math.sqrt(Math.max(0,e(n)||1))*t.nodeRelSize+a;o.beginPath(),o.arc(n.x,n.y,s,0,2*Math.PI,!1),o.fillStyle=r(n)||"rgba(31, 120, 180, 0.92)",o.fill(),t.nodeCanvasObject&&"after"===u&&t.nodeCanvasObject(n,t.ctx,t.globalScale)}else o.restore()}),o.restore()}(),this},emitParticle:function(t,n){return n&&(!n.__photons&&(n.__photons=[]),n.__photons.push({__singleHop:!0})),this}},stateInit:function(){return{forceLayout:fa().force("link",ia()).force("charge",ha()).force("center",zo()).force("dagRadial",null).stop(),engineRunning:!1}},init:function(t,n){n.ctx=t},update:function(t,n){t.engineRunning=!1,t.onUpdate(),null!==t.nodeAutoColorBy&&iu(t.graphData.nodes,Ur(t.nodeAutoColorBy),t.nodeColor),null!==t.linkAutoColorBy&&iu(t.graphData.links,Ur(t.linkAutoColorBy),t.linkColor),t.graphData.links.forEach(function(n){n.source=n[t.linkSource],n.target=n[t.linkTarget]}),t.forceLayout.stop().alpha(1).nodes(t.graphData.nodes);var e=t.forceLayout.force("link");e&&e.id(function(n){return n[t.nodeId]}).links(t.graphData.links);var r=t.dagMode&&function(t,n){var e=t.nodes,r=t.links,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=i.nodeFilter,a=void 0===o?function(){return!0}:o,u=i.onLoopError,s=void 0===u?function(t){throw"Invalid DAG structure! Found cycle in node path: ".concat(t.join(" -> "),".")}:u,l={};e.forEach(function(t){return l[n(t)]={data:t,out:[],depth:-1,skip:!a(t)}}),r.forEach(function(t){var e=t.source,r=t.target,i=s(e),o=s(r);if(!l.hasOwnProperty(i))throw"Missing source node with id: ".concat(i);if(!l.hasOwnProperty(o))throw"Missing target node with id: ".concat(o);var a=l[i],u=l[o];function s(t){return"object"===nu(t)?n(t):t}a.out.push(u)});var c=[];return function t(e){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=function(){var o=e[a];if(-1!==r.indexOf(o)){var u=[].concat(tu(r.slice(r.indexOf(o))),[o]).map(function(t){return n(t.data)});return c.some(function(t){return t.length===u.length&&t.every(function(t,n){return t===u[n]})})||(c.push(u),s(u)),1}i>o.depth&&(o.depth=i,t(o.out,[].concat(tu(r),[o]),i+(o.skip?0:1)))},a=0,u=e.length;a<u;a++)o()}(Object.values(l)),Object.assign.apply(Object,[{}].concat(tu(Object.entries(l).filter(function(t){return!Ka(t,2)[1].skip}).map(function(t){var n=Ka(t,2);return Ya({},n[0],n[1].depth)}))))}(t.graphData,function(n){return n[t.nodeId]},{nodeFilter:t.dagNodeFilter,onLoopError:t.onDagError||void 0}),i=Math.max.apply(Math,tu(Object.values(r||[]))),o=t.dagLevelDistance||t.graphData.nodes.length/(i||1)*2*(-1!==["radialin","radialout"].indexOf(t.dagMode)?.7:1);if(["lr","rl","td","bu"].includes(n.dagMode)){var a=["lr","rl"].includes(n.dagMode)?"fx":"fy";t.graphData.nodes.filter(t.dagNodeFilter).forEach(function(t){return delete t[a]})}if(["lr","rl","td","bu"].includes(t.dagMode)){var u=["rl","bu"].includes(t.dagMode),s=["lr","rl"].includes(t.dagMode)?"fx":"fy";t.graphData.nodes.filter(t.dagNodeFilter).forEach(function(n){return n[s]=function(n){return(r[n[t.nodeId]]-i/2)*o*(u?-1:1)}(n)})}t.forceLayout.force("dagRadial",-1!==["radialin","radialout"].indexOf(t.dagMode)?function(t,n,e,r){var i,o,a,u,s=ta(.1);function l(t){for(var s=0,l=i.length;s<l;++s){var c=i[s],f=c.x-n||1e-6,h=(c.y||0)-e||1e-6,p=(c.z||0)-r||1e-6,d=Math.sqrt(f*f+h*h+p*p),y=(u[s]-d)*a[s]*t/d;c.vx+=f*y,o>1&&(c.vy+=h*y),o>2&&(c.vz+=p*y)}}function c(){if(i){var n,e=i.length;for(a=new Array(e),u=new Array(e),n=0;n<e;++n)u[n]=+t(i[n],n,i),a[n]=isNaN(u[n])?0:+s(i[n],n,i)}}return"function"!=typeof t&&(t=ta(+t)),null==n&&(n=0),null==e&&(e=0),null==r&&(r=0),l.initialize=function(t,...n){i=t,o=n.find(t=>[1,2,3].includes(t))||2,c()},l.strength=function(t){return arguments.length?(s="function"==typeof t?t:ta(+t),c(),l):s},l.radius=function(n){return arguments.length?(t="function"==typeof n?n:ta(+n),c(),l):t},l.x=function(t){return arguments.length?(n=+t,l):n},l.y=function(t){return arguments.length?(e=+t,l):e},l.z=function(t){return arguments.length?(r=+t,l):r},l}(function(n){var e=r[n[t.nodeId]]||-1;return("radialin"===t.dagMode?i-e:e)*o}).strength(function(n){return t.dagNodeFilter(n)?1:0}):null);for(var l=0;l<t.warmupTicks&&!(t.d3AlphaMin>0&&t.forceLayout.alpha()<t.d3AlphaMin);l++)t.forceLayout.tick();this.resetCountdown(),t.onFinishUpdate()}});function su(t,n){var e=t instanceof Array?t:[t],r=new n;return r._destructor&&r._destructor(),{linkProp:function(t){return{default:r[t](),onChange:function(n,r){e.forEach(function(e){return r[e][t](n)})},triggerUpdate:!1}},linkMethod:function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];var a=[];return e.forEach(function(e){var r=n[e],o=r[t].apply(r,i);o!==r&&a.push(o)}),a.length?a[0]:this}}}}var lu=su("forceGraph",uu),cu=su(["forceGraph","shadowGraph"],uu),fu=Object.assign.apply(Object,tu(["nodeColor","nodeAutoColorBy","nodeCanvasObject","nodeCanvasObjectMode","linkColor","linkAutoColorBy","linkLineDash","linkWidth","linkCanvasObject","linkCanvasObjectMode","linkDirectionalArrowLength","linkDirectionalArrowColor","linkDirectionalArrowRelPos","linkDirectionalParticles","linkDirectionalParticleSpeed","linkDirectionalParticleOffset","linkDirectionalParticleWidth","linkDirectionalParticleColor","linkDirectionalParticleCanvasObject","dagMode","dagLevelDistance","dagNodeFilter","onDagError","d3AlphaMin","d3AlphaDecay","d3VelocityDecay","warmupTicks","cooldownTicks","cooldownTime","onEngineTick","onEngineStop"].map(function(t){return Ya({},t,lu.linkProp(t))})).concat(tu(["nodeRelSize","nodeId","nodeVal","nodeVisibility","linkSource","linkTarget","linkVisibility","linkCurvature"].map(function(t){return Ya({},t,cu.linkProp(t))})))),hu=Object.assign.apply(Object,tu(["d3Force","d3ReheatSimulation","emitParticle"].map(function(t){return Ya({},t,lu.linkMethod(t))})));function pu(t){if(t.canvas){var n=t.canvas.width,e=t.canvas.height;300===n&&150===e&&(n=e=0);var r=window.devicePixelRatio;n/=r,e/=r,[t.canvas,t.shadowCanvas].forEach(function(i){i.style.width="".concat(t.width,"px"),i.style.height="".concat(t.height,"px"),i.width=t.width*r,i.height=t.height*r,n||e||i.getContext("2d").scale(r,r)});var i=Le(t.canvas).k;t.zoom.translateBy(t.zoom.__baseElem,(t.width-n)/2/i,(t.height-e)/2/i),t.needsRedraw=!0}}function du(t){var n=window.devicePixelRatio;t.setTransform(n,0,0,n,0,0)}function yu(t,n,e){t.save(),du(t),t.clearRect(0,0,n,e),t.restore()}var gu=Ir({props:Ja({width:{default:window.innerWidth,onChange:function(t,n){return pu(n)},triggerUpdate:!1},height:{default:window.innerHeight,onChange:function(t,n){return pu(n)},triggerUpdate:!1},graphData:{default:{nodes:[],links:[]},onChange:function(t,n){[t.nodes,t.links].every(function(t){return(t||[]).every(function(t){return!t.hasOwnProperty("__indexColor")})})&&n.colorTracker.reset(),[{type:"Node",objs:t.nodes},{type:"Link",objs:t.links}].forEach(function(t){var e=t.type;t.objs.filter(function(t){if(!t.hasOwnProperty("__indexColor"))return!0;var e=n.colorTracker.lookup(t.__indexColor);return!e||!e.hasOwnProperty("d")||e.d!==t}).forEach(function(t){t.__indexColor=n.colorTracker.register({type:e,d:t})})}),n.forceGraph.graphData(t),n.shadowGraph.graphData(t)},triggerUpdate:!1},backgroundColor:{onChange:function(t,n){n.canvas&&t&&(n.canvas.style.background=t)},triggerUpdate:!1},nodeLabel:{default:"name",triggerUpdate:!1},nodePointerAreaPaint:{onChange:function(t,n){n.shadowGraph.nodeCanvasObject(t?function(n,e,r){return t(n,n.__indexColor,e,r)}:null),n.flushShadowCanvas&&n.flushShadowCanvas()},triggerUpdate:!1},linkPointerAreaPaint:{onChange:function(t,n){n.shadowGraph.linkCanvasObject(t?function(n,e,r){return t(n,n.__indexColor,e,r)}:null),n.flushShadowCanvas&&n.flushShadowCanvas()},triggerUpdate:!1},linkLabel:{default:"name",triggerUpdate:!1},linkHoverPrecision:{default:4,triggerUpdate:!1},minZoom:{default:.01,onChange:function(t,n){n.zoom.scaleExtent([t,n.zoom.scaleExtent()[1]])},triggerUpdate:!1},maxZoom:{default:1e3,onChange:function(t,n){n.zoom.scaleExtent([n.zoom.scaleExtent()[0],t])},triggerUpdate:!1},enableNodeDrag:{default:!0,triggerUpdate:!1},enableZoomInteraction:{default:!0,triggerUpdate:!1},enablePanInteraction:{default:!0,triggerUpdate:!1},enableZoomPanInteraction:{default:!0,triggerUpdate:!1},enablePointerInteraction:{default:!0,onChange:function(t,n){n.hoverObj=null},triggerUpdate:!1},autoPauseRedraw:{default:!0,triggerUpdate:!1},onNodeDrag:{default:function(){},triggerUpdate:!1},onNodeDragEnd:{default:function(){},triggerUpdate:!1},onNodeClick:{triggerUpdate:!1},onNodeRightClick:{triggerUpdate:!1},onNodeHover:{triggerUpdate:!1},onLinkClick:{triggerUpdate:!1},onLinkRightClick:{triggerUpdate:!1},onLinkHover:{triggerUpdate:!1},onBackgroundClick:{triggerUpdate:!1},onBackgroundRightClick:{triggerUpdate:!1},onZoom:{triggerUpdate:!1},onZoomEnd:{triggerUpdate:!1},onRenderFramePre:{triggerUpdate:!1},onRenderFramePost:{triggerUpdate:!1}},fu),aliases:{stopAnimation:"pauseAnimation"},methods:Ja({graph2ScreenCoords:function(t,n,e){var r=Le(t.canvas);return{x:n*r.k+r.x,y:e*r.k+r.y}},screen2GraphCoords:function(t,n,e){var r=Le(t.canvas);return{x:(n-r.x)/r.k,y:(e-r.y)/r.k}},centerAt:function(t,n,e,r){if(!t.canvas)return null;if(void 0!==n||void 0!==e){var i=Object.assign({},void 0!==n?{x:n}:{},void 0!==e?{y:e}:{});return r?t.tweenGroup.add(new Pr(o()).to(i,r).easing(Ar.Quadratic.Out).onUpdate(a).start()):a(i),this}return o();function o(){var n=Le(t.canvas);return{x:(t.width/2-n.x)/n.k,y:(t.height/2-n.y)/n.k}}function a(n){var e=n.x,r=n.y;t.zoom.translateTo(t.zoom.__baseElem,void 0===e?o().x:e,void 0===r?o().y:r),t.needsRedraw=!0}},zoom:function(t,n,e){return t.canvas?void 0!==n?(e?t.tweenGroup.add(new Pr({k:r()}).to({k:n},e).easing(Ar.Quadratic.Out).onUpdate(function(t){return i(t.k)}).start()):i(n),this):r():null;function r(){return Le(t.canvas).k}function i(n){t.zoom.scaleTo(t.zoom.__baseElem,n),t.needsRedraw=!0}},zoomToFit:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=arguments.length,i=new Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];var a=this.getGraphBbox.apply(this,i);if(a){var u={x:(a.x[0]+a.x[1])/2,y:(a.y[0]+a.y[1])/2},s=Math.max(1e-12,Math.min(1e12,(t.width-2*e)/(a.x[1]-a.x[0]),(t.height-2*e)/(a.y[1]-a.y[0])));this.centerAt(u.x,u.y,n),this.zoom(s,n)}return this},getGraphBbox:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0},e=Ur(t.nodeVal),r=function(n){return Math.sqrt(Math.max(0,e(n)||1))*t.nodeRelSize},i=t.graphData.nodes.filter(n).map(function(t){return{x:t.x,y:t.y,r:r(t)}});return i.length?{x:[tr(i,function(t){return t.x-t.r}),Ke(i,function(t){return t.x+t.r})],y:[tr(i,function(t){return t.y-t.r}),Ke(i,function(t){return t.y+t.r})]}:null},pauseAnimation:function(t){return t.animationFrameRequestId&&(cancelAnimationFrame(t.animationFrameRequestId),t.animationFrameRequestId=null),this},resumeAnimation:function(t){return t.animationFrameRequestId||this._animationCycle(),this},_destructor:function(){this.pauseAnimation(),this.graphData({nodes:[],links:[]})}},hu),stateInit:function(){return{lastSetZoom:1,zoom:Ye(),forceGraph:new uu,shadowGraph:(new uu).cooldownTicks(0).nodeColor("__indexColor").linkColor("__indexColor").isShadow(!0),colorTracker:new Hi,tweenGroup:new Or}},init:function(t,n){var e=this;t.innerHTML="";var r=document.createElement("div");r.classList.add("force-graph-container"),r.style.position="relative",t.appendChild(r),n.canvas=document.createElement("canvas"),n.backgroundColor&&(n.canvas.style.background=n.backgroundColor),r.appendChild(n.canvas),n.shadowCanvas=document.createElement("canvas");var i=n.canvas.getContext("2d"),o=n.shadowCanvas.getContext("2d",{willReadFrequently:!0}),a={x:-1e12,y:-1e12},u=function(){var t=null,e=window.devicePixelRatio,r=a.x>0&&a.y>0?o.getImageData(a.x*e,a.y*e,1,1):null;return r&&(t=n.colorTracker.lookup(r.data)),t};St(n.canvas).call(function(){var t,n,e,r,i=Ft,o=Lt,a=$t,u=qt,s={},l=Ot("start","drag","end"),c=0,f=0;function h(t){t.on("mousedown.drag",p).filter(u).on("touchstart.drag",g).on("touchmove.drag",v,Pt).on("touchend.drag touchcancel.drag",_).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(a,u){if(!r&&i.call(this,a,u)){var s=m(this,o.call(this,a,u),a,u,"mouse");s&&(St(a.view).on("mousemove.drag",d,jt).on("mouseup.drag",y,jt),Rt(a.view),Tt(a),e=!1,t=a.clientX,n=a.clientY,s("start",a))}}function d(r){if(Nt(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>f}s.mouse("drag",r)}function y(t){St(t.view).on("mousemove.drag mouseup.drag",null),Dt(t.view,e),Nt(t),s.mouse("end",t)}function g(t,n){if(i.call(this,t,n)){var e,r,a=t.changedTouches,u=o.call(this,t,n),s=a.length;for(e=0;e<s;++e)(r=m(this,u,t,n,a[e].identifier,a[e]))&&(Tt(t),r("start",t,a[e]))}}function v(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=s[r[n].identifier])&&(Nt(t),e("drag",t,r[n]))}function _(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=s[i[n].identifier])&&(Tt(t),e("end",t,i[n]))}function m(t,n,e,r,i,o){var u,f,p,d=l.copy(),y=At(o||e,n);if(null!=(p=a.call(t,new Ut("beforestart",{sourceEvent:e,target:h,identifier:i,active:c,x:y[0],y:y[1],dx:0,dy:0,dispatch:d}),r)))return u=p.x-y[0]||0,f=p.y-y[1]||0,function e(o,a,l){var g,v=y;switch(o){case"start":s[i]=e,g=c++;break;case"end":delete s[i],--c;case"drag":y=At(l||a,n),g=c}d.call(o,t,new Ut(o,{sourceEvent:a,subject:p,target:h,identifier:i,active:g,x:y[0]+u,y:y[1]+f,dx:y[0]-v[0],dy:y[1]-v[1],dispatch:d}),r)}}return h.filter=function(t){return arguments.length?(i="function"==typeof t?t:It(!!t),h):i},h.container=function(t){return arguments.length?(o="function"==typeof t?t:It(t),h):o},h.subject=function(t){return arguments.length?(a="function"==typeof t?t:It(t),h):a},h.touchable=function(t){return arguments.length?(u="function"==typeof t?t:It(!!t),h):u},h.on=function(){var t=l.on.apply(l,arguments);return t===l?h:t},h.clickDistance=function(t){return arguments.length?(f=(t=+t)*t,h):Math.sqrt(f)},h}().subject(function(){if(!n.enableNodeDrag)return null;var t=u();return t&&"Node"===t.type?t.d:null}).on("start",function(t){var e=t.subject;e.__initialDragPos={x:e.x,y:e.y,fx:e.fx,fy:e.fy},t.active||(e.fx=e.x,e.fy=e.y),n.canvas.classList.add("grabbable")}).on("drag",function(t){var e=t.subject,r=e.__initialDragPos,i=t,o=Le(n.canvas).k,a={x:r.x+(i.x-r.x)/o-e.x,y:r.y+(i.y-r.y)/o-e.y};["x","y"].forEach(function(t){return e["f".concat(t)]=e[t]=r[t]+(i[t]-r[t])/o}),!e.__dragged&&5>=Math.sqrt(function(t){let n=0;for(let e of t)(e=+e)&&(n+=e);return n}(["x","y"].map(function(n){return Math.pow(t[n]-r[n],2)})))||(n.forceGraph.d3AlphaTarget(.3).resetCountdown(),n.isPointerDragging=!0,e.__dragged=!0,n.onNodeDrag(e,a))}).on("end",function(t){var e=t.subject,r=e.__initialDragPos,i={x:e.x-r.x,y:e.y-r.y};void 0===r.fx&&(e.fx=void 0),void 0===r.fy&&(e.fy=void 0),delete e.__initialDragPos,n.forceGraph.d3AlphaTarget()&&n.forceGraph.d3AlphaTarget(0).resetCountdown(),n.canvas.classList.remove("grabbable"),n.isPointerDragging=!1,e.__dragged&&(delete e.__dragged,n.onNodeDragEnd(e,i))})),n.zoom(n.zoom.__baseElem=St(n.canvas)),n.zoom.__baseElem.on("dblclick.zoom",null),n.zoom.filter(function(t){return!t.button&&n.enableZoomPanInteraction&&("wheel"!==t.type||Ur(n.enableZoomInteraction)(t))&&("wheel"===t.type||Ur(n.enablePanInteraction)(t))}).on("zoom",function(t){var r=t.transform;[i,o].forEach(function(t){du(t),t.translate(r.x,r.y),t.scale(r.k,r.k)}),n.isPointerDragging=!0,n.onZoom&&n.onZoom(Ja(Ja({},r),e.centerAt())),n.needsRedraw=!0}).on("end",function(t){n.isPointerDragging=!1,n.onZoomEnd&&n.onZoomEnd(Ja(Ja({},t.transform),e.centerAt()))}),pu(n),n.forceGraph.onNeedsRedraw(function(){return n.needsRedraw=!0}).onFinishUpdate(function(){Le(n.canvas).k===n.lastSetZoom&&n.graphData.nodes.length&&(n.zoom.scaleTo(n.zoom.__baseElem,n.lastSetZoom=4/Math.cbrt(n.graphData.nodes.length)),n.needsRedraw=!0)}),n.tooltip=new Oo(r),["pointermove","pointerdown"].forEach(function(t){return r.addEventListener(t,function(e){"pointerdown"===t&&(n.isPointerPressed=!0,n.pointerDownEvent=e),!n.isPointerDragging&&"pointermove"===e.type&&n.onBackgroundClick&&(e.pressure>0||n.isPointerPressed)&&("touch"!==e.pointerType||void 0===e.movementX||[e.movementX,e.movementY].some(function(t){return Math.abs(t)>1}))&&(n.isPointerDragging=!0);var i,o,u,s=(i=r.getBoundingClientRect(),o=window.pageXOffset||document.documentElement.scrollLeft,u=window.pageYOffset||document.documentElement.scrollTop,{top:i.top+u,left:i.left+o});a.x=e.pageX-s.left,a.y=e.pageY-s.top},{passive:!0})}),r.addEventListener("pointerup",function(t){if(n.isPointerPressed)if(n.isPointerPressed=!1,n.isPointerDragging)n.isPointerDragging=!1;else{var e=[t,n.pointerDownEvent];requestAnimationFrame(function(){if(0===t.button)if(n.hoverObj){var r=n["on".concat(n.hoverObj.type,"Click")];r&&r.apply(void 0,[n.hoverObj.d].concat(e))}else n.onBackgroundClick&&n.onBackgroundClick.apply(n,e);if(2===t.button)if(n.hoverObj){var i=n["on".concat(n.hoverObj.type,"RightClick")];i&&i.apply(void 0,[n.hoverObj.d].concat(e))}else n.onBackgroundRightClick&&n.onBackgroundRightClick.apply(n,e)})}},{passive:!0}),r.addEventListener("contextmenu",function(t){return!(n.onBackgroundRightClick||n.onNodeRightClick||n.onLinkRightClick)||(t.preventDefault(),!1)}),n.forceGraph(i),n.shadowGraph(o);var s=function(t,n,e){var r=!0,i=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return yr(e)&&(r="leading"in e?!!e.leading:r,i="trailing"in e?!!e.trailing:i),Sr(t,n,{leading:r,maxWait:n,trailing:i})}(function(){yu(o,n.width,n.height),n.shadowGraph.linkWidth(function(t){return Ur(n.linkWidth)(t)+n.linkHoverPrecision});var t=Le(n.canvas);n.shadowGraph.globalScale(t.k).tickFrame()},800);n.flushShadowCanvas=s.flush,(this._animationCycle=function t(){var e=!n.autoPauseRedraw||!!n.needsRedraw||n.forceGraph.isEngineRunning()||n.graphData.links.some(function(t){return t.__photons&&t.__photons.length});if(n.needsRedraw=!1,n.enablePointerInteraction){var r=n.isPointerDragging?null:u();if(r!==n.hoverObj){var o=n.hoverObj,a=o?o.type:null,l=r?r.type:null;if(a&&a!==l){var c=n["on".concat(a,"Hover")];c&&c(null,o.d)}if(l){var f=n["on".concat(l,"Hover")];f&&f(r.d,a===l?o.d:null)}n.tooltip.content(r&&Ur(n["".concat(r.type.toLowerCase(),"Label")])(r.d)||null),n.canvas.classList[r&&n["on".concat(l,"Click")]||!r&&n.onBackgroundClick?"add":"remove"]("clickable"),n.hoverObj=r}e&&s()}if(e){yu(i,n.width,n.height);var h=Le(n.canvas).k;n.onRenderFramePre&&n.onRenderFramePre(i,h),n.forceGraph.globalScale(h).tickFrame(),n.onRenderFramePost&&n.onRenderFramePost(i,h)}n.tweenGroup.update(),n.animationFrameRequestId=requestAnimationFrame(t)})()},update:function(t){}});function vu(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var _u,mu,bu,xu,wu,ku,Su,Au,Mu,Ou,zu,Eu,Cu,Pu={exports:{}},ju={exports:{}},Tu={};function Nu(){return mu||(mu=1,ju.exports=(_u||(_u=1,function(){var t="function"==typeof Symbol&&Symbol.for,n=t?Symbol.for("react.element"):60103,e=t?Symbol.for("react.portal"):60106,r=t?Symbol.for("react.fragment"):60107,i=t?Symbol.for("react.strict_mode"):60108,o=t?Symbol.for("react.profiler"):60114,a=t?Symbol.for("react.provider"):60109,u=t?Symbol.for("react.context"):60110,s=t?Symbol.for("react.async_mode"):60111,l=t?Symbol.for("react.concurrent_mode"):60111,c=t?Symbol.for("react.forward_ref"):60112,f=t?Symbol.for("react.suspense"):60113,h=t?Symbol.for("react.suspense_list"):60120,p=t?Symbol.for("react.memo"):60115,d=t?Symbol.for("react.lazy"):60116,y=t?Symbol.for("react.block"):60121,g=t?Symbol.for("react.fundamental"):60117,v=t?Symbol.for("react.responder"):60118,_=t?Symbol.for("react.scope"):60119;function m(t){if("object"==typeof t&&null!==t){var h=t.$$typeof;switch(h){case n:var y=t.type;switch(y){case s:case l:case r:case o:case i:case f:return y;default:var g=y&&y.$$typeof;switch(g){case u:case c:case d:case p:case a:return g;default:return h}}case e:return h}}}var b=s,x=l,w=u,k=a,S=n,A=c,M=r,O=d,z=p,E=e,C=o,P=i,j=f,T=!1;function N(t){return m(t)===l}Tu.AsyncMode=b,Tu.ConcurrentMode=x,Tu.ContextConsumer=w,Tu.ContextProvider=k,Tu.Element=S,Tu.ForwardRef=A,Tu.Fragment=M,Tu.Lazy=O,Tu.Memo=z,Tu.Portal=E,Tu.Profiler=C,Tu.StrictMode=P,Tu.Suspense=j,Tu.isAsyncMode=function(t){return T||(T=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),N(t)||m(t)===s},Tu.isConcurrentMode=N,Tu.isContextConsumer=function(t){return m(t)===u},Tu.isContextProvider=function(t){return m(t)===a},Tu.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===n},Tu.isForwardRef=function(t){return m(t)===c},Tu.isFragment=function(t){return m(t)===r},Tu.isLazy=function(t){return m(t)===d},Tu.isMemo=function(t){return m(t)===p},Tu.isPortal=function(t){return m(t)===e},Tu.isProfiler=function(t){return m(t)===o},Tu.isStrictMode=function(t){return m(t)===i},Tu.isSuspense=function(t){return m(t)===f},Tu.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===r||t===l||t===o||t===i||t===f||t===h||"object"==typeof t&&null!==t&&(t.$$typeof===d||t.$$typeof===p||t.$$typeof===a||t.$$typeof===u||t.$$typeof===c||t.$$typeof===g||t.$$typeof===v||t.$$typeof===_||t.$$typeof===y)},Tu.typeOf=m}()),Tu)),ju.exports}function Ru(){if(xu)return bu;xu=1;var t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,e=Object.prototype.propertyIsEnumerable;return bu=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var n={},e=0;e<10;e++)n["_"+String.fromCharCode(e)]=e;var r=Object.getOwnPropertyNames(n).map(function(t){return n[t]});if("0123456789"!==r.join(""))return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach(function(t){i[t]=t}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join("")}catch(t){return!1}}()?Object.assign:function(r,i){for(var o,a,u=function(t){if(null==t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}(r),s=1;s<arguments.length;s++){for(var l in o=Object(arguments[s]))n.call(o,l)&&(u[l]=o[l]);if(t){a=t(o);for(var c=0;c<a.length;c++)e.call(o,a[c])&&(u[a[c]]=o[a[c]])}}return u},bu}function Du(){if(ku)return wu;ku=1;return wu="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}function Iu(){return Au?Su:(Au=1,Su=Function.call.bind(Object.prototype.hasOwnProperty))}function Uu(){if(Ou)return Mu;Ou=1;var t,n=Du(),e={},r=Iu();function i(i,o,a,u,s){for(var l in i)if(r(i,l)){var c;try{if("function"!=typeof i[l]){var f=Error((u||"React class")+": "+a+" type `"+l+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof i[l]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw f.name="Invariant Violation",f}c=i[l](o,l,u,a,null,n)}catch(t){c=t}if(!c||c instanceof Error||t((u||"React class")+": type specification of "+a+" `"+l+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof c+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),c instanceof Error&&!(c.message in e)){e[c.message]=!0;var h=s?s():"";t("Failed "+a+" type: "+c.message+(null!=h?h:""))}}}return t=function(t){var n="Warning: "+t;"undefined"!=typeof console&&console.error(n);try{throw new Error(n)}catch(t){}},i.resetWarningCache=function(){e={}},Mu=i}function Fu(){if(Eu)return zu;Eu=1;var t,n=Nu(),e=Ru(),r=Du(),i=Iu(),o=Uu();function a(){return null}return t=function(t){var n="Warning: "+t;"undefined"!=typeof console&&console.error(n);try{throw new Error(n)}catch(t){}},zu=function(u,s){var l="function"==typeof Symbol&&Symbol.iterator;var c="<<anonymous>>",f={array:y("array"),bigint:y("bigint"),bool:y("boolean"),func:y("function"),number:y("number"),object:y("object"),string:y("string"),symbol:y("symbol"),any:d(a),arrayOf:function(t){return d(function(n,e,i,o,a){if("function"!=typeof t)return new p("Property `"+a+"` of component `"+i+"` has invalid PropType notation inside arrayOf.");var u=n[e];if(!Array.isArray(u))return new p("Invalid "+o+" `"+a+"` of type `"+_(u)+"` supplied to `"+i+"`, expected an array.");for(var s=0;s<u.length;s++){var l=t(u,s,i,o,a+"["+s+"]",r);if(l instanceof Error)return l}return null})},element:d(function(t,n,e,r,i){var o=t[n];return u(o)?null:new p("Invalid "+r+" `"+i+"` of type `"+_(o)+"` supplied to `"+e+"`, expected a single ReactElement.")}),elementType:d(function(t,e,r,i,o){var a=t[e];return n.isValidElementType(a)?null:new p("Invalid "+i+" `"+o+"` of type `"+_(a)+"` supplied to `"+r+"`, expected a single ReactElement type.")}),instanceOf:function(t){return d(function(n,e,r,i,o){if(!(n[e]instanceof t)){var a=t.name||c;return new p("Invalid "+i+" `"+o+"` of type `"+(((u=n[e]).constructor&&u.constructor.name?u.constructor.name:c)+"` supplied to `")+r+"`, expected instance of `"+a+"`.")}var u;return null})},node:d(function(t,n,e,r,i){return v(t[n])?null:new p("Invalid "+r+" `"+i+"` supplied to `"+e+"`, expected a ReactNode.")}),objectOf:function(t){return d(function(n,e,o,a,u){if("function"!=typeof t)return new p("Property `"+u+"` of component `"+o+"` has invalid PropType notation inside objectOf.");var s=n[e],l=_(s);if("object"!==l)return new p("Invalid "+a+" `"+u+"` of type `"+l+"` supplied to `"+o+"`, expected an object.");for(var c in s)if(i(s,c)){var f=t(s,c,o,a,u+"."+c,r);if(f instanceof Error)return f}return null})},oneOf:function(n){if(!Array.isArray(n))return t(arguments.length>1?"Invalid arguments supplied to oneOf, expected an array, got "+arguments.length+" arguments. A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).":"Invalid argument supplied to oneOf, expected an array."),a;return d(function(t,e,r,i,o){for(var a=t[e],u=0;u<n.length;u++)if(h(a,n[u]))return null;var s=JSON.stringify(n,function(t,n){return"symbol"===m(n)?String(n):n});return new p("Invalid "+i+" `"+o+"` of value `"+String(a)+"` supplied to `"+r+"`, expected one of "+s+".")})},oneOfType:function(n){if(!Array.isArray(n))return t("Invalid argument supplied to oneOfType, expected an instance of array."),a;for(var e=0;e<n.length;e++){var o=n[e];if("function"!=typeof o)return t("Invalid argument supplied to oneOfType. Expected an array of check functions, but received "+b(o)+" at index "+e+"."),a}return d(function(t,e,o,a,u){for(var s=[],l=0;l<n.length;l++){var c=(0,n[l])(t,e,o,a,u,r);if(null==c)return null;c.data&&i(c.data,"expectedType")&&s.push(c.data.expectedType)}return new p("Invalid "+a+" `"+u+"` supplied to `"+o+"`"+(s.length>0?", expected one of type ["+s.join(", ")+"]":"")+".")})},shape:function(t){return d(function(n,e,i,o,a){var u=n[e],s=_(u);if("object"!==s)return new p("Invalid "+o+" `"+a+"` of type `"+s+"` supplied to `"+i+"`, expected `object`.");for(var l in t){var c=t[l];if("function"!=typeof c)return g(i,o,a,l,m(c));var f=c(u,l,i,o,a+"."+l,r);if(f)return f}return null})},exact:function(t){return d(function(n,o,a,u,s){var l=n[o],c=_(l);if("object"!==c)return new p("Invalid "+u+" `"+s+"` of type `"+c+"` supplied to `"+a+"`, expected `object`.");var f=e({},n[o],t);for(var h in f){var d=t[h];if(i(t,h)&&"function"!=typeof d)return g(a,u,s,h,m(d));if(!d)return new p("Invalid "+u+" `"+s+"` key `"+h+"` supplied to `"+a+"`.\nBad object: "+JSON.stringify(n[o],null,"  ")+"\nValid keys: "+JSON.stringify(Object.keys(t),null,"  "));var y=d(l,h,a,u,s+"."+h,r);if(y)return y}return null})}};function h(t,n){return t===n?0!==t||1/t==1/n:t!=t&&n!=n}function p(t,n){this.message=t,this.data=n&&"object"==typeof n?n:{},this.stack=""}function d(n){var e={},i=0;function o(o,a,u,l,f,h,d){if(l=l||c,h=h||u,d!==r){if(s){var y=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");throw y.name="Invariant Violation",y}if("undefined"!=typeof console){var g=l+":"+u;!e[g]&&i<3&&(t("You are manually calling a React.PropTypes validation function for the `"+h+"` prop on `"+l+"`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details."),e[g]=!0,i++)}}return null==a[u]?o?null===a[u]?new p("The "+f+" `"+h+"` is marked as required in `"+l+"`, but its value is `null`."):new p("The "+f+" `"+h+"` is marked as required in `"+l+"`, but its value is `undefined`."):null:n(a,u,l,f,h)}var a=o.bind(null,!1);return a.isRequired=o.bind(null,!0),a}function y(t){return d(function(n,e,r,i,o,a){var u=n[e];return _(u)!==t?new p("Invalid "+i+" `"+o+"` of type `"+m(u)+"` supplied to `"+r+"`, expected `"+t+"`.",{expectedType:t}):null})}function g(t,n,e,r,i){return new p((t||"React class")+": "+n+" type `"+e+"."+r+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+i+"`.")}function v(t){switch(typeof t){case"number":case"string":case"undefined":return!0;case"boolean":return!t;case"object":if(Array.isArray(t))return t.every(v);if(null===t||u(t))return!0;var n=function(t){var n=t&&(l&&t[l]||t["@@iterator"]);if("function"==typeof n)return n}(t);if(!n)return!1;var e,r=n.call(t);if(n!==t.entries){for(;!(e=r.next()).done;)if(!v(e.value))return!1}else for(;!(e=r.next()).done;){var i=e.value;if(i&&!v(i[1]))return!1}return!0;default:return!1}}function _(t){var n=typeof t;return Array.isArray(t)?"array":t instanceof RegExp?"object":function(t,n){return"symbol"===t||!!n&&("Symbol"===n["@@toStringTag"]||"function"==typeof Symbol&&n instanceof Symbol)}(n,t)?"symbol":n}function m(t){if(null==t)return""+t;var n=_(t);if("object"===n){if(t instanceof Date)return"date";if(t instanceof RegExp)return"regexp"}return n}function b(t){var n=m(t);switch(n){case"array":case"object":return"an "+n;case"boolean":case"date":case"regexp":return"a "+n;default:return n}}return p.prototype=Error.prototype,f.checkPropTypes=o,f.resetWarningCache=o.resetWarningCache,f.PropTypes=f,f},zu}function Lu(){if(Cu)return Pu.exports;Cu=1;var t=Nu();return Pu.exports=Fu()(t.isElement,true),Pu.exports}var $u=vu(Lu());const qu={width:$u.number,height:$u.number,graphData:$u.shape({nodes:$u.arrayOf($u.object).isRequired,links:$u.arrayOf($u.object).isRequired}),backgroundColor:$u.string,nodeRelSize:$u.number,nodeId:$u.string,nodeLabel:$u.oneOfType([$u.string,$u.func]),nodeVal:$u.oneOfType([$u.number,$u.string,$u.func]),nodeVisibility:$u.oneOfType([$u.bool,$u.string,$u.func]),nodeColor:$u.oneOfType([$u.string,$u.func]),nodeAutoColorBy:$u.oneOfType([$u.string,$u.func]),onNodeHover:$u.func,onNodeClick:$u.func,linkSource:$u.string,linkTarget:$u.string,linkLabel:$u.oneOfType([$u.string,$u.func]),linkVisibility:$u.oneOfType([$u.bool,$u.string,$u.func]),linkColor:$u.oneOfType([$u.string,$u.func]),linkAutoColorBy:$u.oneOfType([$u.string,$u.func]),linkWidth:$u.oneOfType([$u.number,$u.string,$u.func]),linkCurvature:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalArrowLength:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalArrowColor:$u.oneOfType([$u.string,$u.func]),linkDirectionalArrowRelPos:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalParticles:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalParticleSpeed:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalParticleOffset:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalParticleWidth:$u.oneOfType([$u.number,$u.string,$u.func]),linkDirectionalParticleColor:$u.oneOfType([$u.string,$u.func]),onLinkHover:$u.func,onLinkClick:$u.func,dagMode:$u.oneOf(["td","bu","lr","rl","zin","zout","radialin","radialout"]),dagLevelDistance:$u.number,dagNodeFilter:$u.func,onDagError:$u.func,d3AlphaMin:$u.number,d3AlphaDecay:$u.number,d3VelocityDecay:$u.number,warmupTicks:$u.number,cooldownTicks:$u.number,cooldownTime:$u.number,onEngineTick:$u.func,onEngineStop:$u.func,getGraphBbox:$u.func},Bu={zoomToFit:$u.func,onNodeRightClick:$u.func,onNodeDrag:$u.func,onNodeDragEnd:$u.func,onLinkRightClick:$u.func,linkHoverPrecision:$u.number,onBackgroundClick:$u.func,onBackgroundRightClick:$u.func,enablePointerInteraction:$u.bool,enableNodeDrag:$u.bool},Hu={showNavInfo:$u.bool,nodeOpacity:$u.number,nodeResolution:$u.number,nodeThreeObject:$u.oneOfType([$u.object,$u.string,$u.func]),nodeThreeObjectExtend:$u.oneOfType([$u.bool,$u.string,$u.func]),nodePositionUpdate:$u.func,linkOpacity:$u.number,linkResolution:$u.number,linkCurveRotation:$u.oneOfType([$u.number,$u.string,$u.func]),linkMaterial:$u.oneOfType([$u.object,$u.string,$u.func]),linkThreeObject:$u.oneOfType([$u.object,$u.string,$u.func]),linkThreeObjectExtend:$u.oneOfType([$u.bool,$u.string,$u.func]),linkPositionUpdate:$u.func,linkDirectionalArrowResolution:$u.number,linkDirectionalParticleResolution:$u.number,linkDirectionalParticleThreeObject:$u.oneOfType([$u.object,$u.string,$u.func]),forceEngine:$u.oneOf(["d3","ngraph"]),ngraphPhysics:$u.object,numDimensions:$u.oneOf([1,2,3])},Vu=Object.assign({},qu,Bu,{linkLineDash:$u.oneOfType([$u.arrayOf($u.number),$u.string,$u.func]),nodeCanvasObjectMode:$u.oneOfType([$u.string,$u.func]),nodeCanvasObject:$u.func,nodePointerAreaPaint:$u.func,linkCanvasObjectMode:$u.oneOfType([$u.string,$u.func]),linkCanvasObject:$u.func,linkPointerAreaPaint:$u.func,linkDirectionalParticleCanvasObject:$u.func,autoPauseRedraw:$u.bool,minZoom:$u.number,maxZoom:$u.number,enableZoomInteraction:$u.oneOfType([$u.bool,$u.func]),enablePanInteraction:$u.oneOfType([$u.bool,$u.func]),onZoom:$u.func,onZoomEnd:$u.func,onRenderFramePre:$u.func,onRenderFramePost:$u.func});Object.assign({},qu,Bu,Hu,{enableNavigationControls:$u.bool,controlType:$u.oneOf(["trackball","orbit","fly"]),rendererConfig:$u.object,extraRenderers:$u.arrayOf($u.shape({render:$u.func.isRequired}))}),Object.assign({},qu,Hu,{nodeDesc:$u.oneOfType([$u.string,$u.func]),linkDesc:$u.oneOfType([$u.string,$u.func])}),Object.assign({},qu,Hu,{markerAttrs:$u.object,yOffset:$u.number,glScale:$u.number});const Gu=function(t){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=o.wrapperElementType,u=void 0===a?"div":a,s=o.nodeMapper,c=void 0===s?function(t){return t}:s,h=o.methodNames,p=void 0===h?[]:h,d=o.initPropNames,y=void 0===d?[]:d;return n.forwardRef(function(o,a){var s=n.useRef(),h=n.useMemo(function(){var n=Object.fromEntries(y.filter(function(t){return o.hasOwnProperty(t)}).map(function(t){return[t,o[t]]}));return t(n)},[]);f(function(){h(c(s.current))},n.useLayoutEffect),f(function(){return h._destructor instanceof Function?h._destructor:void 0});var d,g,v,_=n.useCallback(function(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];return h[t]instanceof Function?h[t].apply(h,e):void 0},[h]),m=n.useRef({});return Object.keys((d=o,g=[].concat(l(p),l(y)),v=new Set(g),Object.assign.apply(Object,[{}].concat(i(Object.entries(d).filter(function(t){var n=r(t,1)[0];return!v.has(n)}).map(function(t){var n=r(t,2);return e({},n[0],n[1])})))))).filter(function(t){return m.current[t]!==o[t]}).forEach(function(t){return _(t,o[t])}),m.current=o,n.useImperativeHandle(a,function(){return Object.fromEntries(p.map(function(t){return[t,function(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];return _.apply(void 0,[t].concat(e))}]}))},[_]),n.createElement(u,{ref:s})})}(gu,{methodNames:["emitParticle","d3Force","d3ReheatSimulation","stopAnimation","pauseAnimation","resumeAnimation","centerAt","zoom","zoomToFit","getGraphBbox","screen2GraphCoords","graph2ScreenCoords"]});return Gu.displayName="ForceGraph2D",Gu.propTypes=Vu,Gu});
