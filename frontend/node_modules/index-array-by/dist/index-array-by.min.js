// Version 1.4.2 index-array-by - https://github.com/vasturiano/index-array-by
!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(r="undefined"!=typeof globalThis?globalThis:r||self).indexBy=n()}(this,(function(){"use strict";function r(r,n){(null==n||n>r.length)&&(n=r.length);for(var t=0,e=Array(n);t<n;t++)e[t]=r[t];return e}function n(r,n){return function(r){if(Array.isArray(r))return r}(r)||function(r,n){var t=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=t){var e,o,i,u,a=[],l=!0,f=!1;try{if(i=(t=t.call(r)).next,0===n);else for(;!(l=(e=i.call(t)).done)&&(a.push(e.value),a.length!==n);l=!0);}catch(r){f=!0,o=r}finally{try{if(!l&&null!=t.return&&(u=t.return(),Object(u)!==u))return}finally{if(f)throw o}}return a}}(r,n)||o(r,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(n){return function(n){if(Array.isArray(n))return r(n)}(n)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(n)||o(n)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(r){var n=function(r,n){if("object"!=typeof r||!r)return r;var t=r[Symbol.toPrimitive];if(void 0!==t){var e=t.call(r,n);if("object"!=typeof e)return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}(r,"string");return"symbol"==typeof n?n:n+""}function o(n,t){if(n){if("string"==typeof n)return r(n,t);var e={}.toString.call(n).slice(8,-1);return"Object"===e&&n.constructor&&(e=n.constructor.name),"Map"===e||"Set"===e?Array.from(n):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?r(n,t):void 0}}return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],u=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=(o instanceof Array?o.length?o:[void 0]:[o]).map((function(r){return{keyAccessor:r,isProp:!(r instanceof Function)}})),l=r.reduce((function(r,n){var t=r,o=n;return a.forEach((function(r,n){var u,l=r.keyAccessor;if(r.isProp){var f=o,c=f[l],s=function(r,n){if(null==r)return{};var t,e,o=function(r,n){if(null==r)return{};var t={};for(var e in r)if({}.hasOwnProperty.call(r,e)){if(n.includes(e))continue;t[e]=r[e]}return t}(r,n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(r);for(e=0;e<i.length;e++)t=i[e],n.includes(t)||{}.propertyIsEnumerable.call(r,t)&&(o[t]=r[t])}return o}(f,[l].map(e));u=c,o=s}else u=l(o,n);n+1<a.length?(t.hasOwnProperty(u)||(t[u]={}),t=t[u]):i?(t.hasOwnProperty(u)||(t[u]=[]),t[u].push(o)):t[u]=o})),r}),{});i instanceof Function&&function r(n){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;t===a.length?Object.keys(n).forEach((function(r){return n[r]=i(n[r])})):Object.values(n).forEach((function(n){return r(n,t+1)}))}(l);var f=l;return u&&(f=[],function r(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];o.length===a.length?f.push({keys:o,vals:e}):Object.entries(e).forEach((function(e){var i=n(e,2),u=i[0],a=i[1];return r(a,[].concat(t(o),[u]))}))}(l),o instanceof Array&&0===o.length&&1===f.length&&(f[0].keys=[])),f}}));
