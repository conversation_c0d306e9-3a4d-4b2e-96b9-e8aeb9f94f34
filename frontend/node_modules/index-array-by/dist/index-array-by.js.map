{"version": 3, "file": "index-array-by.js", "sources": ["../src/index.js"], "sourcesContent": ["export default (list = [], keyAccessors = [], multiItem = true, flattenKeys = false) => {\n\n  const keys = (keyAccessors instanceof Array\n    ? (keyAccessors.length ? keyAccessors : [undefined])\n    : [keyAccessors]).map(key => ({\n    keyAccessor: key,\n    isProp: !(key instanceof Function)\n  }));\n\n  const indexedResult = list.reduce((res, item) => {\n    let iterObj = res;\n    let itemVal = item;\n\n    keys.forEach(({ keyAccessor, isProp }, idx) => {\n      let key;\n      if (isProp) {\n        const { [keyAccessor]: propVal, ...rest } = itemVal;\n        key = propVal;\n        itemVal = rest;\n      } else {\n        key = keyAccessor(itemVal, idx);\n      }\n\n      if (idx + 1 < keys.length) {\n        if (!iterObj.hasOwnProperty(key)) {\n          iterObj[key] = {};\n        }\n        iterObj = iterObj[key];\n      } else { // Leaf key\n        if (multiItem) {\n          if (!iterObj.hasOwnProperty(key)) {\n            iterObj[key] = [];\n          }\n          iterObj[key].push(itemVal);\n        } else {\n          iterObj[key] = itemVal;\n        }\n      }\n    });\n    return res;\n  }, {});\n\n  if (multiItem  instanceof Function) {\n    // Reduce leaf multiple values\n    (function reduce(node, level = 1) {\n      if (level === keys.length) {\n        Object.keys(node).forEach(k => node[k] = multiItem(node[k]));\n      } else {\n        Object.values(node).forEach(child => reduce(child, level + 1));\n      }\n    })(indexedResult); // IIFE\n  }\n\n  let result = indexedResult;\n\n  if (flattenKeys) {\n    // flatten into array\n    result = [];\n\n    (function flatten(node, accKeys = []) {\n      if (accKeys.length === keys.length) {\n        result.push({\n          keys: accKeys,\n          vals: node\n        });\n      } else {\n        Object.entries(node)\n          .forEach(([key, val]) => flatten(val, [...accKeys, key]));\n      }\n    })(indexedResult); //IIFE\n\n    if (keyAccessors instanceof Array && keyAccessors.length === 0 && result.length === 1) {\n      // clear keys if there's no key accessors (single result)\n      result[0].keys = [];\n    }\n  }\n\n  return result;\n}\n"], "names": ["list", "arguments", "length", "undefined", "keyAccessors", "multiItem", "flatten<PERSON>eys", "keys", "Array", "map", "key", "keyAccessor", "isProp", "Function", "indexedResult", "reduce", "res", "item", "iter<PERSON><PERSON><PERSON>", "itemVal", "for<PERSON>ach", "_ref", "idx", "_itemVal", "propVal", "rest", "_objectWithoutProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "push", "node", "level", "Object", "k", "values", "child", "result", "flatten", "accKeys", "vals", "entries", "_ref2", "_ref3", "_slicedToArray", "val", "concat", "_toConsumableArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,cAAA,CAAe,YAAyE;EAAA,EAAA,IAAxEA,IAAI,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;EAAA,EAAA,IAAEG,YAAY,GAAAH,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;EAAA,EAAA,IAAEI,SAAS,GAAAJ,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI,CAAA;EAAA,EAAA,IAAEK,WAAW,GAAAL,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;IAEjF,IAAMM,IAAI,GAAG,CAACH,YAAY,YAAYI,KAAK,GACtCJ,YAAY,CAACF,MAAM,GAAGE,YAAY,GAAG,CAACD,SAAS,CAAC,GACjD,CAACC,YAAY,CAAC,EAAEK,GAAG,CAAC,UAAAC,GAAG,EAAA;MAAA,OAAK;EAC9BC,MAAAA,WAAW,EAAED,GAAG;EAChBE,MAAAA,MAAM,EAAE,EAAEF,GAAG,YAAYG,QAAQ,CAAA;OAClC,CAAA;EAAA,GAAC,CAAC,CAAA;IAEH,IAAMC,aAAa,GAAGd,IAAI,CAACe,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;MAC/C,IAAIC,OAAO,GAAGF,GAAG,CAAA;MACjB,IAAIG,OAAO,GAAGF,IAAI,CAAA;EAElBV,IAAAA,IAAI,CAACa,OAAO,CAAC,UAAAC,IAAA,EAA0BC,GAAG,EAAK;EAAA,MAAA,IAA/BX,WAAW,GAAAU,IAAA,CAAXV,WAAW;UAAEC,MAAM,GAAAS,IAAA,CAANT,MAAM,CAAA;EACjC,MAAA,IAAIF,GAAG,CAAA;EACP,MAAA,IAAIE,MAAM,EAAE;UACV,IAAAW,QAAA,GAA4CJ,OAAO;YAA5BK,OAAO,GAAAD,QAAA,CAArBZ,WAAW,CAAA;YAAec,IAAI,GAAAC,wBAAA,CAAAH,QAAA,GAA9BZ,WAAW,CAAA,CAAAF,GAAA,CAAAkB,cAAA,CAAA,CAAA,CAAA;EACpBjB,QAAAA,GAAG,GAAGc,OAAO,CAAA;EACbL,QAAAA,OAAO,GAAGM,IAAI,CAAA;EAChB,OAAC,MAAM;EACLf,QAAAA,GAAG,GAAGC,WAAW,CAACQ,OAAO,EAAEG,GAAG,CAAC,CAAA;EACjC,OAAA;EAEA,MAAA,IAAIA,GAAG,GAAG,CAAC,GAAGf,IAAI,CAACL,MAAM,EAAE;EACzB,QAAA,IAAI,CAACgB,OAAO,CAACU,cAAc,CAAClB,GAAG,CAAC,EAAE;EAChCQ,UAAAA,OAAO,CAACR,GAAG,CAAC,GAAG,EAAE,CAAA;EACnB,SAAA;EACAQ,QAAAA,OAAO,GAAGA,OAAO,CAACR,GAAG,CAAC,CAAA;EACxB,OAAC,MAAM;EAAE;EACP,QAAA,IAAIL,SAAS,EAAE;EACb,UAAA,IAAI,CAACa,OAAO,CAACU,cAAc,CAAClB,GAAG,CAAC,EAAE;EAChCQ,YAAAA,OAAO,CAACR,GAAG,CAAC,GAAG,EAAE,CAAA;EACnB,WAAA;EACAQ,UAAAA,OAAO,CAACR,GAAG,CAAC,CAACmB,IAAI,CAACV,OAAO,CAAC,CAAA;EAC5B,SAAC,MAAM;EACLD,UAAAA,OAAO,CAACR,GAAG,CAAC,GAAGS,OAAO,CAAA;EACxB,SAAA;EACF,OAAA;EACF,KAAC,CAAC,CAAA;EACF,IAAA,OAAOH,GAAG,CAAA;KACX,EAAE,EAAE,CAAC,CAAA;IAEN,IAAIX,SAAS,YAAaQ,QAAQ,EAAE;EAClC;EACA,IAAA,CAAC,SAASE,MAAMA,CAACe,IAAI,EAAa;EAAA,MAAA,IAAXC,KAAK,GAAA9B,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,CAAC,CAAA;EAC9B,MAAA,IAAI8B,KAAK,KAAKxB,IAAI,CAACL,MAAM,EAAE;UACzB8B,MAAM,CAACzB,IAAI,CAACuB,IAAI,CAAC,CAACV,OAAO,CAAC,UAAAa,CAAC,EAAA;YAAA,OAAIH,IAAI,CAACG,CAAC,CAAC,GAAG5B,SAAS,CAACyB,IAAI,CAACG,CAAC,CAAC,CAAC,CAAA;WAAC,CAAA,CAAA;EAC9D,OAAC,MAAM;UACLD,MAAM,CAACE,MAAM,CAACJ,IAAI,CAAC,CAACV,OAAO,CAAC,UAAAe,KAAK,EAAA;EAAA,UAAA,OAAIpB,MAAM,CAACoB,KAAK,EAAEJ,KAAK,GAAG,CAAC,CAAC,CAAA;WAAC,CAAA,CAAA;EAChE,OAAA;EACF,KAAC,EAAEjB,aAAa,CAAC,CAAC;EACpB,GAAA;IAEA,IAAIsB,MAAM,GAAGtB,aAAa,CAAA;EAE1B,EAAA,IAAIR,WAAW,EAAE;EACf;EACA8B,IAAAA,MAAM,GAAG,EAAE,CAAA;EAEX,IAAA,CAAC,SAASC,OAAOA,CAACP,IAAI,EAAgB;EAAA,MAAA,IAAdQ,OAAO,GAAArC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;EAClC,MAAA,IAAIqC,OAAO,CAACpC,MAAM,KAAKK,IAAI,CAACL,MAAM,EAAE;UAClCkC,MAAM,CAACP,IAAI,CAAC;EACVtB,UAAAA,IAAI,EAAE+B,OAAO;EACbC,UAAAA,IAAI,EAAET,IAAAA;EACR,SAAC,CAAC,CAAA;EACJ,OAAC,MAAM;UACLE,MAAM,CAACQ,OAAO,CAACV,IAAI,CAAC,CACjBV,OAAO,CAAC,UAAAqB,KAAA,EAAA;EAAA,UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,KAAA,EAAA,CAAA,CAAA;EAAE/B,YAAAA,GAAG,GAAAgC,KAAA,CAAA,CAAA,CAAA;EAAEE,YAAAA,GAAG,GAAAF,KAAA,CAAA,CAAA,CAAA,CAAA;EAAA,UAAA,OAAML,OAAO,CAACO,GAAG,EAAA,EAAA,CAAAC,MAAA,CAAAC,kBAAA,CAAMR,OAAO,CAAA,EAAA,CAAE5B,GAAG,CAAA,CAAC,CAAC,CAAA;WAAC,CAAA,CAAA;EAC7D,OAAA;EACF,KAAC,EAAEI,aAAa,CAAC,CAAC;;EAElB,IAAA,IAAIV,YAAY,YAAYI,KAAK,IAAIJ,YAAY,CAACF,MAAM,KAAK,CAAC,IAAIkC,MAAM,CAAClC,MAAM,KAAK,CAAC,EAAE;EACrF;EACAkC,MAAAA,MAAM,CAAC,CAAC,CAAC,CAAC7B,IAAI,GAAG,EAAE,CAAA;EACrB,KAAA;EACF,GAAA;EAEA,EAAA,OAAO6B,MAAM,CAAA;EACf,CAAC;;;;;;;;"}