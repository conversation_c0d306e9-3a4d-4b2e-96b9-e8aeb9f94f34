{"version": 3, "file": "accessor-fn.js", "sources": ["../src/index.js"], "sourcesContent": ["export default p => typeof p === 'function'\n    ? p                     // fn\n    : typeof p === 'string'\n        ? obj => obj[p]     // property name\n        : obj => p;         // constant\n"], "names": ["p", "obj"], "mappings": ";;;;;;;AAAA,gBAAA,CAAe,UAAAA,CAAC,EAAA;IAAA,EAAA,OAAI,OAAOA,CAAC,KAAK,UAAU,GACrCA,CAAC;IAAqB,IACtB,OAAOA,CAAC,KAAK,QAAQ,GACjB,UAAAC,GAAG,EAAA;QAAA,OAAIA,GAAG,CAACD,CAAC,CAAC;OAAK;IAAA,IAClB,UAAAC,GAAG,EAAA;IAAA,IAAA,OAAID,CAAC;IAAA,GAAA;IAAA,CAAA,EAAC;;;;;;;;"}