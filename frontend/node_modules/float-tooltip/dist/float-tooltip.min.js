// Version 1.7.5 float-tooltip - https://github.com/vasturiano/float-tooltip
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t="undefined"!=typeof globalThis?globalThis:t||self).Tooltip=n()}(this,(function(){"use strict";function t(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function n(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function e(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function r(n,e){return function(t){if(Array.isArray(t))return t}(n)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,u,l=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===n);else for(;!(c=(r=i.call(e)).done)&&(l.push(r.value),l.length!==n);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(u=e.return(),Object(u)!==u))return}finally{if(s)throw o}}return l}}(n,e)||function(n,e){if(n){if("string"==typeof n)return t(n,e);var r={}.toString.call(n).slice(8,-1);return"Object"===r&&n.constructor&&(r=n.constructor.name),"Map"===r||"Set"===r?Array.from(n):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(n,e):void 0}}(n,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var i="http://www.w3.org/1999/xhtml",u={svg:"http://www.w3.org/2000/svg",xhtml:i,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function l(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),u.hasOwnProperty(n)?{space:u[n],local:t}:t}function c(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===i&&n.documentElement.namespaceURI===i?n.createElement(t):n.createElementNS(e,t)}}function s(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function a(t){var n=l(t);return(n.local?s:c)(n)}function f(){}function _(t){return null==t?f:function(){return this.querySelector(t)}}function p(){return[]}function h(t){return function(){return function(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}(t.apply(this,arguments))}}function d(t){return function(n){return n.matches(t)}}var y=Array.prototype.find;function v(){return this.firstElementChild}var m=Array.prototype.filter;function g(){return Array.from(this.children)}function b(t){return new Array(t.length)}function w(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function x(t,n,e,r,o,i){for(var u,l=0,c=n.length,s=i.length;l<s;++l)(u=n[l])?(u.__data__=i[l],r[l]=u):e[l]=new w(t,i[l]);for(;l<c;++l)(u=n[l])&&(o[l]=u)}function S(t,n,e,r,o,i,u){var l,c,s,a=new Map,f=n.length,_=i.length,p=new Array(f);for(l=0;l<f;++l)(c=n[l])&&(p[l]=s=u.call(c,c.__data__,l,n)+"",a.has(s)?o[l]=c:a.set(s,c));for(l=0;l<_;++l)s=u.call(t,i[l],l,i)+"",(c=a.get(s))?(r[l]=c,c.__data__=i[l],a.delete(s)):e[l]=new w(t,i[l]);for(l=0;l<f;++l)(c=n[l])&&a.get(p[l])===c&&(o[l]=c)}function A(t){return t.__data__}function E(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function k(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function j(t){return function(){this.removeAttribute(t)}}function O(t){return function(){this.removeAttributeNS(t.space,t.local)}}function P(t,n){return function(){this.setAttribute(t,n)}}function C(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function T(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function N(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}function M(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function U(t){return function(){this.style.removeProperty(t)}}function D(t,n,e){return function(){this.style.setProperty(t,n,e)}}function I(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function L(t){return function(){delete this[t]}}function B(t,n){return function(){this[t]=n}}function H(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function R(t){return t.trim().split(/^|\s+/)}function V(t){return t.classList||new W(t)}function W(t){this._node=t,this._names=R(t.getAttribute("class")||"")}function Y(t,n){for(var e=V(t),r=-1,o=n.length;++r<o;)e.add(n[r])}function F(t,n){for(var e=V(t),r=-1,o=n.length;++r<o;)e.remove(n[r])}function X(t){return function(){Y(this,t)}}function $(t){return function(){F(this,t)}}function q(t,n){return function(){(n.apply(this,arguments)?Y:F)(this,t)}}function G(){this.textContent=""}function z(t){return function(){this.textContent=t}}function J(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}function K(){this.innerHTML=""}function Q(t){return function(){this.innerHTML=t}}function Z(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}function tt(){this.nextSibling&&this.parentNode.appendChild(this)}function nt(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function et(){return null}function rt(){var t=this.parentNode;t&&t.removeChild(this)}function ot(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function it(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function ut(t){return function(){var n=this.__on;if(n){for(var e,r=0,o=-1,i=n.length;r<i;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++o]=e:this.removeEventListener(e.type,e.listener,e.options);++o?n.length=o:delete this.__on}}}function lt(t,n,e){return function(){var r,o=this.__on,i=function(t){return function(n){t.call(this,n,this.__data__)}}(n);if(o)for(var u=0,l=o.length;u<l;++u)if((r=o[u]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=e),void(r.value=n);this.addEventListener(t.type,i,e),r={type:t.type,name:t.name,value:n,listener:i,options:e},o?o.push(r):this.__on=[r]}}function ct(t,n,e){var r=M(t),o=r.CustomEvent;"function"==typeof o?o=new o(n,e):(o=r.document.createEvent("Event"),e?(o.initEvent(n,e.bubbles,e.cancelable),o.detail=e.detail):o.initEvent(n,!1,!1)),t.dispatchEvent(o)}function st(t,n){return function(){return ct(this,t,n)}}function at(t,n){return function(){return ct(this,t,n.apply(this,arguments))}}w.prototype={constructor:w,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},W.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var ft=[null];function _t(t,n){this._groups=t,this._parents=n}function pt(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}_t.prototype={constructor:_t,select:function(t){"function"!=typeof t&&(t=_(t));for(var n=this._groups,e=n.length,r=new Array(e),o=0;o<e;++o)for(var i,u,l=n[o],c=l.length,s=r[o]=new Array(c),a=0;a<c;++a)(i=l[a])&&(u=t.call(i,i.__data__,a,l))&&("__data__"in i&&(u.__data__=i.__data__),s[a]=u);return new _t(r,this._parents)},selectAll:function(t){t="function"==typeof t?h(t):function(t){return null==t?p:function(){return this.querySelectorAll(t)}}(t);for(var n=this._groups,e=n.length,r=[],o=[],i=0;i<e;++i)for(var u,l=n[i],c=l.length,s=0;s<c;++s)(u=l[s])&&(r.push(t.call(u,u.__data__,s,l)),o.push(u));return new _t(r,o)},selectChild:function(t){return this.select(null==t?v:function(t){return function(){return y.call(this.children,t)}}("function"==typeof t?t:d(t)))},selectChildren:function(t){return this.selectAll(null==t?g:function(t){return function(){return m.call(this.children,t)}}("function"==typeof t?t:d(t)))},filter:function(t){"function"!=typeof t&&(t=function(t){return function(){return this.matches(t)}}(t));for(var n=this._groups,e=n.length,r=new Array(e),o=0;o<e;++o)for(var i,u=n[o],l=u.length,c=r[o]=[],s=0;s<l;++s)(i=u[s])&&t.call(i,i.__data__,s,u)&&c.push(i);return new _t(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,A);var e=n?S:x,r=this._parents,o=this._groups;"function"!=typeof t&&(t=function(t){return function(){return t}}(t));for(var i=o.length,u=new Array(i),l=new Array(i),c=new Array(i),s=0;s<i;++s){var a=r[s],f=o[s],_=f.length,p=E(t.call(a,a&&a.__data__,s,r)),h=p.length,d=l[s]=new Array(h),y=u[s]=new Array(h);e(a,f,d,y,c[s]=new Array(_),p,n);for(var v,m,g=0,b=0;g<h;++g)if(v=d[g]){for(g>=b&&(b=g+1);!(m=y[b])&&++b<h;);v._next=m||null}}return(u=new _t(u,r))._enter=l,u._exit=c,u},enter:function(){return new _t(this._enter||this._groups.map(b),this._parents)},exit:function(){return new _t(this._exit||this._groups.map(b),this._parents)},join:function(t,n,e){var r=this.enter(),o=this,i=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(o=n(o))&&(o=o.selection()),null==e?i.remove():e(i),r&&o?r.merge(o).order():o},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,o=e.length,i=r.length,u=Math.min(o,i),l=new Array(o),c=0;c<u;++c)for(var s,a=e[c],f=r[c],_=a.length,p=l[c]=new Array(_),h=0;h<_;++h)(s=a[h]||f[h])&&(p[h]=s);for(;c<o;++c)l[c]=e[c];return new _t(l,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,o=t[n],i=o.length-1,u=o[i];--i>=0;)(r=o[i])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=k);for(var e=this._groups,r=e.length,o=new Array(r),i=0;i<r;++i){for(var u,l=e[i],c=l.length,s=o[i]=new Array(c),a=0;a<c;++a)(u=l[a])&&(s[a]=u);s.sort(n)}return new _t(o,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],o=0,i=r.length;o<i;++o){var u=r[o];if(u)return u}return null},size:function(){let t=0;for(const n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var o,i=n[e],u=0,l=i.length;u<l;++u)(o=i[u])&&t.call(o,o.__data__,u,i);return this},attr:function(t,n){var e=l(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?O:j:"function"==typeof n?e.local?N:T:e.local?C:P)(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?U:"function"==typeof n?I:D)(t,n,null==e?"":e)):function(t,n){return t.style.getPropertyValue(n)||M(t).getComputedStyle(t,null).getPropertyValue(n)}(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?L:"function"==typeof n?H:B)(t,n)):this.node()[t]},classed:function(t,n){var e=R(t+"");if(arguments.length<2){for(var r=V(this.node()),o=-1,i=e.length;++o<i;)if(!r.contains(e[o]))return!1;return!0}return this.each(("function"==typeof n?q:n?X:$)(e,n))},text:function(t){return arguments.length?this.each(null==t?G:("function"==typeof t?J:z)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?K:("function"==typeof t?Z:Q)(t)):this.node().innerHTML},raise:function(){return this.each(tt)},lower:function(){return this.each(nt)},append:function(t){var n="function"==typeof t?t:a(t);return this.select((function(){return this.appendChild(n.apply(this,arguments))}))},insert:function(t,n){var e="function"==typeof t?t:a(t),r=null==n?et:"function"==typeof n?n:_(n);return this.select((function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(rt)},clone:function(t){return this.select(t?it:ot)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,o,i=function(t){return t.trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}))}(t+""),u=i.length;if(!(arguments.length<2)){for(l=n?lt:ut,r=0;r<u;++r)this.each(l(i[r],n,e));return this}var l=this.node().__on;if(l)for(var c,s=0,a=l.length;s<a;++s)for(r=0,c=l[s];r<u;++r)if((o=i[r]).type===c.type&&o.name===c.name)return c.value},dispatch:function(t,n){return this.each(("function"==typeof n?at:st)(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,o=t[n],i=0,u=o.length;i<u;++i)(r=o[i])&&(yield r)}};var ht="object"==typeof global&&global&&global.Object===Object&&global,dt="object"==typeof self&&self&&self.Object===Object&&self,yt=ht||dt||Function("return this")(),vt=function(){return yt.Date.now()},mt=/\s/;var gt=/^\s+/;function bt(t){return t?t.slice(0,function(t){for(var n=t.length;n--&&mt.test(t.charAt(n)););return n}(t)+1).replace(gt,""):t}var wt=yt.Symbol,xt=Object.prototype,St=xt.hasOwnProperty,At=xt.toString,Et=wt?wt.toStringTag:void 0;var kt=Object.prototype.toString;var jt=wt?wt.toStringTag:void 0;function Ot(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":jt&&jt in Object(t)?function(t){var n=St.call(t,Et),e=t[Et];try{t[Et]=void 0;var r=!0}catch(t){}var o=At.call(t);return r&&(n?t[Et]=e:delete t[Et]),o}(t):function(t){return kt.call(t)}(t)}var Pt=/^[-+]0x[0-9a-f]+$/i,Ct=/^0b[01]+$/i,Tt=/^0o[0-7]+$/i,Nt=parseInt;function Mt(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return null!=t&&"object"==typeof t}(t)&&"[object Symbol]"==Ot(t)}(t))return NaN;if(pt(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=pt(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=bt(t);var e=Ct.test(t);return e||Tt.test(t)?Nt(t.slice(2),e?2:8):Pt.test(t)?NaN:+t}var Ut=Math.max,Dt=Math.min;function It(t,n,e){var r,o,i,u,l,c,s=0,a=!1,f=!1,_=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function p(n){var e=r,i=o;return r=o=void 0,s=n,u=t.apply(i,e)}function h(t){var e=t-c;return void 0===c||e>=n||e<0||f&&t-s>=i}function d(){var t=vt();if(h(t))return y(t);l=setTimeout(d,function(t){var e=n-(t-c);return f?Dt(e,i-(t-s)):e}(t))}function y(t){return l=void 0,_&&r?p(t):(r=o=void 0,u)}function v(){var t=vt(),e=h(t);if(r=arguments,o=this,c=t,e){if(void 0===l)return function(t){return s=t,l=setTimeout(d,n),a?p(t):u}(c);if(f)return clearTimeout(l),l=setTimeout(d,n),p(c)}return void 0===l&&(l=setTimeout(d,n)),u}return n=Mt(n)||0,pt(e)&&(a=!!e.leading,i=(f="maxWait"in e)?Ut(Mt(e.maxWait)||0,n):i,_="trailing"in e?!!e.trailing:_),v.cancel=function(){void 0!==l&&clearTimeout(l),s=0,r=c=o=l=void 0},v.flush=function(){return void 0===l?u:y(vt())},v}function Lt(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Bt(t,n,e){return Object.defineProperty(t,"prototype",{writable:!1}),t}function Ht(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,u,l=[],c=!0,s=!1;try{if(i=(e=e.call(t)).next,0===n);else for(;!(c=(r=i.call(e)).done)&&(l.push(r.value),l.length!==n);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=e.return&&(u=e.return(),Object(u)!==u))return}finally{if(s)throw o}}return l}}(t,n)||function(t,n){if(t){if("string"==typeof t)return Lt(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Lt(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Rt=Bt((function t(n,e){var r=e.default,o=void 0===r?null:r,i=e.triggerUpdate,u=void 0===i||i,l=e.onChange,c=void 0===l?function(t,n){}:l;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.name=n,this.defaultVal=o,this.triggerUpdate=u,this.onChange=c}));var Vt,Wt,Yt,Ft,Xt,$t,qt,Gt,zt,Jt,Kt,Qt,Zt={},tn=[],nn=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,en=Array.isArray;function rn(t,n){for(var e in n)t[e]=n[e];return t}function on(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function un(t,n,e,r,o){var i={type:t,props:n,key:e,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==o?++Yt:o,__i:-1,__u:0};return null==o&&null!=Wt.vnode&&Wt.vnode(i),i}function ln(t){return t.children}function cn(t,n){this.props=t,this.context=n}function sn(t,n){if(null==n)return t.__?sn(t.__,t.__i+1):null;for(var e;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e)return e.__e;return"function"==typeof t.type?sn(t):null}function an(t){var n,e;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e){t.__e=t.__c.base=e.__e;break}return an(t)}}function fn(t){(!t.__d&&(t.__d=!0)&&Xt.push(t)&&!_n.__r++||$t!==Wt.debounceRendering)&&(($t=Wt.debounceRendering)||qt)(_n)}function _n(){for(var t,n,e,r,o,i,u,l=1;Xt.length;)Xt.length>l&&Xt.sort(Gt),t=Xt.shift(),l=Xt.length,t.__d&&(e=void 0,o=(r=(n=t).__v).__e,i=[],u=[],n.__P&&((e=rn({},r)).__v=r.__v+1,Wt.vnode&&Wt.vnode(e),bn(n.__P,e,r,n.__n,n.__P.namespaceURI,32&r.__u?[o]:null,i,null==o?sn(r):o,!!(32&r.__u),u),e.__v=r.__v,e.__.__k[e.__i]=e,wn(i,e,u),e.__e!=o&&an(e)));_n.__r=0}function pn(t,n,e,r,o,i,u,l,c,s,a){var f,_,p,h,d,y,v=r&&r.__k||tn,m=n.length;for(c=hn(e,n,v,c,m),f=0;f<m;f++)null!=(p=e.__k[f])&&(_=-1===p.__i?Zt:v[p.__i]||Zt,p.__i=f,y=bn(t,p,_,o,i,u,l,c,s,a),h=p.__e,p.ref&&_.ref!=p.ref&&(_.ref&&Sn(_.ref,null,p),a.push(p.ref,p.__c||h,p)),null==d&&null!=h&&(d=h),4&p.__u||_.__k===p.__k?c=dn(p,c,t):"function"==typeof p.type&&void 0!==y?c=y:h&&(c=h.nextSibling),p.__u&=-7);return e.__e=d,c}function hn(t,n,e,r,o){var i,u,l,c,s,a=e.length,f=a,_=0;for(t.__k=new Array(o),i=0;i<o;i++)null!=(u=n[i])&&"boolean"!=typeof u&&"function"!=typeof u?(c=i+_,(u=t.__k[i]="string"==typeof u||"number"==typeof u||"bigint"==typeof u||u.constructor==String?un(null,u,null,null,null):en(u)?un(ln,{children:u},null,null,null):void 0===u.constructor&&u.__b>0?un(u.type,u.props,u.key,u.ref?u.ref:null,u.__v):u).__=t,u.__b=t.__b+1,l=null,-1!==(s=u.__i=yn(u,e,c,f))&&(f--,(l=e[s])&&(l.__u|=2)),null==l||null===l.__v?(-1==s&&_--,"function"!=typeof u.type&&(u.__u|=4)):s!=c&&(s==c-1?_--:s==c+1?_++:(s>c?_--:_++,u.__u|=4))):t.__k[i]=null;if(f)for(i=0;i<a;i++)null!=(l=e[i])&&!(2&l.__u)&&(l.__e==r&&(r=sn(l)),An(l,l));return r}function dn(t,n,e){var r,o;if("function"==typeof t.type){for(r=t.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=t,n=dn(r[o],n,e));return n}t.__e!=n&&(n&&t.type&&!e.contains(n)&&(n=sn(t)),e.insertBefore(t.__e,n||null),n=t.__e);do{n=n&&n.nextSibling}while(null!=n&&8==n.nodeType);return n}function yn(t,n,e,r){var o,i,u=t.key,l=t.type,c=n[e];if(null===c||c&&u==c.key&&l===c.type&&!(2&c.__u))return e;if(r>(null==c||2&c.__u?0:1))for(o=e-1,i=e+1;o>=0||i<n.length;){if(o>=0){if((c=n[o])&&!(2&c.__u)&&u==c.key&&l===c.type)return o;o--}if(i<n.length){if((c=n[i])&&!(2&c.__u)&&u==c.key&&l===c.type)return i;i++}}return-1}function vn(t,n,e){"-"==n[0]?t.setProperty(n,null==e?"":e):t[n]=null==e?"":"number"!=typeof e||nn.test(n)?e:e+"px"}function mn(t,n,e,r,o){var i;t:if("style"==n)if("string"==typeof e)t.style.cssText=e;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(n in r)e&&n in e||vn(t.style,n,"");if(e)for(n in e)r&&e[n]===r[n]||vn(t.style,n,e[n])}else if("o"==n[0]&&"n"==n[1])i=n!=(n=n.replace(zt,"$1")),n=n.toLowerCase()in t||"onFocusOut"==n||"onFocusIn"==n?n.toLowerCase().slice(2):n.slice(2),t.l||(t.l={}),t.l[n+i]=e,e?r?e.u=r.u:(e.u=Jt,t.addEventListener(n,i?Qt:Kt,i)):t.removeEventListener(n,i?Qt:Kt,i);else{if("http://www.w3.org/2000/svg"==o)n=n.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=n&&"height"!=n&&"href"!=n&&"list"!=n&&"form"!=n&&"tabIndex"!=n&&"download"!=n&&"rowSpan"!=n&&"colSpan"!=n&&"role"!=n&&"popover"!=n&&n in t)try{t[n]=null==e?"":e;break t}catch(t){}"function"==typeof e||(null==e||!1===e&&"-"!=n[4]?t.removeAttribute(n):t.setAttribute(n,"popover"==n&&1==e?"":e))}}function gn(t){return function(n){if(this.l){var e=this.l[n.type+t];if(null==n.t)n.t=Jt++;else if(n.t<e.u)return;return e(Wt.event?Wt.event(n):n)}}}function bn(t,n,e,r,o,i,u,l,c,s){var a,f,_,p,h,d,y,v,m,g,b,w,x,S,A,E,k,j,O=n.type;if(void 0!==n.constructor)return null;128&e.__u&&(c=!!(32&e.__u),i=[l=n.__e=e.__e]),(a=Wt.__b)&&a(n);t:if("function"==typeof O)try{if(v=n.props,m="prototype"in O&&O.prototype.render,g=(a=O.contextType)&&r[a.__c],b=a?g?g.props.value:a.__:r,e.__c?y=(f=n.__c=e.__c).__=f.__E:(m?n.__c=f=new O(v,b):(n.__c=f=new cn(v,b),f.constructor=O,f.render=En),g&&g.sub(f),f.props=v,f.state||(f.state={}),f.context=b,f.__n=r,_=f.__d=!0,f.__h=[],f._sb=[]),m&&null==f.__s&&(f.__s=f.state),m&&null!=O.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=rn({},f.__s)),rn(f.__s,O.getDerivedStateFromProps(v,f.__s))),p=f.props,h=f.state,f.__v=n,_)m&&null==O.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),m&&null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(m&&null==O.getDerivedStateFromProps&&v!==p&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(v,b),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(v,f.__s,b)||n.__v==e.__v)){for(n.__v!=e.__v&&(f.props=v,f.state=f.__s,f.__d=!1),n.__e=e.__e,n.__k=e.__k,n.__k.some((function(t){t&&(t.__=n)})),w=0;w<f._sb.length;w++)f.__h.push(f._sb[w]);f._sb=[],f.__h.length&&u.push(f);break t}null!=f.componentWillUpdate&&f.componentWillUpdate(v,f.__s,b),m&&null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(p,h,d)}))}if(f.context=b,f.props=v,f.__P=t,f.__e=!1,x=Wt.__r,S=0,m){for(f.state=f.__s,f.__d=!1,x&&x(n),a=f.render(f.props,f.state,f.context),A=0;A<f._sb.length;A++)f.__h.push(f._sb[A]);f._sb=[]}else do{f.__d=!1,x&&x(n),a=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++S<25);f.state=f.__s,null!=f.getChildContext&&(r=rn(rn({},r),f.getChildContext())),m&&!_&&null!=f.getSnapshotBeforeUpdate&&(d=f.getSnapshotBeforeUpdate(p,h)),k=(E=null!=a&&a.type===ln&&null==a.key)?a.props.children:a,E&&(a.props.children=null),l=pn(t,en(k)?k:[k],n,e,r,o,i,u,l,c,s),f.base=n.__e,n.__u&=-161,f.__h.length&&u.push(f),y&&(f.__E=f.__=null)}catch(t){if(n.__v=null,c||null!=i)if(t.then){for(n.__u|=c?160:128;l&&8==l.nodeType&&l.nextSibling;)l=l.nextSibling;i[i.indexOf(l)]=null,n.__e=l}else for(j=i.length;j--;)on(i[j]);else n.__e=e.__e,n.__k=e.__k;Wt.__e(t,n,e)}else null==i&&n.__v==e.__v?(n.__k=e.__k,n.__e=e.__e):l=n.__e=xn(e.__e,n,e,r,o,i,u,c,s);return(a=Wt.diffed)&&a(n),128&n.__u?void 0:l}function wn(t,n,e){for(var r=0;r<e.length;r++)Sn(e[r],e[++r],e[++r]);Wt.__c&&Wt.__c(n,t),t.some((function(n){try{t=n.__h,n.__h=[],t.some((function(t){t.call(n)}))}catch(t){Wt.__e(t,n.__v)}}))}function xn(t,n,e,r,o,i,u,l,c){var s,a,f,_,p,h,d,y=e.props,v=n.props,m=n.type;if("svg"==m?o="http://www.w3.org/2000/svg":"math"==m?o="http://www.w3.org/1998/Math/MathML":o||(o="http://www.w3.org/1999/xhtml"),null!=i)for(s=0;s<i.length;s++)if((p=i[s])&&"setAttribute"in p==!!m&&(m?p.localName==m:3==p.nodeType)){t=p,i[s]=null;break}if(null==t){if(null==m)return document.createTextNode(v);t=document.createElementNS(o,m,v.is&&v),l&&(Wt.__m&&Wt.__m(n,i),l=!1),i=null}if(null===m)y===v||l&&t.data===v||(t.data=v);else{if(i=i&&Vt.call(t.childNodes),y=e.props||Zt,!l&&null!=i)for(y={},s=0;s<t.attributes.length;s++)y[(p=t.attributes[s]).name]=p.value;for(s in y)if(p=y[s],"children"==s);else if("dangerouslySetInnerHTML"==s)f=p;else if(!(s in v)){if("value"==s&&"defaultValue"in v||"checked"==s&&"defaultChecked"in v)continue;mn(t,s,null,p,o)}for(s in v)p=v[s],"children"==s?_=p:"dangerouslySetInnerHTML"==s?a=p:"value"==s?h=p:"checked"==s?d=p:l&&"function"!=typeof p||y[s]===p||mn(t,s,p,y[s],o);if(a)l||f&&(a.__html===f.__html||a.__html===t.innerHTML)||(t.innerHTML=a.__html),n.__k=[];else if(f&&(t.innerHTML=""),pn("template"===n.type?t.content:t,en(_)?_:[_],n,e,r,"foreignObject"==m?"http://www.w3.org/1999/xhtml":o,i,u,i?i[0]:e.__k&&sn(e,0),l,c),null!=i)for(s=i.length;s--;)on(i[s]);l||(s="value","progress"==m&&null==h?t.removeAttribute("value"):void 0!==h&&(h!==t[s]||"progress"==m&&!h||"option"==m&&h!==y[s])&&mn(t,s,h,y[s],o),s="checked",void 0!==d&&d!==t[s]&&mn(t,s,d,y[s],o))}return t}function Sn(t,n,e){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==n||(t.__u=t(n))}else t.current=n}catch(t){Wt.__e(t,e)}}function An(t,n,e){var r,o;if(Wt.unmount&&Wt.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||Sn(r,null,n)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){Wt.__e(t,n)}r.base=r.__P=null}if(r=t.__k)for(o=0;o<r.length;o++)r[o]&&An(r[o],n,e||"function"!=typeof t.type);e||on(t.__e),t.__c=t.__=t.__e=void 0}function En(t,n,e){return this.constructor(t,e)}function kn(t,n,e){var r,o,i;n==document&&(n=document.documentElement),Wt.__&&Wt.__(t,n),r=!1?null:n.__k,o=[],i=[],bn(n,t=n.__k=function(t,n,e){var r,o,i,u={};for(i in n)"key"==i?r=n[i]:"ref"==i?o=n[i]:u[i]=n[i];if(arguments.length>2&&(u.children=arguments.length>3?Vt.call(arguments,2):e),"function"==typeof t&&null!=t.defaultProps)for(i in t.defaultProps)void 0===u[i]&&(u[i]=t.defaultProps[i]);return un(t,u,r,o,null)}(ln,null,[t]),r||Zt,Zt,n.namespaceURI,r?null:n.firstChild?Vt.call(n.childNodes):null,o,r?r.__e:n.firstChild,false,i),wn(o,t,i)}function jn(t,n,e){var r,o,i,u,l=rn({},t.props);for(i in t.type&&t.type.defaultProps&&(u=t.type.defaultProps),n)"key"==i?r=n[i]:"ref"==i?o=n[i]:l[i]=void 0===n[i]&&void 0!==u?u[i]:n[i];return arguments.length>2&&(l.children=arguments.length>3?Vt.call(arguments,2):e),un(t.type,l,r||t.key,o||t.ref,null)}Vt=tn.slice,Wt={__e:function(t,n,e,r){for(var o,i,u;n=n.__;)if((o=n.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(t)),u=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(t,r||{}),u=o.__d),u)return o.__E=o}catch(n){t=n}throw t}},Yt=0,Ft=function(t){return null!=t&&null==t.constructor},cn.prototype.setState=function(t,n){var e;e=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rn({},this.state),"function"==typeof t&&(t=t(rn({},e),this.props)),t&&rn(e,t),null!=t&&this.__v&&(n&&this._sb.push(n),fn(this))},cn.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),fn(this))},cn.prototype.render=ln,Xt=[],qt="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Gt=function(t,n){return t.__v.__b-n.__v.__b},_n.__r=0,zt=/(PointerCapture)$|Capture$/i,Jt=0,Kt=gn(!1),Qt=gn(!0);var On=function(t){if("object"!==o(t))return t;var r,i=jn(t);i.props&&(i.props=function(t){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?e(Object(o),!0).forEach((function(e){n(t,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):e(Object(o)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(o,n))}))}return t}({},i.props),null!=i&&null!==(r=i.props)&&void 0!==r&&r.children&&(i.props.children=Array.isArray(i.props.children)?i.props.children.map(On):On(i.props.children)));return i};!function(t,n){void 0===n&&(n={});var e=n.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css","top"===e&&r.firstChild?r.insertBefore(o,r.firstChild):r.appendChild(o),o.styleSheet?o.styleSheet.cssText=t:o.appendChild(document.createTextNode(t))}}(".float-tooltip-kap {\n  position: absolute;\n  width: max-content; /* prevent shrinking near right edge */\n  max-width: max(50%, 150px);\n  padding: 3px 5px;\n  border-radius: 3px;\n  font: 12px sans-serif;\n  color: #eee;\n  background: rgba(0,0,0,0.6);\n  pointer-events: none;\n}\n");var Pn,Cn,Tn,Nn,Mn,Un,Dn,In,Ln,Bn,Hn,Rn,Vn,Wn,Yn=(Pn={props:{content:{default:!1},offsetX:{triggerUpdate:!1},offsetY:{triggerUpdate:!1}},init:function(t,n){var e=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).style,i=void 0===e?{}:e,u=function(t){return"string"==typeof t?new _t([[document.querySelector(t)]],[document.documentElement]):new _t([[t]],ft)}(t&&"object"===o(t)&&t.node&&"function"==typeof t.node?t.node():t);"static"===u.style("position")&&u.style("position","relative"),n.tooltipEl=u.append("div").attr("class","float-tooltip-kap"),Object.entries(i).forEach((function(t){var e=r(t,2),o=e[0],i=e[1];return n.tooltipEl.style(o,i)})),n.tooltipEl.style("left","-10000px").style("display","none");var l="tooltip-".concat(Math.round(1e12*Math.random()));n.mouseInside=!1,u.on("mousemove.".concat(l),(function(t){n.mouseInside=!0;var e=function(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var o=n.getBoundingClientRect();return[t.clientX-o.left-n.clientLeft,t.clientY-o.top-n.clientTop]}}return[t.pageX,t.pageY]}(t),r=u.node(),o=r.offsetWidth,i=r.offsetHeight,l=[null===n.offsetX||void 0===n.offsetX?"-".concat(e[0]/o*100,"%"):"number"==typeof n.offsetX?"calc(-50% + ".concat(n.offsetX,"px)"):n.offsetX,null===n.offsetY||void 0===n.offsetY?i>130&&i-e[1]<100?"calc(-100% - 6px)":"21px":"number"==typeof n.offsetY?n.offsetY<0?"calc(-100% - ".concat(Math.abs(n.offsetY),"px)"):"".concat(n.offsetY,"px"):n.offsetY];n.tooltipEl.style("left",e[0]+"px").style("top",e[1]+"px").style("transform","translate(".concat(l.join(","),")")),n.content&&n.tooltipEl.style("display","inline")})),u.on("mouseover.".concat(l),(function(){n.mouseInside=!0,n.content&&n.tooltipEl.style("display","inline")})),u.on("mouseout.".concat(l),(function(){n.mouseInside=!1,n.tooltipEl.style("display","none")}))},update:function(t){var n,e;t.tooltipEl.style("display",t.content&&t.mouseInside?"inline":"none"),t.content?t.content instanceof HTMLElement?(t.tooltipEl.text(""),t.tooltipEl.append((function(){return t.content}))):"string"==typeof t.content?t.tooltipEl.html(t.content):function(t){return Ft(jn(t))}(t.content)?(t.tooltipEl.text(""),n=t.content,delete(e=t.tooltipEl.node()).__k,kn(On(n),e)):(t.tooltipEl.style("display","none"),console.warn("Tooltip content is invalid, skipping.",t.content,t.content.toString())):t.tooltipEl.text("")}},Cn=Pn.stateInit,Tn=void 0===Cn?function(){return{}}:Cn,Nn=Pn.props,Mn=void 0===Nn?{}:Nn,Un=Pn.methods,Dn=void 0===Un?{}:Un,In=Pn.aliases,Ln=void 0===In?{}:In,Bn=Pn.init,Hn=void 0===Bn?function(){}:Bn,Rn=Pn.update,Vn=void 0===Rn?function(){}:Rn,Wn=Object.keys(Mn).map((function(t){return new Rt(t,Mn[t])})),function t(){for(var n=arguments.length,e=new Array(n),r=0;r<n;r++)e[r]=arguments[r];var o=!!(this instanceof t?this.constructor:void 0),i=o?e.shift():void 0,u=e[0],l=void 0===u?{}:u,c=Object.assign({},Tn instanceof Function?Tn(l):Tn,{initialised:!1}),s={};function a(t){return f(t,l),_(),a}var f=function(t,n){Hn.call(a,t,c,n),c.initialised=!0},_=It((function(){c.initialised&&(Vn.call(a,c,s),s={})}),1);return Wn.forEach((function(t){a[t.name]=function(t){var n=t.name,e=t.triggerUpdate,r=void 0!==e&&e,o=t.onChange,i=void 0===o?function(t,n){}:o,u=t.defaultVal,l=void 0===u?null:u;return function(t){var e=c[n];if(!arguments.length)return e;var o=void 0===t?l:t;return c[n]=o,i.call(a,o,c,e),!s.hasOwnProperty(n)&&(s[n]=e),r&&_(),a}}(t)})),Object.keys(Dn).forEach((function(t){a[t]=function(){for(var n,e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return(n=Dn[t]).call.apply(n,[a,c].concat(r))}})),Object.entries(Ln).forEach((function(t){var n=Ht(t,2),e=n[0],r=n[1];return a[e]=a[r]})),a.resetProps=function(){return Wn.forEach((function(t){a[t.name](t.defaultVal)})),a},a.resetProps(),c._rerender=_,o&&i&&a(i),a});return Yn}));
