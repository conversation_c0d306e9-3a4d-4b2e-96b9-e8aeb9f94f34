{"version": 3, "file": "testUtils.js", "sources": ["../src/index.js"], "sourcesContent": ["import { options } from 'preact';\n\n/**\n * Setup a rerender function that will drain the queue of pending renders\n * @returns {() => void}\n */\nexport function setupRerender() {\n\toptions.__test__previousDebounce = options.debounceRendering;\n\toptions.debounceRendering = cb => (options.__test__drainQueue = cb);\n\treturn () => options.__test__drainQueue && options.__test__drainQueue();\n}\n\nconst isThenable = value => value != null && typeof value.then == 'function';\n\n/** Depth of nested calls to `act`. */\nlet actDepth = 0;\n\n/**\n * Run a test function, and flush all effects and rerenders after invoking it.\n *\n * Returns a Promise which resolves \"immediately\" if the callback is\n * synchronous or when the callback's result resolves if it is asynchronous.\n *\n * @param {() => void|Promise<void>} cb The function under test. This may be sync or async.\n * @return {Promise<void>}\n */\nexport function act(cb) {\n\tif (++actDepth > 1) {\n\t\t// If calls to `act` are nested, a flush happens only when the\n\t\t// outermost call returns. In the inner call, we just execute the\n\t\t// callback and return since the infrastructure for flushing has already\n\t\t// been set up.\n\t\t//\n\t\t// If an exception occurs, the outermost `act` will handle cleanup.\n\t\ttry {\n\t\t\tconst result = cb();\n\t\t\tif (isThenable(result)) {\n\t\t\t\treturn result.then(\n\t\t\t\t\t() => {\n\t\t\t\t\t\t--actDepth;\n\t\t\t\t\t},\n\t\t\t\t\te => {\n\t\t\t\t\t\t--actDepth;\n\t\t\t\t\t\tthrow e;\n\t\t\t\t\t}\n\t\t\t\t);\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t--actDepth;\n\t\t\tthrow e;\n\t\t}\n\t\t--actDepth;\n\t\treturn Promise.resolve();\n\t}\n\n\tconst previousRequestAnimationFrame = options.requestAnimationFrame;\n\tconst rerender = setupRerender();\n\n\t/** @type {() => void} */\n\tlet flushes = [], toFlush;\n\n\t// Override requestAnimationFrame so we can flush pending hooks.\n\toptions.requestAnimationFrame = fc => flushes.push(fc);\n\n\tconst finish = () => {\n\t\ttry {\n\t\t\trerender();\n\t\t\twhile (flushes.length) {\n\t\t\t\ttoFlush = flushes;\n\t\t\t\tflushes = [];\n\n\t\t\t\ttoFlush.forEach(x => x());\n\t\t\t\trerender();\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tif (!err) {\n\t\t\t\terr = e;\n\t\t\t}\n\t\t} finally {\n\t\t\tteardown();\n\t\t}\n\n\t\toptions.requestAnimationFrame = previousRequestAnimationFrame;\n\t\t--actDepth;\n\t};\n\n\tlet err;\n\tlet result;\n\n\ttry {\n\t\tresult = cb();\n\t} catch (e) {\n\t\terr = e;\n\t}\n\n\tif (isThenable(result)) {\n\t\treturn result.then(finish, err => {\n\t\t\tfinish();\n\t\t\tthrow err;\n\t\t});\n\t}\n\n\t// nb. If the callback is synchronous, effects must be flushed before\n\t// `act` returns, so that the caller does not have to await the result,\n\t// even though React recommends this.\n\tfinish();\n\tif (err) {\n\t\tthrow err;\n\t}\n\treturn Promise.resolve();\n}\n\n/**\n * Teardown test environment and reset preact's internal state\n */\nexport function teardown() {\n\tif (options.__test__drainQueue) {\n\t\t// Flush any pending updates leftover by test\n\t\toptions.__test__drainQueue();\n\t\tdelete options.__test__drainQueue;\n\t}\n\n\tif (typeof options.__test__previousDebounce != 'undefined') {\n\t\toptions.debounceRendering = options.__test__previousDebounce;\n\t\tdelete options.__test__previousDebounce;\n\t} else {\n\t\toptions.debounceRendering = undefined;\n\t}\n}\n"], "names": ["setup<PERSON><PERSON>nder", "options", "__test__previousDebounce", "debounceRendering", "cb", "__test__drainQueue", "isThenable", "value", "then", "actDepth", "teardown", "undefined", "result", "e", "Promise", "resolve", "toFlush", "previousRequestAnimationFrame", "requestAnimationFrame", "rerender", "flushes", "fc", "push", "err", "finish", "length", "for<PERSON>ach", "x"], "mappings": "wBAMgB,SAAAA,IAGf,OAFAC,UAAQC,EAA2BD,EAAOA,QAACE,kBAC3CF,UAAQE,kBAAoB,SAAAC,GAAE,OAAKH,EAAOA,QAACI,EAAqBD,CAAE,oBACrDH,UAAQI,GAAsBJ,EAAOA,QAACI,GAAoB,CACxE,CAEA,IAAMC,EAAa,SAAAC,UAAkB,MAATA,GAAsC,mBAAdA,EAAMC,IAAkB,EAGxEC,EAAW,EAoGC,SAAAC,IACXT,EAAOA,QAACI,IAEXJ,UAAQI,WACDJ,UAAQI,QAG+B,IAApCJ,UAAQC,GAClBD,EAAAA,QAAQE,kBAAoBF,UAAQC,SAC7BD,EAAAA,QAAQC,GAEfD,EAAAA,QAAQE,uBAAoBQ,CAE9B,aAtGO,SAAaP,GACnB,KAAMK,EAAW,EAAG,CAOnB,IACC,IAAMG,EAASR,IACf,GAAIE,EAAWM,GACd,OAAOA,EAAOJ,KACb,aACGC,CACH,EACA,SAAAI,GAEC,OADEJ,EACII,CACP,EAMH,CAHE,MAAOA,GAER,OADEJ,EACII,CACP,CAEA,QADEJ,EACKK,QAAQC,SAChB,CAEA,IAIkBC,EAJZC,EAAgChB,UAAQiB,sBACxCC,EAAWnB,IAGboB,EAAU,GAGdnB,UAAQiB,sBAAwB,SAAAG,GAAE,OAAID,EAAQE,KAAKD,EAAG,EAEtD,IAsBIE,EACAX,EAvBEY,EAAS,WACd,IAEC,IADAL,IACOC,EAAQK,QACdT,EAAUI,EACVA,EAAU,GAEVJ,EAAQU,QAAQ,SAAAC,UAAKA,GAAG,GACxBR,GAQF,CANE,MAAON,GACHU,IACJA,EAAMV,EAER,CAAC,QACAH,GACD,CAEAT,UAAQiB,sBAAwBD,IAC9BR,CACH,EAKA,IACCG,EAASR,GAGV,CAFE,MAAOS,GACRU,EAAMV,CACP,CAEA,GAAIP,EAAWM,GACd,OAAOA,EAAOJ,KAAKgB,EAAQ,SAAAD,GAE1B,MADAC,IACMD,CACP,GAOD,GADAC,IACID,EACH,MAAMA,EAEP,OAAOT,QAAQC,SAChB"}