<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
  <!--<script src="../../dist/force-graph.js"></script>-->
</head>

<body>
<div id="graph"></div>

<script>
  fetch('../datasets/miserables.json').then(res => res.json()).then(data => {
    const elem = document.getElementById('graph');

    const Graph = new ForceGraph(elem)
      .graphData(data)
      .nodeLabel('id')
      .nodeAutoColorBy('group')
      .linkDirectionalParticles(2)
      .linkDirectionalParticleWidth(1.4)
      .onNodeClick(node => {
        // Center/zoom on node
        Graph.centerAt(node.x, node.y, 1000);
        Graph.zoom(8, 2000);
      });
  });
</script>
</body>