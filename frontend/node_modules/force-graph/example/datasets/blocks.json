{"nodes": [{"id": "4062045", "user": "mbostock", "description": "Force-Directed Graph"}, {"id": "1341021", "user": "mbostock", "description": "Parallel Coordinates"}, {"id": "1341281", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Parallel Coordinates"}, {"id": "b3ff6ae1c120eea654b5", "user": "d3noob", "description": "Simple d3.js Graph"}, {"id": "4963004", "user": "calvin<PERSON><PERSON>f", "description": "Quadtree Madness Round 2"}, {"id": "4962892", "user": "calvin<PERSON><PERSON>f", "description": "Quadtree Madness Round 2"}, {"id": "4963273", "user": "calvin<PERSON><PERSON>f", "description": "Quadtree Madness Round 2"}, {"id": "4967213", "user": "calvin<PERSON><PERSON>f", "description": "Rtree Madness"}, {"id": "4060606", "user": "mbostock", "description": "Choropleth"}, {"id": "4686432", "user": "dwtkns", "description": "Faux-3d Shaded Globe"}, {"id": "3757101", "user": "mbostock", "description": "Lambert Azimuthal Equal-Area"}, {"id": "5028304", "user": "d3noob", "description": "Sankey diagram with horizontal and vertical node movement"}, {"id": "4343214", "user": "mbostock", "description": "Quadtree"}, {"id": "1346410", "user": "mbostock", "description": "Pie Chart Update, II"}, {"id": "1098617", "user": "mbostock", "description": "Arc Clock"}, {"id": "4341574", "user": "mbostock", "description": "The Amazing Pie"}, {"id": "4341417", "user": "mbostock", "description": "Donut Transitions"}, {"id": "4183330", "user": "mbostock", "description": "World Tour"}, {"id": "9656675", "user": "mbostock", "description": "Zoom to Bounding Box II"}, {"id": "4707858", "user": "mbostock", "description": "Project to Bounding Box"}, {"id": "1153292", "user": "mbostock", "description": "Mobile Patent Suits"}, {"id": "2706022", "user": "mbostock", "description": "Force-Directed Graph with <PERSON><PERSON>"}, {"id": "5141278", "user": "d3noob", "description": "Basic Directional Force Layout Diagram"}, {"id": "5107491", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Random World Tour with flags"}, {"id": "5044313", "user": "mbostock", "description": "Gray Earth"}, {"id": "5141528", "user": "d3noob", "description": "Directional Force Layout Diagram with Node Highlighting"}, {"id": "4342045", "user": "mbostock", "description": "Symbol Map"}, {"id": "3711652", "user": "mbostock", "description": "Projection Transitions"}, {"id": "4063530", "user": "mbostock", "description": "Circle Packing"}, {"id": "3305854", "user": "mbostock", "description": "Custom Tweens"}, {"id": "5180185", "user": "mbostock", "description": "Zipdecode"}, {"id": "5386999", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Zipdecode Switzerland"}, {"id": "5416440", "user": "mbostock", "description": "Merging States II"}, {"id": "5416405", "user": "mbostock", "description": "Merging States"}, {"id": "4061961", "user": "mbostock", "description": "Bullet Charts"}, {"id": "5518052", "user": "milro<PERSON>", "description": "bar + sum: vanilla d3.js"}, {"id": "5519642", "user": "milro<PERSON>", "description": "bar + sum: reusable d3.js"}, {"id": "5519819", "user": "milro<PERSON>", "description": "bar + sum: d3.js & angular.js"}, {"id": "5520449", "user": "milro<PERSON>", "description": "bar + sum: d3.js & ember.js "}, {"id": "5522467", "user": "milro<PERSON>", "description": "bar + sum: d3.js & backbone.js"}, {"id": "d22bbf92231876505e5d", "user": "milro<PERSON>", "description": "bar + sum: d3.js, react.js, & Flux"}, {"id": "5553051", "user": "milro<PERSON>", "description": "    "}, {"id": "5593150", "user": "mbostock", "description": "Vector Tiles"}, {"id": "1642989", "user": "mbostock", "description": "Spline Transition"}, {"id": "1705868", "user": "mbostock", "description": "Point-Along-Path Interpolation"}, {"id": "5662135", "user": "cloudshapes", "description": "attrTween in a Transition to Move an Element Using a Function"}, {"id": "1667367", "user": "mbostock", "description": "Focus+Context via Brushing"}, {"id": "4180634", "user": "mbostock", "description": "World Map"}, {"id": "5732685"}, {"id": "4973620", "user": "dwtkns", "description": "Faux-3D Arcs"}, {"id": "5732719", "user": "darrenjaworski", "description": "TOPOJSON from shapefile - OK Census tracts"}, {"id": "5616813", "user": "mbostock", "description": "Vector Tiles"}, {"id": "5874133", "user": "darrenjaworski", "description": "Tutorial I"}, {"id": "5874214", "user": "darrenjaworski", "description": "Tutorial II"}, {"id": "5874227", "user": "darrenjaworski", "description": "Tutorial III"}, {"id": "5874229"}, {"id": "5874233"}, {"id": "5874236"}, {"id": "5397362", "user": "darrenjaworski", "description": "Continuous gradient key"}, {"id": "5886992", "user": "d3noob", "description": "Simple d3.js Bullet Chart example"}, {"id": "1021953", "user": "mbostock", "description": "Multi-Foci Force Layout"}, {"id": "5691513"}, {"id": "1389927", "user": "mbostock", "description": "A Bar Chart"}, {"id": "1129492", "user": "mbostock", "description": "Bounded Force Layout"}, {"id": "5820393", "user": "karmadude", "description": "San Francisco Contours"}, {"id": "6052814", "user": "nbremer", "description": "Self Organizing Map - Heatmap - D3"}, {"id": "6052681", "user": "nbremer", "description": "Self Organizing Map - Hexagonal Heatmap - Lines"}, {"id": "6187523", "user": "CodeXmonk", "description": "BulleT: a variant based on <PERSON><PERSON><PERSON>'s bullet with <PERSON><PERSON>'s d3.chart.js"}, {"id": "5249328", "user": "mbostock", "description": "Hexagon <PERSON>"}, {"id": "6150151", "user": "nitaku", "description": "Colored hex regions"}, {"id": "3887118", "user": "mbostock", "description": "Scatterplot"}, {"id": "6112167", "user": "CodeXmonk", "description": "Simple modification based on mbostock's Bullet Charts."}, {"id": "1703449", "user": "christopher<PERSON><PERSON>", "description": "Hamiltonian Graph Builder"}, {"id": "6203413", "user": "christopher<PERSON><PERSON>", "description": "CTA Line Simplification"}, {"id": "4062085", "user": "mbostock", "description": "Population Pyramid"}, {"id": "6226150", "user": "cmdoptesc", "description": "JavaScript D3: Drawing Concentric Arcs"}, {"id": "5100636", "user": "mbostock", "description": "Arc Tween"}, {"id": "6228457", "user": "cmdoptesc", "description": "JavaScript D3: Arc tween transitions using attrTween and attr methods"}, {"id": "6279966", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "France_topographic_map.svg"}, {"id": "5649592", "user": "mbostock", "description": "Stroke Dash Interpolation"}, {"id": "2846454", "user": "mbostock", "description": "Force Layout & Matrix Market Format"}, {"id": "4060366", "user": "mbostock", "description": "Voronoi Tessellation"}, {"id": "1405439", "user": "njvack", "description": "Voronoi-based point picker"}, {"id": "1734663", "user": "christopher<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>agram with Force Directed Nodes and Delaunay Links"}, {"id": "6439398", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Kobe Bryant 2012-13 Game Log Line Graph"}, {"id": "3884955", "user": "mbostock", "description": "Multi-Series Line Chart"}, {"id": "6459889", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "US Map of Nielsen Media Markets"}, {"id": "2206590", "user": "mbostock", "description": "click-to-zoom via transform"}, {"id": "3019563", "user": "mbostock", "description": "Margin Convention"}, {"id": "3894205", "user": "mbostock", "description": "Difference Chart"}, {"id": "3885304", "user": "mbostock", "description": "Bar Chart"}, {"id": "21746a9668ffdf6d8242", "user": "nbremer", "description": "Radar Chart Redesign"}, {"id": "6514960", "user": "nitaku", "description": "Hilbert curve (L-system)"}, {"id": "11094667", "user": "ndarville", "description": "Party Trend (Multi-Party Display)"}, {"id": "5230564", "user": "mbostock", "description": "Times"}, {"id": "6901037", "user": "nitaku", "description": "Circles"}, {"id": "6622700", "user": "nitaku", "description": "GeoJSON squares"}, {"id": "6173659", "user": "nitaku", "description": "Hex coordinates"}, {"id": "7023140", "user": "nitaku", "description": "Gosper hex tiling"}, {"id": "7008736", "user": "nitaku", "description": "Custom hex projection"}, {"id": "6521802", "user": "nitaku", "description": "Gosper curve (L-system)"}, {"id": "7015186", "user": "nitaku", "description": "Gosper hex tiling (fancy)"}, {"id": "7023794", "user": "nitaku", "description": "Gosper regions"}, {"id": "6982528", "user": "nitaku", "description": "TopoJSON geometrical renderer"}, {"id": "7025272", "user": "nitaku", "description": "Precomputed Gosper regions"}, {"id": "7051552", "user": "phil-pedruco", "description": "Disable zoom on scrollwheel but retain the ability to pan #2"}, {"id": "7051484", "user": "phil-pedruco", "description": "Disable zoom on scrollwheel but retain the ability to pan #1"}, {"id": "4063423", "user": "mbostock", "description": "Sunburst Partition"}, {"id": "7032353", "user": "radiocontrolled", "description": "Humanitarian population by major Canadian city, 2012"}, {"id": "4237768", "user": "mbostock", "description": "<PERSON><PERSON><PERSON><PERSON> C<PERSON>"}, {"id": "3846051", "user": "mbostock", "description": "Voronoi tests"}, {"id": "7233233", "user": "nitaku", "description": "Voronoi"}, {"id": "1157787", "user": "mbostock", "description": "Small Multiples"}, {"id": "6476579", "user": "Caged", "description": "Using d3-tip to add tooltips to a d3 bar chart"}, {"id": "2295155"}, {"id": "3680999", "user": "mbostock", "description": "SVG Geometric Zooming"}, {"id": "7328902", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "My attempt to add templating to d3.js"}, {"id": "3750558", "user": "mbostock", "description": "Sticky Force Layout"}, {"id": "7483341", "user": "nitaku", "description": "ID-based force layout"}, {"id": "7493693", "user": "nitaku", "description": "Graph editing"}, {"id": "5001347", "user": "r<PERSON><PERSON><PERSON>", "description": "Directed Graph Editor"}, {"id": "7512487", "user": "nitaku", "description": "Graph editing tools"}, {"id": "6890861", "user": "nitaku", "description": "IndexedDB"}, {"id": "7517984", "user": "nitaku", "description": "Graph editing with persistence"}, {"id": "7475950", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "A new attempt to add templating to d3.js"}, {"id": "5052095", "user": "timelyportfolio", "description": ""}, {"id": "7188018", "user": "radiocontrolled", "description": "Humanitarian population by Canadian region, 2012"}, {"id": "4339184", "user": "mbostock", "description": "<PERSON><PERSON><PERSON>–Tilford Tree"}, {"id": "1093025", "user": "mbostock", "description": "Collapsible Indented Tree"}, {"id": "7910706", "user": "nitaku", "description": "Random tree"}, {"id": "7962295"}, {"id": "7938492", "user": "nitaku", "description": "Random tree II"}, {"id": "7960622"}, {"id": "4136647", "user": "mbostock", "description": "U.S. TopoJSON"}, {"id": "8028264", "user": "nitaku", "description": "Fractal treemap (random, Go<PERSON>)"}, {"id": "7518871", "user": "nitaku", "description": "Graph with immutable nodes and links"}, {"id": "3757132", "user": "mbostock", "description": "Spherical Mercator"}, {"id": "8031205", "user": "nitaku", "description": "Fractal tree depth map (random, Go<PERSON>)"}, {"id": "8260510", "user": "nitaku", "description": "Canonical representation of unordered rooted trees"}, {"id": "8185373", "user": "rveciana", "description": "Base map for Haiyan typhoon track"}, {"id": "1346395", "user": "mbostock", "description": "Pie Chart Update, I"}, {"id": "8244446", "user": "nitaku", "description": "Random tree (unordered)"}, {"id": "8272105"}, {"id": "8272715", "user": "nitaku", "description": "Fractal treemap of Konanopii (1,724 leaves)"}, {"id": "3014589", "user": "mbostock", "description": "Lab and HCL Color Spaces"}, {"id": "8699346", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "D3 Authors by Domain"}, {"id": "3808218", "user": "mbostock", "description": "General Update <PERSON><PERSON>, I"}, {"id": "8772179", "user": "nitaku", "description": "Space-flling curve layout (<PERSON><PERSON>)"}, {"id": "8784278", "user": "nitaku", "description": "Jigsaw treemap (Gosper)"}, {"id": "8784942", "user": "nitaku", "description": "Jigsaw treemap (<PERSON><PERSON>)"}, {"id": "8787102", "user": "nitaku", "description": "Hilbert treemap (area encoding)"}, {"id": "4341134", "user": "mbostock", "description": "Hierarchical Edge Bundling"}, {"id": "8788101", "user": "nitaku", "description": "Gosper treemap with hierarchical edge bundling"}, {"id": "8566245", "user": "nitaku", "description": "Categorical color scale generator (HCL)"}, {"id": "8246732", "user": "radiocontrolled", "description": "What per cent of people in the UK have used the Internet?"}, {"id": "8900555", "user": "nitaku", "description": "Coloring of intersecting regions"}, {"id": "8882722", "user": "nitaku", "description": "Label placement for Hilbert treemaps"}, {"id": "8955159", "user": "nitaku", "description": "Stability of <PERSON><PERSON><PERSON> curve"}, {"id": "8947871", "user": "nitaku", "description": "Stable Hilbert curve"}, {"id": "8947565", "user": "nitaku", "description": "Unstable Hilbert curve"}, {"id": "6531064", "user": "nitaku", "description": "Peano (?) curve (L-system)"}, {"id": "8949471", "user": "nitaku", "description": "Peano curve (L-system)"}, {"id": "8968230", "user": "nitaku", "description": "FASS curve I (L-system)"}, {"id": "4063269", "user": "mbostock", "description": "Bubble Chart"}, {"id": "4327678", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Swiss Cantons and Municipalities"}, {"id": "9205257", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Transitions with React and D3 I"}, {"id": "9205264", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Transitions with React and D3 II"}, {"id": "9205238", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Transitions with D3"}, {"id": "9216081"}, {"id": "9216751", "user": "vlandham"}, {"id": "9204795", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Swiss Cantons and Municipalities with React"}, {"id": "9265219", "user": "bollwyvl", "description": "A fishbone editor"}, {"id": "9239214", "user": "bollwyvl", "description": "d3 fishbone"}, {"id": "9572040", "user": "dbuezas", "description": "Pie Chart Labels with missing data"}, {"id": "5126418", "user": "mbostock", "description": "New Jersey State Plane"}, {"id": "4573883", "user": "mbostock", "description": "Threshold Key"}, {"id": "9417919", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "3886208", "user": "mbostock", "description": "Stacked Bar Chart"}, {"id": "950642", "user": "mbostock", "description": "Labeled Force Layout"}, {"id": "9746652", "user": "erkal", "description": "Directed Links with Polygons"}, {"id": "9746628", "user": "erkal", "description": "Mobile Patent Suits with Polygon Links"}, {"id": "7607535", "user": "mbostock", "description": "Zoomable Circle Packing"}, {"id": "7090426", "user": "kerryrodden", "description": "Sequences sunburst"}, {"id": "9845999", "user": "milro<PERSON>", "description": "<PERSON><PERSON>'s Bar, II"}, {"id": "9842179"}, {"id": "9842284"}, {"id": "9842512", "user": "milro<PERSON>", "description": "<PERSON><PERSON>'s Bar, I"}, {"id": "9871234", "user": "erkal", "description": "General Update Pat<PERSON> with play<PERSON><PERSON><PERSON>"}, {"id": "9871048", "user": "erkal", "description": "playButton"}, {"id": "9914167", "user": "erkal", "description": "waitAndEval II"}, {"id": "9912897", "user": "erkal", "description": "waitAndEval"}, {"id": "4282586", "user": "mbostock", "description": "Three-Axis Rotation"}, {"id": "9733594", "user": "VisDockHub", "description": "VisDock in Stacked Bar Chart"}, {"id": "9972460", "user": "<PERSON><PERSON><PERSON>", "description": "Another brushing with d3"}, {"id": "11085004", "user": "monfera", "description": "circinus b"}, {"id": "3795544", "user": "mbostock", "description": "Adaptive Resampling"}, {"id": "5732029", "user": "mbostock", "description": "Line Simplification"}, {"id": "4090846"}, {"id": "4163057", "user": "mbostock", "description": "Gradient Along Stroke"}, {"id": "10990305", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Remote football stadiums (with labels)"}, {"id": "10989553", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Most remote football stadiums"}, {"id": "10527804", "user": "monfera", "description": "circinus"}, {"id": "5731632", "user": "mbostock", "description": "Orthographic to Equirectangular"}, {"id": "1256572", "user": "mbostock", "description": "D3 Show Reel"}, {"id": "6574995", "user": "ndarville", "description": "Party Trend (Single-Party Display)"}, {"id": "7e8e57368175a1433791", "user": "<PERSON><PERSON><PERSON>", "description": "SVG scaled overlay in Leaflet "}, {"id": "11273546", "user": "radiocontrolled", "description": "Interactive thermometer, version 2"}, {"id": "9885869", "user": "radiocontrolled", "description": "Interactive thermometer"}, {"id": "ce9c2deb92042df13179", "user": "georules", "description": "oh yes spiral"}, {"id": "6958e230e69a377620e3", "user": "georules", "description": "oh yes spiral"}, {"id": "d67e0c64f0cb392f8a78", "user": "georules", "description": "wankel spiral"}, {"id": "3cc1a2a289dddbd64688", "user": "curran", "description": "Fundamental Visualizations"}, {"id": "c8b4472a648b08a26720", "user": "vicapow", "description": "fresh block"}, {"id": "a23f857ee922bea4bded", "user": "vicapow", "description": "fresh block"}, {"id": "818723e652995b6c110d", "user": "vicapow", "description": "fresh block"}, {"id": "310c99e53880faec2434", "user": "mbostock", "description": "A Less-Angry Rainbow"}, {"id": "76342abc327062128604", "user": "mbostock", "description": "Color Mesh II"}, {"id": "3150059", "user": "syntagmatic", "description": "Nutrient Parallel Coordinates"}, {"id": "eb9cddd11657887e1e0d", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Curriculum Exploration"}, {"id": "5194141", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Issue Breakdown"}, {"id": "c256fda4965d38e7c59b", "user": "syntagmatic", "description": "Hexbin Heightmap Transitions"}, {"id": "1092fe86860def004ea7", "user": "syntagmatic", "description": "<PERSON><PERSON><PERSON>"}, {"id": "3289530", "user": "mbostock", "description": "Heightmap"}, {"id": "4248145", "user": "mbostock", "description": "Hexagonal Binning"}, {"id": "280d83080497c8c13152", "user": "mbostock", "description": "Connected Particles III"}, {"id": "b3831331f475fdcb13df", "user": "mbostock", "description": "Poisson-Disc V"}, {"id": "9c87aed9a53a3ff20e29", "user": "enoex", "description": "Poisson-Disc V"}, {"id": "e7a13ff417ff5257a067", "user": "enoex", "description": "Poisson-Disc V"}, {"id": "1af08ad6cdb01707c33f", "user": "curran", "description": "Chiasm Boilerplate"}, {"id": "60b40877ef898f19aeb8", "user": "curran", "description": "Standalone Line Chart"}, {"id": "3887235", "user": "mbostock", "description": "Pie Chart"}, {"id": "134ed87c99257e3f2e31", "user": "curran", "description": "Standalone Scatter Plot"}, {"id": "0e57e7fff27f2eee431f", "user": "enoex", "description": "Fundamental Visualizations"}, {"id": "7e830bd65ad749237e4d", "user": "enoex", "description": "data yoga 1"}, {"id": "620ebec1c2841ea5a929", "user": "enoex", "description": "breathe"}, {"id": "4188334", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "World Map"}, {"id": "6123708", "user": "mbostock", "description": "Drag + Zoom"}, {"id": "8beea1d918ff4104f9ab", "user": "d3noob", "description": "Simple Difference Chart"}, {"id": "e5daff57a04c2639125e", "user": "d3noob", "description": "Favorite tooltip (simple version)"}, {"id": "3902569", "user": "mbostock", "description": "X-Value Mouseover"}, {"id": "4618602"}, {"id": "c2637e28b79fb3bfea13", "user": "d3noob", "description": "Sankey diagram from formatted JSON"}, {"id": "38744a17f9c0141bcd04", "user": "d3noob", "description": "Simple scatterplot with d3.js"}, {"id": "473f0cf66196a008cf99", "user": "d3noob", "description": "Simple graph and table using d3.js"}, {"id": "7030f35b72de721622b8", "user": "d3noob", "description": "Update d3.js data with button press"}, {"id": "4ad06b0d9ee2a390eb20", "user": "sampathweb", "description": "datasana #1"}, {"id": "2efd8084d345dd23ef96", "user": "sampathweb", "description": "datasana #circles"}, {"id": "29a00092e4dbb7c659f7", "user": "sxywu", "description": "visfest block visualization"}, {"id": "b3b3ff937241182ee67e", "user": "sxywu", "description": "visfest block visualization 2"}, {"id": "33bc932b850792e832f8", "user": "sxywu", "description": "How D3 Transitions Work, Pt. 1: d3.interpolate"}, {"id": "3a81930c054bb8ee39bf", "user": "sxywu", "description": "How D3 Transitions Work, Pt. 2: d3.interpolate with multiple elements"}, {"id": "779f2db53e1e365e98d4", "user": "curran", "description": "Density-based Ticks"}, {"id": "28f1fbeb41df9753e120", "user": "syntagmatic", "description": "Issue Breakdown"}, {"id": "002ef2fb5c1dc2df5821", "user": "syntagmatic", "description": "Hexbin Canvas Transitions"}, {"id": "1308257", "user": "mbostock", "description": "The Euro Debt Crisis"}, {"id": "433a9b04f82a1c78af4b", "user": "emeeks", "description": "Modal Infoboxes - d3.carto.map"}, {"id": "6746848", "user": "mbostock", "description": "See-Through Globe"}, {"id": "c88435cb568dcaf11308", "user": "emeeks", "description": "Non-Rotating SVG Transparent Globe"}, {"id": "29bccce80df0f253c97e", "user": "syntagmatic", "description": "Quantile, Quantize, Threshold Scales"}, {"id": "6769077", "user": "syntagmatic", "description": "Github Users Worldwide"}, {"id": "6419716", "user": "syntagmatic", "description": "Force-directed Splitting"}, {"id": "0d1635533f6fb5ac4da3", "user": "syntagmatic", "description": "Nutrient Parallel Coordinates II"}, {"id": "895eb781d88148cfd8e5", "user": "syntagmatic", "description": "Clock"}, {"id": "ba569633d51ebec6ec6e", "user": "syntagmatic", "description": "Comparing Map Projections"}, {"id": "8fadc5ac9c2a9e7c5ba2", "user": "mbostock", "description": "Map Pan & Zoom I"}, {"id": "7352810", "user": "linssen", "description": "Programmatically and smoothly zoom to the centre."}, {"id": "c2cc7242c8f800c736c4", "user": "mgold", "description": "<PERSON>m <PERSON> I"}, {"id": "bbc451a7b9f902954e7c", "user": "mgold", "description": "Zoom Buttons II"}, {"id": "81271937fa94fdbdd854", "user": "curran", "description": "Migrant Deaths Map (direct)"}, {"id": "87d038562333a7ad4a64", "user": "curran", "description": "Crossfilter & Chiasm"}, {"id": "47aceae44bb5f8b63d7b", "user": "boeric", "description": "Deaths of Migrants"}, {"id": "60eec0e1727c6c628728", "user": "s<PERSON>b<PERSON><PERSON>", "description": "Migrants-Deaths-Visualization"}, {"id": "3943967", "user": "mbostock", "description": "Stacked-to-Grouped Bars"}, {"id": "752b97cef3f880a813ab", "user": "curran", "description": "World City Explorer"}, {"id": "a931688fa8cb65db4079", "user": "curran", "description": "Magic Bar Chart (Browserified)"}, {"id": "b6e1d23c16dc76371a92", "user": "curran", "description": "Magic Bar Chart"}, {"id": "2c736a99c2f57bfac7e4", "user": "curran", "description": "Margin Convention with Model.js"}, {"id": "87d5dd02739089986d4a", "user": "curran", "description": "fresh block"}, {"id": "3a68b0c81991e2e94b19", "user": "curran", "description": "Responding to Resize"}, {"id": "d1e9ea2850047562be09", "user": "curran", "description": "Focus + Context Scatter Plots"}, {"id": "cf4b98fff0517ca04667", "user": "curran", "description": "Scatter Plot Zooming"}, {"id": "ba63c55dd2dbc3ab0127", "user": "mbostock", "description": "This Is a Globe"}, {"id": "e2744ee563f7cab80350"}, {"id": "bd552e711b8325c64729", "user": "en<PERSON><PERSON>", "description": "visualizing map distortion"}, {"id": "66d926fe73211fd650ec", "user": "curran", "description": "Towards Reusable Charts Example"}, {"id": "8033015", "user": "mbostock", "description": "Multi-Line Voronoi"}, {"id": "5577023", "user": "mbostock", "description": "Every ColorBrewer Scale"}, {"id": "5537697", "user": "mbostock", "description": "Log Axis"}, {"id": "a22c42db65eb00d4e369", "user": "d3noob", "description": "Simple d3.js tooltips"}, {"id": "9e04ccfebeb84bcdc76c", "user": "curran", "description": "Scatter Plot"}, {"id": "4ce2ee825811f1c32125", "user": "curran", "description": "Chiasm Bar Chart and Line Chart"}, {"id": "3b811f05a0ce39d0d7cd", "user": "curran", "description": "Data Canvas Part 4 - Colors"}, {"id": "015d34d6d3d562877e51", "user": "curran", "description": "Data Canvas Part 3 - Bar Chart"}, {"id": "015402cce2caa074551e", "user": "curran", "description": "Data Canvas Part 2 - Line Chart"}, {"id": "dd73d3d8925cdf50df86", "user": "curran", "description": "Picking N Colors Automatically"}, {"id": "5905182da50a4667dc00", "user": "curran", "description": "Reactive Flow Diagram"}, {"id": "5f255332a9dcb9906f84", "user": "curran", "description": "Data Canvas Part 1 - Data"}, {"id": "0854f60e489b5ee6e11a", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Animating Circles (D3 & SVG)"}, {"id": "40a7be58639daf5d510e", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Animating Circles (React & SVG)"}, {"id": "dc5be7c1d700eb6ecf46", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Animating Circles (Canvas)"}, {"id": "b6bdb82045c2aa8225f5", "user": "<PERSON><PERSON><PERSON>", "description": "Annual Temp - New York 2015"}, {"id": "2fffa9abe50ac97603c7", "user": "emeeks", "description": "Brushable Radial Chart"}, {"id": "95800d30829ab8ab4ba6", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "stage fright"}, {"id": "b602b35c682ec69b9025", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "stage fright 2"}, {"id": "f2aff83148a5f64f3222", "user": "micahstubbs", "description": "Animated ROC Chart"}, {"id": "f1b4a29749408dd826ef"}, {"id": "465725cdc547c7cc8491", "user": "micahstubbs", "description": "radar chart with smallest area on top"}, {"id": "44bb05aab218a40a4c12", "user": "micahstubbs", "description": "radar chart for nested data"}, {"id": "2975320", "user": "ZJONSSON", "description": "barStack - stacking with negative values"}, {"id": "6100713"}, {"id": "a40254b6cb914018ff81", "user": "micahstubbs", "description": "stacking with negative values"}, {"id": "5661984", "user": "cloudshapes", "description": "Dotted Path Following the Mouse"}, {"id": "eda532b01c24bf4ac1d6", "user": "timelyportfolio", "description": "BioFabric in R as an htmlwidget with d3.js"}, {"id": "7148ee2a60a09fed0b43", "user": "timelyportfolio", "description": "biofabric as an rstats htmlwidget - pre-alpha"}, {"id": "a6916e88484f962d8f95", "user": "timelyportfolio", "description": "R | <PERSON><PERSON><PERSON>'s English Letter ngrams with sunburstR (d3.js sequence sunburst htmlwidget)"}, {"id": "3793462de638478937"}, {"id": "d6658135f11023171587", "user": "timelyportfolio", "description": "R base graphics plot using stateface from propublica"}, {"id": "58f8fe3e167ef47145d0", "user": "timelyportfolio", "description": "R + d3.js Parallel Coordinates of partykit ver 2 with interactive querying"}, {"id": "d4ff502dc32b5b04eae4", "user": "timelyportfolio", "description": "R + d3.js <PERSON><PERSON><PERSON> Coordinates of partykit"}, {"id": "adc2dfee7aef48ce5485", "user": "timelyportfolio", "description": " rCharts + d3.js view of rpart with sankey-like link width"}, {"id": "7880033", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "D3.js Drag and Drop, Zoomable, Panning, Collapsible Tree with auto-sizing."}, {"id": "782b91b09326541a0fc8"}, {"id": "a5df8dc94786a27d6420", "user": "en<PERSON><PERSON>", "description": "datasana #1"}, {"id": "859c7b7150c81cc512b3", "user": "rcrocker13", "description": "datasana #1"}, {"id": "67648b87e80fbc31ad20", "user": "arrayjam", "description": "Overlapping Geographic Circles"}, {"id": "5c4288aa30de1b949433", "user": "arrayjam", "description": "Non-Overlapping Geographic Circles"}, {"id": "5348789", "user": "mbostock", "description": "Concurrent Transitions"}, {"id": "7ef773bdf11999c9eebb", "user": "maartenzam", "description": "Responsive map with Cartodb/Leaflet"}, {"id": "1a5e8d77ae8feb464167", "user": "<PERSON><PERSON><PERSON>", "description": "Violin Plot + Box Plot v2"}, {"id": "d15a4f16fa618273e10f", "user": "<PERSON><PERSON><PERSON>", "description": "Reusable Violin + Box Plot"}, {"id": "5014368"}, {"id": "6506614", "user": "nbremer", "description": "D3.js - Radar Chart or Spider Chart - Adjusted from radar-chart-d3"}, {"id": "3a33bfdeda5b8f8c8cd2", "user": "nbremer", "description": "Text on an Arc - Animal Donut Slices - Default Arc Labels"}, {"id": "b603c3e0f7a74794da87", "user": "nbremer", "description": "Text on an Arc - Animal Donut Slices - Flipped & Centered Arc Labels"}, {"id": "7b051187fe329d705ee9", "user": "nbremer", "description": "Text on an Arc - Animal Donut Slices - Centered Arc Labels"}, {"id": "25e2db4abc9eaf080190", "user": "nbremer", "description": "Text on an Arc - Animate from a Wavy line to an Arc"}, {"id": "3916621", "user": "mbostock", "description": "Path Tween"}, {"id": "bf3d285e48189507e0ea", "user": "nbremer", "description": "Text on an Arc - Animate from Arc to an Arc"}, {"id": "f9dacd23eece9d23669c", "user": "nbremer", "description": "Stretched Chord Diagram - From educations to occupations"}, {"id": "5b16c2a5716d346e2f34", "user": "nbremer", "description": "Stretched Chord - Step 4 - How to pull the two halves apart"}, {"id": "c11409af47b5950f0289", "user": "nbremer", "description": "Stretched Chord - The Final Result - Including the hover effects"}, {"id": "2e10bf27fa0f9f49588b", "user": "nbremer", "description": "Stretched Chord - Step 3 - Different Color Scheme"}, {"id": "b99e416bd99fc0191b50", "user": "nbremer", "description": "Stretched Chord - Step 5 - How to make the chords touch the pulled apart arcs again"}, {"id": "dcd01f820aa098133596", "user": "nbremer", "description": "Stretched Chord - Step 3 - How to make the two sections symmetrical"}, {"id": "8295697aee7844da2299", "user": "nbremer", "description": "Stretched Chord - Step 2 - How to create an empty section in between the two halves"}, {"id": "1527c2dd26a26d9ee942", "user": "nbremer", "description": "Stretched Chord - Step 1 - How to set up a Chord Diagram matrix to show Flows"}, {"id": "f6952f1ef900d84be918", "user": "nbremer", "description": "Gooey Effect - Line"}, {"id": "69808ec7ec07542ed7df", "user": "nbremer", "description": "Gooey Effect - Circle"}, {"id": "a3684c52fb527c8fa415", "user": "nbremer", "description": "Gooey effect - Rectangle"}, {"id": "8e25e1434cdd6db25b69", "user": "nbremer", "description": "<PERSON>'s day Heart curve"}, {"id": "df7f87361bb7e297e751", "user": "nbremer", "description": "<PERSON>'s day Heart curve - One Line"}, {"id": "2b1446f496c3968f6ecd", "user": "en<PERSON><PERSON>", "description": "Stacked-to-Grouped Police Killings"}, {"id": "7833311", "user": "mbostock", "description": "Dynamic Hexbin"}, {"id": "f3225fa363fbaccb8c9e", "user": "en<PERSON><PERSON>", "description": "visfest experiment #1"}, {"id": "a74519c5a055b2903b41", "user": "en<PERSON><PERSON>", "description": "visfest experiment #2"}, {"id": "98cf105e8531ff12013b"}, {"id": "e7f4634cd71473c9658c"}, {"id": "f8c41743ca81d7828c3a", "user": "en<PERSON><PERSON>", "description": "visfest experiment #3"}, {"id": "7492cc85b1311f69ba3f", "user": "en<PERSON><PERSON>", "description": "morph experiment #1"}, {"id": "02ac156db28e61ff20bb", "user": "en<PERSON><PERSON>", "description": "morph experiment #2"}, {"id": "ebda1656976d90e2a636", "user": "en<PERSON><PERSON>", "description": "morph experiment #3"}, {"id": "20d4098ce7ed88dde5c4", "user": "en<PERSON><PERSON>", "description": "viSFest logo: small multiples"}, {"id": "7ea44d0bc364b6bddbd8", "user": "en<PERSON><PERSON>", "description": "The Migrant Files: Deaths"}, {"id": "d6696d28fb47fcf8a537", "user": "en<PERSON><PERSON>", "description": "The Migrant Files: Deportations"}, {"id": "d6a92a077e78fd33316a", "user": "en<PERSON><PERSON>", "description": "The Migrant Files: Money"}, {"id": "157333662ef11c151080", "user": "mbostock", "description": "Connected Particles"}, {"id": "7f14a531dec565ca0884", "user": "vicapow", "description": "WebGL + d3.layout.force"}, {"id": "4bd6d92fd553bb9bac11", "user": "en<PERSON><PERSON>", "description": "sparse matrix zoo"}, {"id": "42a531c5d87bfc91036e", "user": "en<PERSON><PERSON>", "description": "sparse matrix zoo: canvas"}, {"id": "5109a9fbd5f9a7230f17", "user": "en<PERSON><PERSON>", "description": "hilbert grid"}, {"id": "77cfa8eb62e34833ae7c", "user": "en<PERSON><PERSON>", "description": "hilbert grid marching"}, {"id": "e662bb2e2c0783ee82f9", "user": "en<PERSON><PERSON>", "description": "hilbert grid creeping"}, {"id": "6ac67b0d8ed673c9aa61", "user": "en<PERSON><PERSON>", "description": "all the blocks"}, {"id": "1d679f0322174b65d032", "user": "en<PERSON><PERSON>", "description": "block wall"}, {"id": "211bd42857358a60a936", "user": "en<PERSON><PERSON>", "description": "block wall: thumbnails only"}, {"id": "7b63fef8ccfbad1f851e", "user": "en<PERSON><PERSON>", "description": "block wall: thumbnails hilbert"}, {"id": "1554783", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "alpha-shapes aka concave hulls in d3"}, {"id": "27969219a945e2bd20dc", "user": "en<PERSON><PERSON>", "description": "d3.geo.weichel"}, {"id": "c1f459768d09e0c334c1", "user": "en<PERSON><PERSON>", "description": "cosine similarity"}, {"id": "c741995e777c0e089418", "user": "en<PERSON><PERSON>", "description": "copy paste aesthetic"}, {"id": "01aa2685f083b6c1b9fb", "user": "curran", "description": "Map & Globe"}, {"id": "eec4a6cda2f573574a11", "user": "mbostock", "description": "Map Pan & Zoom II"}, {"id": "618daab0fc5a3ff494d9", "user": "en<PERSON><PERSON>", "description": "中文: simplified characters"}, {"id": "a9a3b146cfe63ab68a6b", "user": "en<PERSON><PERSON>", "description": "中文: traditional characters"}, {"id": "65ae9c0fc95337107448", "user": "en<PERSON><PERSON>", "description": "matrix: reboot"}, {"id": "bc38c32d24e6fba4db84", "user": "emeeks", "description": "Sketchy Network II"}, {"id": "3887051", "user": "mbostock", "description": "Grouped Bar Chart"}, {"id": "e285b8e4c7b84710e463"}, {"id": "a7c02c493896a33d5f37", "user": "bricedev", "description": "Diverging Stacked Bar Chart (odd)"}, {"id": "f80fdbc2c03dbc1ae4e8", "user": "bricedev", "description": "Diverging Stacked Bar Chart (even)"}, {"id": "1377729", "user": "<PERSON><PERSON>", "description": "Force-based label placement"}, {"id": "5653692", "user": "dj<PERSON><PERSON>", "description": "Collapsible force layout with directed paths and images/text in the nodes. "}, {"id": "4679202", "user": "mbostock", "description": "Stacked-to-Multiples"}, {"id": "4063663", "user": "mbostock", "description": "Scatterplot Matrix Brushing"}, {"id": "1fa21f4ccf084f6369d3", "user": "en<PERSON><PERSON>", "description": "hull padding"}, {"id": "298e07ea67a640b5d9f4", "user": "emeeks", "description": "Orbit Layout 4"}, {"id": "d8b1c5f7bc975de83d99", "user": "emeeks", "description": "Orbital Layout 3"}, {"id": "8e73290130d9e25c6d9c", "user": "emeeks", "description": "Orbital Layout 2"}, {"id": "7d5925cb7d9721c60360", "user": "emeeks", "description": "Quadtree clustering - d3.carto.map"}, {"id": "540b1b884b80b14a44c6", "user": "emeeks", "description": "Improved Circle Pack Clustering - d3.carto"}, {"id": "1a75ec54489368b514f8", "user": "emeeks", "description": "Feature Clustering - d3.carto"}, {"id": "905bb4f399e0855e08fe", "user": "emeeks", "description": "Custom Infobox Formatter - d3.carto.map"}, {"id": "f8c0220c54ec8347ea95", "user": "emeeks", "description": "Changing Markers - d3.carto"}, {"id": "aea3c7fc1acc0eca4d44", "user": "emeeks", "description": "Styling Canvas Elements - d3.carto"}, {"id": "e6f72fbca42e77be0eb3", "user": "emeeks", "description": "<PERSON><PERSON> (Transform) - d3.carto"}, {"id": "0ad500de10f9edfe5d15", "user": "zanarmstrong", "description": "Time Data"}, {"id": "23137b412caf6e80b34a", "user": "zanarmstrong", "description": "Jittery Line w/ option to choose interpolation"}, {"id": "dcfcc3887ff56a9e1928", "user": "<PERSON><PERSON><PERSON>", "description": "SVG default overlay in Leaflet v 1.0"}, {"id": "11114288", "user": "<PERSON><PERSON><PERSON>", "description": "Leaflet Canvas Overlay"}, {"id": "b57f4cc89dacd38fcdcd", "user": "emeeks", "description": "circularbrush.filter"}, {"id": "9265674", "user": "mbostock", "description": "Mexico"}, {"id": "6a83de20f780b42fadb9", "user": "boeric", "description": "D3 Real Time Chart with Multiple Data Streams"}, {"id": "3b57a788a4b96e1af211", "user": "boeric", "description": "D3 Based Real Time Chart"}, {"id": "e16ad218bc241dfd2d6e", "user": "boeric", "description": "D3 Dynamic Array of Tables"}, {"id": "9643738d5f79c7b594d0", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Map zoom + responsive frame"}, {"id": "c89a76096c070a5a7d23", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Map zoom + static frame"}, {"id": "1014829", "user": "mbostock", "description": "External SVG"}, {"id": "91254c459602b31ba549", "user": "syntagmatic", "description": "Curriculum Exploration"}, {"id": "914d024adf10bface11a", "user": "dhoboy", "description": "Gately Tree"}, {"id": "8027637", "user": "mbostock", "description": "Closest Point on Path"}, {"id": "8027835", "user": "mbostock", "description": "Closest Point on Path II"}, {"id": "06af5f439f24bf28aec0", "user": "hwangmoretime", "description": "Point Tracking for Non-Linear Lines: I"}, {"id": "06aa7e108b77745f1f24", "user": "hwangmoretime", "description": "Point Tracking for Non-Linear Lines: II"}, {"id": "da8e72b5e680c827d6dd", "user": "hwangmoretime", "description": "Point Tracking for Non-Linear Lines: III"}, {"id": "98129315d0f7df3b53e3", "user": "1wheel", "description": "Scatter I"}, {"id": "7e473c251e68ce760e2a", "user": "1wheel", "description": "Scatter II"}, {"id": "3dfee2b74943398f0550", "user": "1wheel", "description": "Scatter III"}, {"id": "f0b021b0a56fee82dd97", "user": "nitaku", "description": "Tangled tree II"}, {"id": "5fb51700f991e4101773", "user": "nitaku", "description": "Tangled tree"}, {"id": "8822395"}, {"id": "8746032", "user": "nitaku", "description": "Basic force layout (ID-based, zoomable, fixed random seed)"}, {"id": "833632f23c308ae2d58b", "user": "nitaku", "description": "Isometric treemap (flare)"}, {"id": "a8aa67de3cd9f2e0554e", "user": "nitaku", "description": "Tree colors (sunburst, DBpedia)"}, {"id": "d7cbf8fd96ce42fed36d", "user": "nitaku", "description": "Duck Islands (Pink Floyd)"}, {"id": "3db48ae4c663ca028a6f", "user": "nitaku", "description": "<PERSON> Similarity (<PERSON>)"}, {"id": "8f96bf393b94caff688b", "user": "nitaku", "description": "Isometric projection"}, {"id": "3424eed86f55550622ef", "user": "nitaku", "description": "Labeled treemap (flare)"}, {"id": "6210bd80bdd20181e1f4", "user": "nitaku", "description": "Isometric word cloud"}, {"id": "e03a1a5c1a4a95f06c3b", "user": "nitaku", "description": "Isometric \"treemap\""}, {"id": "79f16392f7e1bed77f07", "user": "nitaku", "description": "Word cloud treemap (flare)"}, {"id": "0c991d6ed0e994e1c7ed", "user": "nitaku", "description": "Node Gosper hex tiling"}, {"id": "ce638f8bd5e70cb809e1", "user": "nitaku", "description": "Node Gosper curve"}, {"id": "1036b853c3f52b8994a4", "user": "nitaku", "description": "Gosper hex tiling irregularity"}, {"id": "cd8e12c7a18495980bd0"}, {"id": "d8c76dfc03589e6dbf1f", "user": "nitaku", "description": "Half matrix for symmetrical relations"}, {"id": "5c5078b3cc3e54e4fec2", "user": "nitaku", "description": "A classic comparison"}, {"id": "4afb54600dc9328d14bd", "user": "nitaku", "description": "Gosper Displacement"}, {"id": "d3fd26207d7468b6c514", "user": "nitaku", "description": "Classic hexagonal binning"}, {"id": "bf5c4ea014d0b91f1f04", "user": "nitaku", "description": "Classic hexagonal binning II"}, {"id": "2d502924bad6152acbe5", "user": "nitaku", "description": "Basic treemap (flare)"}, {"id": "564fcb398fdad08442c1", "user": "nitaku", "description": "Node-link circular layout: Sankey links"}, {"id": "a50ccec1c202731fa8f1", "user": "nitaku", "description": "Node-link polar layout: curved links"}, {"id": "6354551", "user": "nitaku", "description": "Flowlines"}, {"id": "230ff9a14751b8f3092d"}, {"id": "a161bdb59b9dcec9a32e", "user": "nitaku", "description": "Node-link polar layout"}, {"id": "d5fc7956ba6896330ea1", "user": "nitaku", "description": "Node-link polar layout with centrality (eigenvector)"}, {"id": "70da709633904c6de9d9", "user": "nitaku", "description": "Pie vs. pie"}, {"id": "16933e96f51571fbbcd6", "user": "nitaku", "description": "Random(ish) polygon"}, {"id": "8543484"}, {"id": "dcce9b645783d5239a04", "user": "nitaku", "description": "FASS spiral (L-system)"}, {"id": "bffd66a05d5bf04aefa7", "user": "nitaku", "description": "Hilbert spiral (L-system)"}, {"id": "e2af207bc46782aa460c", "user": "nitaku", "description": "Hilbert sequence (L-system)"}, {"id": "8b9e134ca8bae13bb470", "user": "nitaku", "description": "Square spiral (L-system)"}, {"id": "c3d1662948a049fc80dd", "user": "nitaku", "description": "Hex spiral (L-system)"}, {"id": "11337491", "user": "mbostock", "description": "Rainbow Luminance"}, {"id": "5171361"}, {"id": "4360c8ac1dcaee373113", "user": "da<PERSON>h", "description": "2D with stacksgl"}, {"id": "a71ff1c9d6e163e2faea", "user": "da<PERSON>h", "description": "Benchmark"}, {"id": "14e2e4e14898f13e13c7", "user": "da<PERSON>h", "description": "Planetary Grid Browser I"}, {"id": "7b816a50e66bb62208a7", "user": "da<PERSON>h", "description": "Planetary Grid Browser II"}, {"id": "0444bdd92c1f968c75ef", "user": "da<PERSON>h", "description": "Zoom to Group of Countries III"}, {"id": "f5204d3d85bdaa1fd6ea", "user": "da<PERSON>h", "description": "Zoom to Group of Countries II"}, {"id": "baf7dd8d481d83b7f37e", "user": "da<PERSON>h", "description": "Zoom to Group of Countries"}, {"id": "2d12a584a14910032ab8", "user": "da<PERSON>h", "description": "Planetary Grid"}, {"id": "2bab520665d975780931", "user": "da<PERSON>h", "description": "Rotating Planetary Grid"}, {"id": "2fe464efd794bde5ed68", "user": "da<PERSON>h", "description": "Hexakis <PERSON>"}, {"id": "3055104", "user": "mbostock", "description": "Icosahedron"}, {"id": "4b6ab7914183dbf7a16f", "user": "zanarmstrong", "description": "Adaption of Jfire.io animation, for teaching purposes"}, {"id": "3b5f766647e15e23e5b8", "user": "zanarmstrong", "description": "Stripped adaptation of Jfire.io animation, for teaching purposes"}, {"id": "56aa8232fb56247094ae", "user": "zanarmstrong", "description": "Bar Chart: stripped easier"}, {"id": "172a6a53ddbce792b10d", "user": "zanarmstrong", "description": "visualization template based on mouse input"}, {"id": "671c0e7adfb1ce7060b8", "user": "emeeks", "description": "Simple Correlation Matrix"}, {"id": "5cce4b8ca41650f14bb4", "user": "sxywu", "description": "Image processing fun #1"}, {"id": "515f713d41186e18e384", "user": "sxywu", "description": "Image processing fun #2"}, {"id": "680375cb6a66760ea011", "user": "sxywu", "description": "Image processing fun #3"}, {"id": "98f56f3e840143a4eccf", "user": "emeeks", "description": "Treemap"}, {"id": "c5f71b45dbd87402e6f2", "user": "micahstubbs", "description": "County Treemap I"}, {"id": "58f360e13a4397bf3dad", "user": "j<PERSON><PERSON><PERSON>", "description": "d3intro_ex02"}, {"id": "fe38f5960d1276833555", "user": "j<PERSON><PERSON><PERSON>", "description": "d3intro_ex03"}, {"id": "9358409", "user": "sxywu", "description": "Enter-Update-Exit in Force Layout"}, {"id": "4063326", "user": "duopixel", "description": "Animate path in D3"}, {"id": "4063318", "user": "mbostock", "description": "Calendar View"}, {"id": "51ccb8b4f111ef4aec1c", "user": "zanarmstrong", "description": "Stripped adaptation of Jfire.io animation, for teaching purposes: more difficult"}, {"id": "5446416", "user": "mbostock", "description": "Milky Way"}, {"id": "de262983e3ba0c0f89c1", "user": "en<PERSON><PERSON>", "description": "block colors"}, {"id": "0d2e658691a4f93cad92", "user": "nbremer", "description": "A SOM Clustering of block Colors - Including HSL & RGB"}, {"id": "6f26d011aebc4c75272b", "user": "ufenegga", "description": "sin(2t), radial projection"}, {"id": "bb29b599bce91048ec40", "user": "ufenegga", "description": "Radial line generator, interpolation from an empty array"}, {"id": "037488ed37f0e1cbfe32", "user": "emeeks", "description": "Another UI Voronoi"}, {"id": "29cc3cc4078091fd2115", "user": "mbostock", "description": "State Grid"}, {"id": "8ba86c4fded2c67b34c7", "user": "sxywu", "description": "Image processing fun #4"}, {"id": "53c6d59ea70afaf19cf1", "user": "nicks<PERSON>all", "description": "KotoJS BarChart (ES2015)"}, {"id": "58457ea613b84f7544b1", "user": "nicks<PERSON>all", "description": "KotoJS BarChart (ES5)"}, {"id": "5c136de85de1c2abb6fc", "user": "timelyportfolio", "description": "demo programmatic control of a d3 brush"}, {"id": "a8d01671b04b326d36de", "user": "ufenegga", "description": "Satellite Projection Explorer"}, {"id": "07ed66cc6fc394d6ea53", "user": "j<PERSON><PERSON><PERSON>", "description": "Binomial Probability Density"}, {"id": "d7e4f9199c15d71258b5"}, {"id": "667e4df76848e72f250b", "user": "nbremer", "description": "Zoomable Circle Packing with Canvas & D3.js - I"}, {"id": "5a801d9153059b226bbd", "user": "nbremer", "description": "Circle Packing at its most Basic - Canvas only"}, {"id": "db24422abdb20150a9dd", "user": "nbremer", "description": "Zoomable Circle Packing with Canvas & D3.js - II"}, {"id": "4b42493b70dbc9e1ce02", "user": "nbremer", "description": "A SOM Clustering of block Colors - Including RGB"}, {"id": "a35687613ab0f4991f37"}, {"id": "cc818fff7a1049cc0efe", "user": "en<PERSON><PERSON>", "description": "The Migrant Files: Money by Route"}, {"id": "80097d54d4ec4ce83222", "user": "<PERSON><PERSON>lo", "description": "A scatter plot"}, {"id": "89de858deaf6f8fc78ba", "user": "<PERSON><PERSON>lo", "description": "A scatter plot"}, {"id": "15f52d2ec0b035b53c6f", "user": "Jay-Oh-eN", "description": "Civic Impact through Data Visualization: Exercise 1"}, {"id": "34d2ce39d3b8c2f8a577", "user": "malcolm-de<PERSON><PERSON>", "description": "American Lynches"}, {"id": "19a3f0afed794cf720b3"}, {"id": "40f0f319001394b5f8af", "user": "curran", "description": "Lynchings by State Pie Charts"}, {"id": "f1a875885437608d17e8", "user": "curran", "description": "Lynchings by State Pie Charts First Attempt"}, {"id": "e025a50dbaae7186e516", "user": "curran", "description": "Sized Pie Charts"}, {"id": "11b02f8917fac66d6fe5", "user": "curran", "description": "Donut Chart Small Multiples"}, {"id": "40d5e61963abf20da067", "user": "curran", "description": "Polar Area Small Multiples"}, {"id": "be3744cfe1b318bf4035", "user": "curran", "description": "Pie Chart Small Multiples"}, {"id": "3757119", "user": "mbostock", "description": "Equirectangular (Plate Carrée)"}, {"id": "14e29858efdd19253787"}, {"id": "1da93bab4cdc708f41ae", "user": "curran", "description": "Blocks Graph"}, {"id": "d0a42741ce0cf6cdc3ab", "user": "curran", "description": "Sized Donut Charts"}, {"id": "94f1376b946c9d217014", "user": "curran", "description": "Polar Area Diagram"}, {"id": "1c23d9c7070deb6f8488", "user": "curran", "description": "Religions Pie Chart"}, {"id": "950cbe78b4c307fa14a1", "user": "curran", "description": "Using d3-legend"}, {"id": "805413fb3b2efaada1ce", "user": "curran", "description": "Stacked Bars"}, {"id": "b66a37afe6581eb5bc0c", "user": "zanarmstrong", "description": "D3 Bingo! (Zan)"}, {"id": "6daa5503fc60d4a89a11", "user": "zanarmstrong", "description": "D3 Bingo!"}, {"id": "666179ad5587254694bc", "user": "enoex", "description": "Styled Map 1 - Working"}, {"id": "010ffad3577afec10ef6", "user": "enoex", "description": "Styled Map 2 - Broken by default"}, {"id": "92abca0ebac031d77521", "user": "enoex", "description": "Styled Map 1 - Working"}, {"id": "e749224c89f82788cb18", "user": "emeeks", "description": "Sankey Particles III"}, {"id": "9673c96a682fe3948379", "user": "emeeks", "description": "Sankey Particles II"}, {"id": "21f99959d48dd0d0c746", "user": "emeeks", "description": "Sankey Particles"}, {"id": "4403522", "user": "mbostock", "description": "Rotated Axis Labels"}, {"id": "3176159", "user": "phoe<PERSON><PERSON>", "description": "D3 using nest on csv data"}, {"id": "0055d87cfaecc5b06cd2", "user": "hsu<PERSON>g", "description": "d3 first transition"}, {"id": "835c17a94abade5c2ca1", "user": "hsu<PERSON>g", "description": "d3 first svg"}, {"id": "4a7f517bec51adc6e7bc", "user": "hsu<PERSON>g", "description": "d3 first bar plot"}, {"id": "be4a45ec74357e7d9b10", "user": "curran", "description": "Blocks Graph with Links"}, {"id": "ebec037138b5866ff0d3", "user": "syntagmatic", "description": "Breathing Color Mesh"}, {"id": "c21840890a4790632124", "user": "en<PERSON><PERSON>", "description": "Connected Particles: Lasers"}, {"id": "be2abfb3155a38be4de4", "user": "eesur", "description": "d3 | Force layout with images"}, {"id": "3cda05ca68cba260cb81", "user": "micahstubbs", "description": "programmatic control of a d3 brush - specify zoom"}, {"id": "7b226e90c7338c69606b", "user": "en<PERSON><PERSON>", "description": "matrix: rotations"}, {"id": "d5252d37917ab6eab032", "user": "curran", "description": "Focus + Context Area Charts"}, {"id": "92248ec5049ba86bf125", "user": "geraldarthur", "description": "State Grid <PERSON>"}, {"id": "78b10a3836254f8b6d8e", "user": "geraldarthur", "description": "JSON to <PERSON><PERSON>"}, {"id": "2ceaea7f0a4e37ca5a5d", "user": "sxywu", "description": "Image processing fun #5"}, {"id": "2aead353c3446ee9acca", "user": "sxywu", "description": "Image processing fun #6"}, {"id": "f2f6883ac7c965d09b90", "user": "emeeks", "description": "Networks - Graphs 7"}, {"id": "daf6bc9db8b0a28e3973", "user": "curran", "description": "Blocks Graph Edges Only"}, {"id": "db1e524cae5e4344b2e6", "user": "curran", "description": "Dynamic Size"}, {"id": "6c0ce7a12c7d5497350d", "user": "curran", "description": "Multi-Line Voronoi 2015"}, {"id": "a61e208c37ea177cf83a", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Civic Impact through Data Visualization: Exercise 1"}, {"id": "b6e4ec3859b2f732dfc0", "user": "nbremer", "description": "Circle Packing at its most Basic - Canvas & D3.js "}, {"id": "8a7f9bed9715c0eb678a", "user": "maartenzam", "description": "Responsive CartoDB choropleth linked to strip plot"}, {"id": "d1c32b2e77120f560061", "user": "nitaku", "description": "Arc Diagram II"}, {"id": "00e6248efe5605c1fa5c", "user": "j<PERSON><PERSON><PERSON>", "description": "Conversion rates"}, {"id": "66eee134d6fd3babb716"}, {"id": "018e79e4b62e86dfd988", "user": "alto<PERSON>ulus", "description": "<PERSON> globe"}, {"id": "2920551", "user": "<PERSON><PERSON><PERSON>", "description": "force-multi-foci with convex hulls"}, {"id": "00336c9eb59ee956426d", "user": "newsummit", "description": "force-multi-foci with convex hulls"}, {"id": "ea64575d70ecf3354874", "user": "xaranke", "description": "income_categories"}, {"id": "b5df5023965f65719d7a"}, {"id": "93297e55e7535dd833a1", "user": "xaranke", "description": "538_RTs"}, {"id": "23b5bc37d39c9f9dd5e7", "user": "xaranke", "description": "538_links"}, {"id": "8be32e6f1f32920ba841", "user": "en<PERSON><PERSON>", "description": "aframe + d3 test"}, {"id": "a3ee04578325668bd3f8", "user": "EE2dev", "description": "item explorer - demo"}, {"id": "5a4ab3ca8b3b7b57d234", "user": "EE2dev", "description": "item explorer - data format - example 1"}, {"id": "0f7abbfc6ab01513d89a", "user": "EE2dev", "description": "item explorer - data format - example 2"}, {"id": "d9ad0499316f09c598a3", "user": "EE2dev", "description": "item explorer - data format - example 3"}, {"id": "2a7a31815153d26b39f6", "user": "EE2dev", "description": "item explorer - data format - example 4"}, {"id": "69c42b901d0ed52d480a", "user": "EE2dev", "description": "item explorer - data format - example 5"}, {"id": "3bb8a779948659a5b101", "user": "EE2dev", "description": "item explorer - data source - example 1"}, {"id": "07bbe91f368e5ce0b180", "user": "EE2dev", "description": "item explorer - data source - example 2"}, {"id": "a5e1b098533228613f28", "user": "EE2dev", "description": "item explorer - data source - example 3"}, {"id": "de4a9e0010795ace76b8", "user": "EE2dev", "description": "item explorer - data source - example 4"}, {"id": "131ad62a0ef5a8e6968b", "user": "EE2dev", "description": "item explorer - visualization option 1"}, {"id": "c8ef9d67acc044b368ef", "user": "emeeks", "description": "d3.layout.orbit modes"}, {"id": "bdc0474429e6567bc320", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "D3 Scatterplot of 2014 NCAA Passing Statistics"}, {"id": "3e46da46186e1dc1a02e", "user": "malcolm-de<PERSON><PERSON>", "description": "Lobbying Spending 1998-2015"}, {"id": "f8e9ae0a4c101d978f84", "user": "sxywu", "description": "Art in Pi, Experiment #1"}, {"id": "9fc66f76d0217475f2e4", "user": "sxywu", "description": "Art in Pi, Experiment #2"}, {"id": "61a4bd0cfc373cf08884", "user": "sxywu", "description": "The Force with React + D3, Approach #1"}, {"id": "1db896c1a38d89ae71b4", "user": "sxywu", "description": "The Force with React + D3, Approach #2"}, {"id": "fcef0e6dac231ef2e54b", "user": "sxywu", "description": "The Force with React + D3, Approach #3"}, {"id": "c31c5eb3bf1c5a72bde9", "user": "biovisualize", "description": "Histogram Equalization"}, {"id": "3878819", "user": "phoe<PERSON><PERSON>", "description": "Ireland Sporthorse Foal Registrations - in progress"}, {"id": "1238376", "user": "biovisualize", "description": "External SVG"}, {"id": "99df29f258860e53c5e2", "user": "micahstubbs", "description": "Sporthorse Foal Registrations II"}, {"id": "18cb7a77b2d9de597b86", "user": "en<PERSON><PERSON>", "description": "dots on a map: setup"}, {"id": "a4679ee453aa9357977c", "user": "eesur", "description": "d3 | Reusable slopegraph"}, {"id": "ee8d0ab88229d0e07f7f", "user": "eesur", "description": "d3 | reusable slopegraph v2"}, {"id": "5fbda7f410d31da35e42", "user": "eesur", "description": "d3 | reusable heatmap calendar"}, {"id": "b100d04bda697f95f246", "user": "veltman", "description": "Gif rendering"}, {"id": "03edaa335f93b5a9ee57", "user": "veltman", "description": "Gif rendering - globe"}, {"id": "3231298", "user": "mbostock", "description": "Collision Detection"}, {"id": "b84846a0511acfeaf925", "user": "Jay-Oh-eN", "description": "A Rectangle of Circles"}, {"id": "3d66eaf6306fe3b279e6", "user": "geraldarthur", "description": "Random line drawing"}, {"id": "e52b02edb554747370c5", "user": "geraldarthur", "description": "Random line drawing 2"}, {"id": "6bd2b990bea368bd6358", "user": "geraldarthur", "description": "Random line drawing 3"}, {"id": "5f168b5c884b1f9c36a5", "user": "KatiRG", "description": "d3js clickable stacked bar chart"}, {"id": "8325617"}, {"id": "3601e88d5317ab2be0e8", "user": "<PERSON><PERSON><PERSON>", "description": "Bar Chart"}, {"id": "487ef139e7025406c4c1", "user": "benduffin", "description": "Connected Particles III"}, {"id": "294eae4fec5e31d0390d", "user": "benduffin", "description": "Connected Particles III"}, {"id": "4506b5bf37fab13586f7", "user": "benduffin", "description": "Focus+Context via Brushing"}, {"id": "ca5eb04b731b7676104a", "user": "benduffin", "description": "Population Pyramid"}, {"id": "e8ea925f7c61b353f694", "user": "benduffin", "description": "Population Pyramid"}, {"id": "47ec2268b4e1edfda9d7", "user": "benduffin", "description": "Population Pyramid"}, {"id": "a3b5571be92a0513b2f9", "user": "benduffin", "description": "Population Pyramid"}, {"id": "fa6292523b1680ecbb15", "user": "khawkins98", "description": "Adaptive-content-model v0.2"}, {"id": "308aa0fb4aeb2771b6cc", "user": "khawkins98", "description": "Adaptive-content-model V0.1"}, {"id": "fe31fe34099bbb41e0a7", "user": "ch<PERSON><PERSON><PERSON>", "description": "Bar Chart"}, {"id": "6b62ef1ddf6523b1f0fd", "user": "mansfimx", "description": "Bar Chart"}, {"id": "e266096cad90266aa85e", "user": "mansfimx", "description": "Bar Chart"}, {"id": "df9af361ca4b8f34aec9", "user": "lambrex", "description": "Bar Chart"}, {"id": "f641200424b306c4b724", "user": "lambrex", "description": "Bar Chart"}, {"id": "8d7bad2d954ee09ea347", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "d1f3811a4964d484017c", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "bahamas flag"}, {"id": "262b5344222f227dd9d3", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "f633095702b6f478cc12", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "f2e095a4f77bc51b6010", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "9e3f368898e5fca662f2", "user": "g<PERSON><PERSON><PERSON>y", "description": "Bar Chart"}, {"id": "2ed04b7552f1ef9f8e0c", "user": "g<PERSON><PERSON><PERSON>y", "description": ""}, {"id": "aa2b8867d2f13ead838b", "user": "mcgovemc", "description": "Bar Chart"}, {"id": "c5d9a583437b19493ba3", "user": "zaccagbj", "description": "Bar Chart"}, {"id": "a7ef0b8473773b3ccda1", "user": "zaccagbj", "description": "Bar Chart"}, {"id": "a2db15a154b360c591c7", "user": "zaccagbj", "description": "Bar Chart"}, {"id": "75878caf6912fe6bfe85", "user": "zaccagbj", "description": "Bar Chart"}, {"id": "4082de18058c055efe84", "user": "Smith5mr", "description": "Bar Chart"}, {"id": "1733c62207e0bf614f06", "user": "Smith5mr", "description": "Bar Chart"}, {"id": "f37b07b92633781a46f7", "user": "mbostock", "description": "Arc Padding II"}, {"id": "9b16c3815f461d8c0817", "user": "Smith5mr", "description": "Arc Padding II"}, {"id": "c32d1c61d0d09dcda690", "user": "Smith5mr", "description": "fresh block"}, {"id": "38cd48fe454b017122aa", "user": "Smith5mr", "description": "fresh block"}, {"id": "3ef8e84b6a3482c9efa9", "user": "Smith5mr", "description": "fresh block"}, {"id": "33a6d86c5036e61549b5", "user": "AdamBlaine1", "description": "Bar Chart"}, {"id": "eca6a6ab132db0eb3278", "user": "AdamBlaine1", "description": "Bar Chart"}, {"id": "c23d14a90a18c0ba9498", "user": "AdamBlaine1", "description": "Bar Chart"}, {"id": "5cebc9137ffcd6271071"}, {"id": "59ad8df8112637667982", "user": "thoma2nm", "description": "Bar Chart"}, {"id": "0a4fc38df5249e1614a1", "user": "dun<PERSON><PERSON>", "description": "Bar Chart"}, {"id": "8ed968fc7b4ccb04630c", "user": "diepvf", "description": "Bar Chart"}, {"id": "5155181", "user": "d3noob", "description": "Directional Force Layout Diagram with varying link opacity"}, {"id": "18c2975af798328a528f", "user": "BDAguila", "description": "Bar Chart"}, {"id": "01deb69af82797bd8738", "user": "BDAguila", "description": "Bar Chart"}, {"id": "db57b9e8546b4b83a405", "user": "micahstubbs", "description": "Correlation Matrix VI"}, {"id": "66474fc2225415d1ebd3", "user": "micahstubbs", "description": "Correlation Matrix IV"}, {"id": "ea0a8c361ba849711628", "user": "micahstubbs", "description": "Correlation Matrix V"}, {"id": "4f068e1a8833a7d2f2fb", "user": "micahstubbs", "description": "tiny hexbin plot"}, {"id": "3f6e737fd8e50f6cbda2", "user": "micahstubbs", "description": "Correlation Matrix III"}, {"id": "42b1361b49ed2cc4e0ae", "user": "micahstubbs", "description": "Correlation Matrix II"}, {"id": "3ef5a223bdb760a228b4", "user": "micahstubbs", "description": "Correlation Matrix I"}, {"id": "26c887df79dc869059ba", "user": "micahstubbs", "description": "Blocks Graph II Voronoi Sun"}, {"id": "8a173cfcb9171627c7f1", "user": "micahstubbs", "description": "Blocks Graph I Readme Mentions"}, {"id": "6420534", "user": "couchand", "description": "Better force layout node selection"}, {"id": "0cfe97cda6ed02d49591", "user": "stelt", "description": "morph a composite path"}, {"id": "b1cd9a8ae8f2f9d67ea9", "user": "stelt", "description": "morph a composite path"}, {"id": "4b86c29e1669b75653de", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Reusable Area Chart: Ladybug group interactions"}, {"id": "4969184", "user": "tmcw", "description": "Jenks Natural Breaks with simple-statistics and d3"}, {"id": "60a1ba782ce09cf2091e", "user": "curran", "description": "Useless Vis"}, {"id": "6567fb027971588bc779", "user": "curran", "description": "Invitations Links Map"}, {"id": "55d327542393530662c3", "user": "curran", "description": "Country Centroids on a Map"}, {"id": "c4a8380f6b3a5ae6668d", "user": "b<PERSON>hon", "description": "Shellsort"}, {"id": "1582075", "user": "mbostock", "description": "Quicksort"}, {"id": "4360892", "user": "mbostock", "description": "U.S. Airports Voronoi"}, {"id": "a0d2efed63aeefce5cb0", "user": "SpaceActuary", "description": "Distributions"}, {"id": "41b50ea8bb206e44fec2", "user": "SpaceActuary", "description": "Non-negative Distributions"}, {"id": "a003bf90d75a54c7ccb6", "user": "bletcher", "description": "d3FishMove"}, {"id": "b3f5eb034f440cfb6809", "user": "bletcher", "description": "d3FishMove"}, {"id": "4d62de0846a2554b113b", "user": "boeric", "description": "Driver License Suspensions II"}, {"id": "f6ddea14600dc5093506", "user": "boeric", "description": "Mapbox GL Synced Dual Maps"}, {"id": "2dca5c7c84eb5d3c72a8", "user": "boeric", "description": "Driver License Suspensions"}, {"id": "8203750", "user": "christopher<PERSON><PERSON>", "description": "Random Walk USA"}, {"id": "0d95074b6d83a77dc3ad", "user": "bricedev", "description": "Grouped bar chart"}, {"id": "0b4b038fd4dd3a046168", "user": "monfera", "description": "<PERSON>'s Student Dashboard with d3.js"}, {"id": "ddc9632a718e306b6e71"}, {"id": "3f0e150ff6a86838f810"}, {"id": "f56bb7f0e3c73358b752", "user": "thomasvbrown", "description": "<PERSON>'s Student Dashboard with d3.js"}, {"id": "f3708ba3311007a9b4a6", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Line Voronoi"}, {"id": "8ae3fd45fca0f18372d4", "user": "nitaku", "description": "Half matrix II"}, {"id": "162e96df868aa79167ac"}, {"id": "6783e81b15f3205e9ff2", "user": "lindep", "description": "arc with datum"}, {"id": "542e6e08a2a9f7c2ebac", "user": "Jay-Oh-eN", "description": "Udacity Data Visualization: World Cup Attendance (scatter)"}, {"id": "2f2183747543c804b8ca", "user": "Jay-Oh-eN", "description": "Udacity Data Visualization: World Cup Attendance (line)"}, {"id": "9d62b7a2c4335a0fd1e6", "user": "Jay-Oh-eN", "description": "Udacity Data Visualization: World Cup Attendance (D3.js)"}, {"id": "f25a996484a0588cb8c2", "user": "Jay-Oh-eN", "description": "Udacity Data Visualization: World Cup Attendance Annotated"}, {"id": "3969722", "user": "mbostock", "description": "Gradient Encoding"}, {"id": "9154d57067918cdb4cf5", "user": "Jay-Oh-eN", "description": "1-<PERSON> (points)"}, {"id": "1440766", "user": "mbostock", "description": "selection.order"}, {"id": "8ad986db8b3f5cc82e69"}, {"id": "744228c927ee95871559", "user": "<PERSON><PERSON><PERSON>", "description": "trace struct flattener"}, {"id": "810749b7dbcf0eca1e4e", "user": "j<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> (Hire2)"}, {"id": "341ed17c343e2520bde6", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "04a00e72a27edafa5ab8", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "3c280b21451e269ba759", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "06972c1773b30ccdb2d8", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "4c45db68b16ff90254de", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "77428cb3e0875e9683d9", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "4dbe405dc614fd08ad77", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "b3dbd57c3ec465a3d925", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "5df8dd084a93ad878cfc", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "bb56b979c5a6117b334b", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints"}, {"id": "b887532acfb8b001940d", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Glucose over time + food hints mod"}, {"id": "f73384946e17e4b1b46a", "user": "ChristosT", "description": " CS 725 Information Visualization - VI3"}, {"id": "52239ed074878b9a60ca", "user": "ChristosT", "description": " CS 725 Information Visualization - VI2"}, {"id": "2f93011f91b398b936be", "user": "ChristosT", "description": " CS 725 Information Visualization - VI1"}, {"id": "39d0395522fb4882de38"}, {"id": "9499896"}, {"id": "2481e1f3b1d92002317e", "user": "Fil", "description": "UN Votes - Session 70"}, {"id": "a9e99b949d0d9b1e6b85"}, {"id": "c5e83b964f5cf7c5edda", "user": "alandunning", "description": "fresh block new"}, {"id": "c0da0fb4a6c9e4c393de", "user": "alandunning", "description": "fresh block"}, {"id": "4df29e2f8c6e20ed2baf", "user": "curran", "description": "Religions Bar Chart"}, {"id": "b1014a71757ce72444e1", "user": "curran", "description": "Multi-Line Chart of Largest Countries"}, {"id": "34784700eb8fb594f17f", "user": "curran", "description": "World Population Area Chart"}, {"id": "9eea6a43904b1a158e92", "user": "curran", "description": "Stacked Area Chart"}, {"id": "09bc0b1f7512757ad152", "user": "curran", "description": "Stacked Area Chart"}, {"id": "38845ef1d0c11b40dfd8", "user": "curran", "description": "Sortable Slices"}, {"id": "8838736cfa6f3f960e2c", "user": "curran", "description": "Crime by Race Dataset"}, {"id": "851f506aac452bf962d3", "user": "curran", "description": "Crime by Race Dataset (Normalized)"}, {"id": "430efaf8f592d018bd3e", "user": "curran", "description": "<PERSON> Gun Violence Dataset"}, {"id": "a08a1080b88344b0c8a7", "user": "curran", "description": "The Iris Dataset"}, {"id": "014b461f460cf8fa3b77", "user": "curran", "description": "Iris Dataset Scatter Plot"}, {"id": "9938078a93a4ba380a0e", "user": "curran", "description": "Interactive Scatter Plot"}, {"id": "46050d18d5ec1ab401fa", "user": "curran", "description": "Chiasm-Charts v0.1.0"}, {"id": "3883195", "user": "mbostock", "description": "Area Chart"}, {"id": "e05c76ab9450cbc214ac", "user": "curran", "description": "World Population Line Chart"}, {"id": "1297670a35e6e0841636", "user": "Sokrates86", "description": "scatterplot-lines-SAINTS"}, {"id": "b50089030f88e5f58c07", "user": "lulu567", "description": "Milky Way"}, {"id": "df90b219b9443e457e0d", "user": "jala<PERSON>", "description": "<PERSON>' tactics board"}, {"id": "bc36d0af347f32152e07"}, {"id": "7d15b6ea45e5b37c20c4", "user": "jala<PERSON>", "description": "Tactics board, freehand draw"}, {"id": "522d3842f27e2581f0d9", "user": "Jay-Oh-eN", "description": "Foundations of D3: Data Binding Scope"}, {"id": "4d17fa15a7a5084e217992f985fba484", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "D3 tutorial V: Adding tooltips"}, {"id": "5181105", "user": "rveciana", "description": "D3 tutorial V: Adding tooltips"}, {"id": "7b66ad387b75d6077348", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "European FABs"}, {"id": "d517a035d0abb5cd0919", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "European FIRs"}, {"id": "9ea56e041f6847dbe944", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "D3.js <PERSON> "}, {"id": "2986e55a01c7008d4b5b", "user": "rveciana", "description": "D3 Trail Layout animated map"}, {"id": "10743959", "user": "rveciana", "description": "Hayan typhoon gradient colored track II"}, {"id": "6193058", "user": "rveciana", "description": "Flag map with D3js - SVG"}, {"id": "5689783", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "C<PERSON>ll-<PERSON><PERSON> map projection"}, {"id": "86e51152d02e6280e9f3"}, {"id": "94c3203a8f86d34a6af7", "user": "sheffieldhale", "description": "Soda Taxes Bubble Up (White)"}, {"id": "50667ddd436c39f66b84", "user": "bletcher", "description": "transition test"}, {"id": "1e7a161a8bf6f545b8f8", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "transition test"}, {"id": "1fd196cd99f8d58a56d3", "user": "en<PERSON><PERSON>", "description": "aframe + d3 test: text labels"}, {"id": "dc1ce756527c072885dc", "user": "en<PERSON><PERSON>", "description": "dots on a map: setup-gl"}, {"id": "b95748220bd4752fb33a", "user": "en<PERSON><PERSON>", "description": "dots on a map: lasso"}, {"id": "3887118,"}, {"id": "8287bd24c157edb5bb2b", "user": "ndarville", "description": "World Map"}, {"id": "707cc158d2520c4e4602", "user": "ndarville", "description": "World Map (Multicolour)"}, {"id": "acb1dde273bff654262c", "user": "ndarville", "description": "Visualisering af statistisk usikkerhed i Megafon-måling 27/11-14"}, {"id": "e09fcd2621b42d07a9fe", "user": "ndarville", "description": "Meningsmålingstendenser op til 27/11-14"}, {"id": "66fa642ce3eb6a0de885", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "0faeb2237488f7aa526b", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "6185069", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "D3 Scatterplot Example"}, {"id": "6e602654b41a48709612", "user": "ch<PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "6be047ed3c064b505c8f", "user": "AdamBlaine1", "description": "Multi-Series Line Chart"}, {"id": "d6c5a1923f53a60876ad", "user": "AdamBlaine1", "description": "Multi-Series Line Chart"}, {"id": "533e9ec746874e1a36cd", "user": "zaccagbj", "description": "Multi-Series Line Chart"}, {"id": "6e75ad8f8664c245d0a0", "user": "dun<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "2b1e4cc603310e9abc86", "user": "lambrex", "description": "fresh block"}, {"id": "17567df60d22eed325b4", "user": "lambrex", "description": "fresh block"}, {"id": "e758727fb4e111fdf045", "user": "lambrex", "description": "fresh block"}, {"id": "b3f97b9b6a27a2878f5b", "user": "lambrex", "description": "fresh block"}, {"id": "51cd71f0b6854e57e6a1", "user": "lambrex", "description": "fresh block"}, {"id": "c55265d611e30e7e95f0", "user": "lambrex", "description": "fresh block"}, {"id": "0514ad3e56d05722f997", "user": "lambrex", "description": "fresh block"}, {"id": "09f8c8eb06feff3e6dea", "user": "lambrex", "description": "fresh block"}, {"id": "264ed380895fea0d3844", "user": "mansfimx", "description": "Multi-Series Line Chart"}, {"id": "1e6da47724c39156adb3", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "timeline - correlation"}, {"id": "0a8fe1caa2ac025c8e86", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "timeline - trend & seasonality"}, {"id": "fa5deb2022e829449688"}, {"id": "189bbf2dad71071b9969"}, {"id": "2583342b5b484e70899b"}, {"id": "9da0a8c4e7391243e941", "user": "tacastillo", "description": "Multi-Series Line Chart"}, {"id": "5cf2da9490fac8f59799", "user": "rmtrailor", "description": "Multi-Series Line Chart"}, {"id": "ca1f73e81c2440b46eed", "user": "rmtrailor", "description": "Multi-Series Line Chart"}, {"id": "a416105e509593928ec5", "user": "vwrideout", "description": "Multi-Series Line Chart"}, {"id": "a7b5b1f23019e065d8aa", "user": "<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "f80a387b1ce45adfbe08", "user": "sliu72", "description": "Multi-Series Line Chart"}, {"id": "9d9ccdf86fb59ab6c6e0", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "29d80cf22eda5b55e2c1", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "7c289a9dbf38ceb665d8", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a45d78cc14704ced3f88", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "deb677307bfb906b38e6", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a9711c2f39239465f30e", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "de369cc33c7c02015263", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "6e75e8d20da020b9de19", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "1727593e326e81b0951f", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "4db859d1e3fd8e8b8642", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a2e10229636d23f13dbb", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "d7aded22d8ab1a5ff063", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "527111d4bf58cfbaa8f7", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "4119e1e519d687eb7ffc", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "aaa180c9379a902aba3e", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "5359c5b7020527a68e60", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "aa154290d2f39a1ab54b", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "ab47787d16eb43d8af34", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "185510fc80ac8a874cd1", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "7ae92335446d3ad639d0", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "5740eb91f7f2f4bef0ae", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "7d8f7ff7f31b2789b1f9", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "87df80ba235efb82e9de", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "7b28e2aca81bd450e15e", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "6d6fa3b5fbd160e0afb4", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "640803240b9db8f610af", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "3da6b1e5522c57dd8106", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "c7b3934926cee4f7b39e", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "80509e9d1cdc66568152", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a592a0a644f806ee56a7", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "ab22a21c4fb7d977ea7b", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "b74bb2749f8a96c4c241", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "b7f4298f7d2892ba4b1b", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "d865b3a6873f39fe0d75", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "65ea67b5e062b5ba666b", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "921af093736051ab15a8", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "d63a29287faa83227f65", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "ac5b6c28390fb4af7a04", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "9f2c2f3e48fc6b3e51ff", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "8a6e0ca67f054dd9ef29", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a2c2ab6da0a0aa3d8fd1", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "1f02ba8c57e84bbf0d08", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "d3a03c0a140b6d4b4e2c", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "7b912a3693e7dee2633e", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "0dc207c08decd57319f3", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "3ffc84f3def0025a4243", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "333c0477264183d25c12", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a69684b04fde3f873ee8", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "10f43d81d7fa62872872", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "f00d5c23c2d79f37653e", "user": "feng<PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "2fbe51dcbc0d27cf897b", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "ea72de53eb3daad14f73", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "f3e6415f79b078b75fe4", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "d3b1e127741d5be5bbc3", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "fea2240ea499f36b7ec4", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "d721b55071e31f8da8c3", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "4df06aacdf53e24c90b7", "user": "kiansheik", "description": "Multi-Series Line Chart"}, {"id": "00e295827c51ffa80104"}, {"id": "9b23673c0786fa5b1a75", "user": "N0taN3rd", "description": "fresh block"}, {"id": "e503f9cd7655acf5d9a7", "user": "N0taN3rd", "description": "fresh block"}, {"id": "cf3031700f42670dccf6", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "97a40f1c0684bc5bd0d5", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "81d0c04cbd78c95cc674", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "a807f2ba95e19db2a0f5", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "70b669815dbad375ea75", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "28c466de83d5100f009e", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Series Line Chart"}, {"id": "cf29a41485972f2a15cf", "user": "Cdcole", "description": "Multi-Series Line Chart"}, {"id": "a4658f02960b71612a57", "user": "Cdcole", "description": "Multi-Series Line Chart"}, {"id": "4e9a0662dc81944593f9", "user": "Cdcole", "description": "Multi-Series Line Chart"}, {"id": "c81883c957e8537be10f", "user": "Cdcole", "description": "Multi-Series Line Chart"}, {"id": "1aef402d919da5d776b5", "user": "djokicx", "description": "Multi-Series Line Chart"}, {"id": "fdb820d9d828400cb227", "user": "djokicx", "description": "Multi-Series Line Chart"}, {"id": "9ccba191aa29181821d2", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "ed2a8aff3d202713525f", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "4689130", "user": "vjpgo", "description": "d3.js major minor tick style 3"}, {"id": "4349486", "user": "mbostock", "description": "ggplot2-Style Axis"}, {"id": "6738109", "user": "mbostock", "description": "Superscript Format"}, {"id": "0da60479257cc3d90a62", "user": "lambrex", "description": "ggplot2-Style Axis"}, {"id": "965cf81685bc8a9021a8", "user": "jfost00", "description": "Bar Chart"}, {"id": "7ac51fe69e41fc6c606a", "user": "jrodgz", "description": "JDR VI4 for CS725@ODU "}, {"id": "3887235."}, {"id": "60e84ea5cdffa49356e4", "user": "ashtonburg", "description": "d3.js major minor tick style 3"}, {"id": "384dd445a6a7ae9f6086", "user": "ashtonburg", "description": "d3.js major minor tick style 3"}, {"id": "4384949715de645a77ef", "user": "ashtonburg", "description": "d3.js major minor tick style 3"}, {"id": "1f1ab0ec1cc8cee5970f", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "23e98ddcc9ff35b6517f", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "7b807fa90c485937b2ea", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "bf6871220d8db6efd10f", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "b378b3a76c1e12038c43", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "74cc63dc0a9ae18297de", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "96a3758777f9bb5e91c6", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "981721947501c7fc2fbc", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "5c4e011cc543373478e8", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "b95717283160c8c293e0", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "cdc8b048f1e7a56fae0e", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "da81217dee2d551d1411", "user": "ashtonburg", "description": "Superscript Format"}, {"id": "5ada6a7d00115f0e2f1e", "user": "emeeks", "description": "Ch. 5, Fig. 6 - D3.js in Action"}, {"id": "019307194abe050e1117", "user": "cool-Blue", "description": "d3 Force directed graph with node size transitions"}, {"id": "f810911f5f84b94f2e3e", "user": "cool-Blue", "description": "d3 Force directed graph with node size transitions - CSS transitions "}, {"id": "a4530669c16faced0d57", "user": "cool-Blue", "description": "Pixi.js WebGL using spritesheet"}, {"id": "ea9be02dff5b6c3a18e2", "user": "cool-Blue", "description": "Canvas renderer with texture atlas (spritesheet)"}, {"id": "febe139365a1819a6953", "user": "cool-Blue", "description": "WebGL renderer with multiple sprites"}, {"id": "9704fe9249dfbc5c7da9", "user": "cool-Blue", "description": "Canvas renderer with multiple sprites"}, {"id": "23ca2f006b720c784fc2", "user": "cool-Blue", "description": "SVG speed test"}, {"id": "5b3161f6be8bd2baac52", "user": "cool-Blue", "description": "d3 Force directed graph with node size transitions - velocity.js transitions"}, {"id": "4562ac558aa8fb77269c", "user": "en<PERSON><PERSON>", "description": "interviewing.io: mean lines"}, {"id": "78e3a730e77c677e168c", "user": "en<PERSON><PERSON>", "description": "interviewing.io: mean vs stddev"}, {"id": "4ee4c58c9dfc2a41ed26", "user": "micahstubbs", "description": "hexbin with no axes"}, {"id": "8c462789ffbb04351a11", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "timeline - seasonality detection (II)"}, {"id": "89e1e69c888e8241ed92", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "timeline - seasonality detection (I)"}, {"id": "5118ba2eb78edfcf645e", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "timeline - seasonality detection (III)"}, {"id": "77ddbd1e08c11e18a0f7", "user": "ChristosT", "description": " CS 725 Information Visualization - VI4"}, {"id": "08fc85ae18559fe95ad3", "user": "georules", "description": "oh yes spiral"}, {"id": "3ad7359dc3d6f0d25734", "user": "rpgove", "description": "Heatmap of Force-Directed Graph Node Positions"}, {"id": "a82271eb40bed573f921", "user": "nitaku", "description": "Polar floating bar chart"}, {"id": "4c7a8a89a25bd86a61a8", "user": "<PERSON><PERSON><PERSON>", "description": "Radar Plot Annual Temperature"}, {"id": "6a922b0fb6abbb815158", "user": "veltman", "description": "Gif rendering - dynamic SVG"}, {"id": "d1b8c76c16ceab5d3b45", "user": "veltman", "description": "Transition hacking"}, {"id": "23460413ea085c024bf8", "user": "veltman", "description": "Transition hacking with chains"}, {"id": "1071413ad6b5b542a1a3", "user": "veltman", "description": "Gif rendering - SVG to GIF"}, {"id": "38149d05ea247cbcebb1", "user": "veltman", "description": "Hexbin resizing test"}, {"id": "5d4eac580beb1fbb0031", "user": "chillee151", "description": "fresh block"}, {"id": "7fdc5a209906a8e6789b", "user": "chillee151", "description": "fresh block"}, {"id": "936ef76e148c655b1058", "user": "chillee151", "description": "fresh block"}, {"id": "e9b3bfc6f43b4bd237e2", "user": "cmdoptesc", "description": "D3 Arc Progress Gauge / Meter (for d3-js group)"}, {"id": "3750941", "user": "mbostock", "description": "Progress Events"}, {"id": "10699411", "user": "<PERSON><PERSON><PERSON>", "description": "SVG foreignObject tooltips in D3"}, {"id": "d15be2c0ace80aef3503", "user": "<PERSON><PERSON><PERSON>", "description": "playing with SVG line markers in D3"}, {"id": "a639c8d8fb6ad384a8e3", "user": "<PERSON><PERSON><PERSON>", "description": "second verse...not same as the first"}, {"id": "c22e71c05e3c3bfadcae", "user": "<PERSON><PERSON><PERSON>", "description": "vector tiles plus plotted coordinates"}, {"id": "7722907", "user": "radiocontrolled", "description": "Refugee claimants by Canadian region, 2012"}, {"id": "913d0c1716e92e9fe29c", "user": "rveciana", "description": "JSL 2015: Map<PERSON> sencillo usando <PERSON>"}, {"id": "2787020caf01beea88cd", "user": "rveciana", "description": "JSL 2015: Map<PERSON> sencillo"}, {"id": "4affa5bdb5e8c87ea5e5", "user": "rveciana", "description": "JSL 2015: Pie layout animado"}, {"id": "20f120d7ab0580f9a8c5", "user": "rveciana", "description": "JSL 2015: <PERSON>out"}, {"id": "cbd4c10fed8942c87332", "user": "rveciana", "description": "JSL 2015: Gráfico con transiciones"}, {"id": "c81710601e2232aaedc0", "user": "rveciana", "description": "JSL 2015: <PERSON><PERSON><PERSON><PERSON><PERSON>illo"}, {"id": "12c2e8a92d521144e146", "user": "thedod", "description": "Twister daily traffic (D3 visualization of TwisterIO stats)"}, {"id": "8603837", "user": "d3noob", "description": "Multiple line graphs with labels"}, {"id": "b8a8fcb885b6840bfde5", "user": "scresawn", "description": "genome browser"}, {"id": "01215dfa696fb3ec66f6", "user": "AdamBlaine1", "description": "genome browser"}, {"id": "e263c1e6b71ff8fe0976", "user": "AdamBlaine1", "description": "genome browser"}, {"id": "d5ce25776cb31dde851b", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "ccd1b2d7ce54e59e455e", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "a679815370ad5e8ce609", "user": "ChristosT", "description": " CS 725 Information Visualization - VI5"}, {"id": "a0c5ef180272fac3aea6", "user": "bricedev", "description": "Text tween"}, {"id": "5e498878a37abb60b902", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Text tween"}, {"id": "4e199eb9812a066e7f2d", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Portugal banking"}, {"id": "a104fffc6e3c2abc1c8d", "user": "GeorgeMcIntire", "description": "Portugal banking"}, {"id": "96447623ef4d342ee09b", "user": "NPashaP", "description": "DashBoard"}, {"id": "9b193942e2fc4ac88c6e", "user": "curran", "description": "Unemployment in San Mateo County"}, {"id": "8d667f0989d66f03294b"}, {"id": "002ba028b31588848738", "user": "RuiChang123", "description": "Portugal banking age"}, {"id": "8709363", "user": "llb4ll", "description": "k-nearest-neighbor search using D3 quadtrees"}, {"id": "0dfac0e7c3d33fcca86b", "user": "<PERSON><PERSON><PERSON>", "description": "HSL Decomposition"}, {"id": "1cccb8a71e8c5aaf6c08", "user": "<PERSON><PERSON><PERSON>", "description": "HSL Decomposition 2"}, {"id": "4248146", "user": "mbostock", "description": "Hexagonal Binning (Area)"}, {"id": "6a2d04e4eb8ae8a72223", "user": "ch<PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "f2540e53c4e327a145d0", "user": "darrenjaworski", "description": "OK Voting Trends"}, {"id": "e552ead70d7baaa77739", "user": "darrenjaworski", "description": "Voting trends OK Upper House"}, {"id": "a0e1406d6bb56ff70269", "user": "djokicx", "description": "Multi-Series Line Chart"}, {"id": "f1b2b824bd9cccea57df", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "7caf3e0c1bff5f4ffaac", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "a1c9688944f950218658"}, {"id": "2dd0548aee7cd62dd496", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "247b71fdef129a907f35", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "36511a38ee44a254c172", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser"}, {"id": "64b015914153b85419ce", "user": "hungvietdo", "description": "starbucks in Norfolk"}, {"id": "ed790586548e472f1f57", "user": "hungvietdo", "description": "Starbucks in Norfolk (mousehover)"}, {"id": "43c5ecc0fe2199ea84dc", "user": "jpnoll", "description": "genome browser"}, {"id": "646c80e683c3fdecf5b7", "user": "jpnoll", "description": "Genome Ruler"}, {"id": "1a32a92131723a1cc808", "user": "jpnoll", "description": "Genome Ruler"}, {"id": "847da6bc1fd5e0b0579a", "user": "lambrex", "description": "genome browser"}, {"id": "8b896db8aac59dac7a37", "user": "Smith5mr", "description": "Arc Padding II"}, {"id": "0c1b97dc48a6096ea7ce", "user": "mcgovemc", "description": "genome browser"}, {"id": "5d0b0777aa681f849090", "user": "Smith5mr", "description": "genome browser"}, {"id": "fb8e1e6750843954ead0", "user": "AdamBlaine1", "description": "The genome browser"}, {"id": "bbc662a5bf51665cce8b", "user": "mcgovemc", "description": "genome browser"}, {"id": "e785328c22638497dcb5"}, {"id": "17b1a9d5f1788c35b255", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "ffe781dfde208dd3db46"}, {"id": "5905838e05c8035329cb"}, {"id": "e7da8d7aac86fce33bee"}, {"id": "2312026badf1ad2a6cf4", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "8c67e661fa919f87f182", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "92e1dc0212d0cbf99a82", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "66daabcfeed118d590f6", "user": "Jay-Oh-eN", "description": "The Simplest Scatter"}, {"id": "32db87891b734b6c7267", "user": "Jay-Oh-eN", "description": "A <PERSON>r <PERSON> (color added)"}, {"id": "57f23fbddd23a0386030", "user": "SpaceActuary", "description": "Walmarts"}, {"id": "2af6e6e47935190936b1", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "TDD Bar Chart"}, {"id": "e1aa61856118edfa624d", "user": "d3noob", "description": "d3.js Graph with many features"}, {"id": "7830074", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, {"id": "f4035e412a7822abf074", "user": "RuiChang123", "description": "Portugal banking"}, {"id": "4d79d0cdb41a16903b7e", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "DashBoard"}, {"id": "29c2c89bcdf66c2553c2", "user": "SpaceActuary", "description": "Bar Chart Transitions - Positive to Negative"}, {"id": "4c0c9844596a2d00938a", "user": "SpaceActuary", "description": "Trapezoid Charts"}, {"id": "54181ad2cb45d7e41ac8", "user": "SpaceActuary", "description": "Trapezoid Charts 2"}, {"id": "fbb43f7cf188fa8f2d15", "user": "en<PERSON><PERSON>", "description": "The Counted: simple example"}, {"id": "9f3884a9f9474e9beedd", "user": "TheLady", "description": "The Counted: simple example"}, {"id": "00c36fbe045e5d0290fc", "user": "nguyenbq", "description": "Genome Viewer"}, {"id": "93e01a9c651ebb8f0968", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "sdfgsg"}, {"id": "955beda9851111b7c4b1", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "dce660b60d70576d6447"}, {"id": "545566453d4c45d1f4a3", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "f6db3b84de562142fe4b", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "9c001d2ad72ea5c8f91c", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "fb082e67cd821b4362ef", "user": "<PERSON><PERSON><PERSON>", "description": "scatter inner nodes ordering"}, {"id": "b956ee7215b7b50bc78a", "user": "xaranke", "description": "d3-hexbin demo"}, {"id": "a1a7d04bdd34c2096eaf", "user": "fabid", "description": "Rectangular binning - 2d histogram"}, {"id": "0c4cf357ab5ed3e90382", "user": "fabid", "description": "d3-heatmap"}, {"id": "024452c42b94723b401d", "user": "fabid", "description": "d3-returntimemap"}, {"id": "1d8085294b6094e617e9", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser"}, {"id": "a3cd99de2763a9a696d1", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser with GC"}, {"id": "13933a693dc8fac38c62", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser with GC"}, {"id": "db6533daf51fb05b0af6", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser with numbering, color, reverse, and stagger"}, {"id": "7aa370b761c4763c0901"}, {"id": "c8bb2ca99a6a3b894217", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser with numbering, color, reverse, and stagger"}, {"id": "ed16bc3c6fe8c4b051db", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser with numbering, color, reverse, and stagger"}, {"id": "90c4cfd299ca35bc54d8", "user": "g<PERSON><PERSON><PERSON>y", "description": "genome browser with numbering, color, reverse, and stagger"}, {"id": "7402f55e19e1e609f014", "user": "en<PERSON><PERSON>", "description": "Node splitting"}, {"id": "36014350348825ded276", "user": "en<PERSON><PERSON>", "description": "rigid links"}, {"id": "0568c594c55b8951b94c", "user": "<PERSON><PERSON><PERSON>", "description": "rigid links"}, {"id": "fd78a64d3ff9f03907c4", "user": "en<PERSON><PERSON>", "description": "polygroups visualization"}, {"id": "9d0251925a6bf6409533", "user": "margeigh", "description": "Foundations of D3: Data Binding Scope"}, {"id": "201b1e8e5a25dfcf76d4", "user": "margeigh", "description": "Foundations of D3: Data Binding Scope"}, {"id": "26b6602160cd14c7e143", "user": "margeigh", "description": "Foundations of D3: Data Binding Scope"}, {"id": "5cc8033ab729fbd9a4c9", "user": "margeigh", "description": "Foundations of D3: Data Binding Scope"}, {"id": "eb69ef5fbcd2330a7d61", "user": "en<PERSON><PERSON>", "description": "polygroups visualization: activate"}, {"id": "12cf7435eb7ccdbd8d02", "user": "mdcscry", "description": "Veggie 2014 Show Reel"}, {"id": "ab8b613ca00de4f31b05", "user": "mdcscry", "description": "VeggieCirclePack 2014"}, {"id": "7bb89480b28f189fafd3", "user": "mdcscry", "description": "VeggieBurst 2014"}, {"id": "9406077edb5a01d13d02", "user": "mcgovemc", "description": "genome browser with tool tip ####"}, {"id": "1028a3ee9ab4642199db", "user": "mcgovemc", "description": "genome browser %"}, {"id": "090f603aa3ef1c5fcf0b", "user": "mcgovemc", "description": "genome browser"}, {"id": "bf8dbe207e9aa4c74b21", "user": "mcgovemc", "description": "genome browser"}, {"id": "abdff74d4f8ebf9b3350", "user": "<PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "c16d4bf5e677045e9b94", "user": "mcgovemc", "description": "genome browser"}, {"id": "8d48064f03c22ae1a539", "user": "mcgovemc", "description": "genome browser"}, {"id": "d30e1f3cd1f7219bd0e5", "user": "diepvf", "description": "genome browser"}, {"id": "3fa3c1a523c8f3c2cfb9", "user": "micahstubbs", "description": "Sankey Particles with only inline styles"}, {"id": "f5922ed4d0ac1ac2161f", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Rotated hexbin"}, {"id": "de3ee03f25aa9f134ecf", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Force directed graph using tabletop.js and shortestpath"}, {"id": "3918369", "user": "ZJONSSON", "description": "d3.legend example"}, {"id": "c9b90689c1438f57d649", "user": "d3noob", "description": "Sankey from csv with d3.js"}, {"id": "6c6dd78bb6502e14b2f2", "user": "zaccagbj", "description": "genome browser"}, {"id": "9c856f4944e50b734b85", "user": "AdamBlaine1", "description": "The genome browser"}, {"id": "f2befd433e182193027dfa1d1b7a65d4", "user": "BDAguila", "description": "fresh block"}, {"id": "19c88beca509ce5195643099f5740838", "user": "BDAguila", "description": "fresh block"}, {"id": "9409231fdbdc710dcbb46cff643f86c9", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "c185b3758e7fd011f9a32a3c0b806839"}, {"id": "42f1c37f325999025db3", "user": "BDAguila", "description": "genome browser"}, {"id": "cc6cd97c95a80f35c824cc24a5bf0f6d", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "2e1f2a94cd49a429cda6d5a3d77e5271"}, {"id": "33881b1947efe610b0e743580af093f8", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "3f6b48f9c5d72fb14fafc28ed3129a9f"}, {"id": "6d57db7d251fbf26360cb88a7f5a7296", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "751159e6a2290ab4417e5deac63adec1", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "7574590639a3fd654d87f02db11bddb1", "user": "er<PERSON><PERSON>el", "description": "genome browser"}, {"id": "a2746196ad4c3a2d46e416d27c52d2cf"}, {"id": "e0408aa623ee83b1eb8d", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "9327a6642682b87430ec", "user": "scresawn", "description": "A Bar Chart"}, {"id": "af2040b064e5d2e2e589", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "A Bar Chart"}, {"id": "9bae6529ccab4b902a50", "user": "CBasis", "description": "d3.js wiki visualization"}, {"id": "ba2ffcaaf8c8b7541661", "user": "Darius9", "description": "genome browser(testing1)"}, {"id": "5993342", "user": "nsonnad", "description": "Circle-bound D3 force layout"}, {"id": "8241b9338930f081275090c4d421b4bc", "user": "Jay-Oh-eN", "description": "Single Multiple"}, {"id": "c649af7f141a700dca817b97386147f9", "user": "Jay-Oh-eN", "description": "Multiple Multiples"}, {"id": "5da8fc981efc46180000cc749b99465d", "user": "Jay-Oh-eN", "description": "SF AirBnB Listings: Pie Chart"}, {"id": "8232e0e796a6dbddc33caffe4929d985", "user": "Jay-Oh-eN", "description": "SF AirBnB Listings: Pie Chart with Text Annotation"}, {"id": "f372f1555d1ab41dadd1", "user": "Jay-Oh-eN", "description": "An Experiment in Sketching: Visualizing SF AirBnB Listings"}, {"id": "d4a745960538e937dcde13f61e9893f1", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Beeswarm + maintain x-position of nodes"}, {"id": "91c5e84bed1ba6b1541deaa5d5f4f258", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "ForceLayout - maintain x-position of nodes"}, {"id": "6a0b4488bc60ea61eb887bf4d1f9b2b5", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Force Layout - faster final arrangement"}, {"id": "151f6344ffd02105a67a", "user": "bj<PERSON><PERSON>", "description": "Bounded Force Layout - force rank by group"}, {"id": "e6a1a8b53e87cadd00aa", "user": "Sokrates86", "description": "alkoholi-kulutus-kuolema"}, {"id": "7ac04d6c0797b583c5c79bea0dcb2a3f", "user": "SpaceActuary", "description": "Mini Chart"}, {"id": "6fd7a74709e6d24bf327439c62a3f1c1", "user": "SpaceActuary", "description": "Mini Charts"}, {"id": "b408e03229958635c43b", "user": "SpaceActuary", "description": "Bugle Charts"}, {"id": "4055889", "user": "mbostock", "description": "Pseudo-<PERSON><PERSON> Cartogram"}, {"id": "9a92db9794c21d576087", "user": "SpaceActuary", "description": "Pseudo-<PERSON><PERSON> Cartogram"}, {"id": "53fe9d373ca3b3e20632f69c5169a59d", "user": "scresawn", "description": "final exam question 2"}, {"id": "9921862", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Path transition"}, {"id": "14fcf6f266e877cb1c23", "user": "jala<PERSON>", "description": "network fade"}, {"id": "9078690", "user": "mbostock", "description": "Quadtree Search"}, {"id": "1593f53c0c8346d067491f39255d0b84", "user": "<PERSON><PERSON><PERSON>", "description": "K-D Tree Nearest Neighbors (k-NN)"}, {"id": "7c0bd51dab6b9665a315fef06c436f27", "user": "<PERSON><PERSON><PERSON>", "description": "Triangular Binning I"}, {"id": "dcfa9c27db140183bd87f3fc90efaf10", "user": "<PERSON><PERSON><PERSON>", "description": "Triangular Binning II"}, {"id": "b4aa88691528c0f0b1fa", "user": "curran", "description": "Chiasm Foundation"}, {"id": "e98e438c05eba9b8882412ca79af1c6f", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Chiasm Foundation"}, {"id": "814c00f234349de899ef0d95649974e9", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Chiasm Foundation"}, {"id": "cb9da9f0caa674dc908708336645f0dd", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Chiasm Bar Chart and Line Chart"}, {"id": "1bc8659b52b35b8a320f3fefb7275ef5", "user": "b<PERSON>hon", "description": "Triangle to Square"}, {"id": "6276c0db5b24cf6bfea02b9dd72c378d", "user": "b<PERSON>hon", "description": "Triangle to Triangle I"}, {"id": "73a90dd4229f8941b7f79df8b2c8505d", "user": "b<PERSON>hon", "description": "Sutherland<PERSON><PERSON><PERSON><PERSON>"}, {"id": "28821b58b36a81d36a6a814ecdbdc037", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pulse Animations"}, {"id": "d6b7f134335ddc4d12a3e143f81304d8"}, {"id": "8d863db116f226727c71", "user": "ch<PERSON><PERSON><PERSON>", "description": "genome browser blocks "}, {"id": "735c8063f00c773ef1dea78f62a321fa", "user": "curran", "description": "Margin Convention II with ReactiveModel"}, {"id": "6f97aa94357cae4611c54a80c11f6128", "user": "curran", "description": "Responsive Axes with ReactiveModel"}, {"id": "b8e76dd7003538975b3e81e86ac6dd1e", "user": "curran", "description": "Margin Convention with ReactiveModel"}, {"id": "974c9def890f8ac0172611921fb51b8a", "user": "curran", "description": "Responding to Resize with ReactiveModel"}, {"id": "8622315", "user": "devgru", "description": "Simple stream + interpolation, layout offset & axes"}, {"id": "a05b3ac17407e5d1d9cc989f882125af"}, {"id": "e14c09b73d241d89536cd81b35771215", "user": "devgru", "description": "Simple stream 2016"}, {"id": "5485fa28d3fdd422930b53ab330f96e3", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "D3 Dynamic Array of Tables"}, {"id": "681bfd06a86a6d5e237c", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Genome Viewer Final"}, {"id": "2cce0f9305ec755a53e7", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "genome browser"}, {"id": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"id": "6222544da1551c11d55b505edf4b88fa", "user": "lambrex", "description": "genome browser"}, {"id": "dcd52a1daf6b00e5f0afa9b32e2ca043", "user": "lambrex", "description": "genome browser abundance version"}, {"id": "f8bacc54b016d6ea014e54d81f2b7e48", "user": "jpnoll", "description": "genome browser abundance version"}, {"id": "4d7dfded54d1e2162265862ffb178f4a", "user": "scresawn", "description": "genome browser abundance version"}, {"id": "9d49a219f72d8958313eb7fd9f236936", "user": "lambrex", "description": "genome browser abundance version"}, {"id": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "user": "diepvf", "description": "genome browser abundance version"}, {"id": "7b750aac02c274ce7be1a2ec65681b6b", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"id": "e594934db447063ce886b35c2e3ea0c3", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "63b313292ecb948800fb1bebaef690b3", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "73d2088a4754f9a3b46db81c3a12ca42"}, {"id": "752ff3769123dd6f733dd1a0fd139698", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "8e82406ccf2abef3600b5c63db1830ca"}, {"id": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "e08d1b622815450a47a52deb8644533d"}, {"id": "e844a36eff424d262f3de5d9baa16b91", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "ea33bdf3e6d636ecd1bb9f1febd0125e", "user": "dun<PERSON><PERSON>", "description": "genome browser"}, {"id": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"id": "34dc0e1aaabac24684ec622da2add7e9", "user": "eoiny", "description": "fresh block"}, {"id": "54b835498f0288d2affc5c2d021864d1", "user": "eoiny", "description": "barNotes"}, {"id": "a1169104fd3d0b78a2b60a2f099e0391", "user": "eoiny", "description": "barNotes"}, {"id": "2338f36048aaa1372f386daa32bf624c", "user": "eoiny", "description": "barNotes"}, {"id": "dde8f503ef6ea72edc8f3212896497d7", "user": "eoiny", "description": "barNotes"}, {"id": "7c4f11418ebb53a997531c718e760f7c"}, {"id": "deda5442d64c34186b537f11751004aa"}, {"id": "0271e40675307d3aede5cecdad5715e0", "user": "eoiny", "description": "barNotes"}, {"id": "1919bd8c2f574caa17ba", "user": "en<PERSON><PERSON>", "description": "State Grid Minimap"}, {"id": "5606736552160acb3351", "user": "en<PERSON><PERSON>", "description": "Quadtree Tree"}, {"id": "676bfe2b6e67e61e0656", "user": "en<PERSON><PERSON>", "description": "topojson on a map: setup-gl"}, {"id": "373e03ed01dfcf97d985", "user": "emeeks", "description": "Networks - Graphs 6"}, {"id": "da3f27dd8be82859b45e14898aa7fce0", "user": "BDAguila", "description": "fresh block"}, {"id": "32d4c5badc4534da66b827122d6a1831", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "ba1beb933b849d986453f374c9cfd206", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "b895d79139c315b4a14c7c4f63a9b00e", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "db4dcb20e68ee366eb4c253399a3ad5d", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "4bfacbc47612de727bf99a32d632abc3", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "f8eaa285348a91b74ff2bed44d4af44f", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "b28c34585d071b6937fdf74b9a85bca4", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "ede8ef37272abcb60013f885b6736d63", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "f48e7435ff4bb34180e744b0786c57de", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "b88b41124a4e1a398f8443041c41e24f", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "7c0354016ccc2f7974d84714dc8e8d4a", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "0d1bfe27165765ed03f8f175ce8680f1", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "627517c6d7a1b1384bce3f5402fbb9c4", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "ed2735e3952c35c92852410c21d15f46", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "25f855bb1f47666b42962a82216dfd3e", "user": "er<PERSON><PERSON>el", "description": "fresh block"}, {"id": "b29a3572b3e3f19e7a22b18ef9134920", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "50f0b4412292163bf4b50d06253994ae", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler"}, {"id": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"id": "b4f37349887a4c178ef93ed855153605", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes"}, {"id": "1431967216e048fcb4890445e1741c7c", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes"}, {"id": "59d18946ec237fe864d70b811a62eddd", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen"}, {"id": "9be28947eccefb1ac05598b8fbc53d27", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering"}, {"id": "0bbec668d5ba6765cc908eafd6c556fc", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering path attempt1"}, {"id": "7d41f75647bdc4fef8736442db42bad0", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering Tool tip"}, {"id": "afc1b005be34f64b495b259263733b35", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering Tool tip repeat connect attempt1"}, {"id": "33505001b3f4ff96c29fcb15425b561b", "user": "scresawn", "description": "Repeat finder circle ruler arc genes line gen keystone numbering Tool tip repeat connect attempt1"}, {"id": "90056c4e88c140345ac8a52b8999cf1c", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering Tool tip repeat connect attempt1"}, {"id": "0762f972ce5785f6efd4b975f2a95ff5", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering Tool tip repeat connect line tool tips"}, {"id": "5f080cc5f6d000b6540bded03b3dc133", "user": "g<PERSON><PERSON><PERSON>y", "description": "Repeat finder circle ruler arc genes line gen keystone numbering Tool tip repeat connect line random color tool tips"}, {"id": "d390147f53cbcf8094b6aefea8791fc8", "user": "g<PERSON><PERSON><PERSON>y", "description": "Wheel animated Repeat finder circle ruler arc genes line gen keystone numbering Tool tip repeat connect line random color tool tips"}, {"id": "58abc7c39f8bcd04de56"}, {"id": "302096884d5fbc1817062492605b50dd", "user": "emeeks", "description": "D3v4 Constraint-Based Layout"}, {"id": "62cf60e116ae821c06602793d265eaf6", "user": "nbremer", "description": "Linear SVG Gradient - Traffic accidents per Day & Hour combination"}, {"id": "7f8df0bcd7fba7e531e6", "user": "hungvietdo", "description": "VI7: Node-link diagram and Adjacency matrix"}, {"id": "0020bebb012760de0d54", "user": "jala<PERSON>", "description": "network fade 2"}, {"id": "7315455", "user": "officeofjane", "description": "Small multiple bar charts with tooltips"}, {"id": "93c5c349be14f459c98a", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Stacked-to-Multiples (improved)"}, {"id": "24699e2002b4cea23615", "user": "j<PERSON><PERSON><PERSON>", "description": "d3intro_ex02"}, {"id": "ab293c5472d02639ca4f", "user": "j<PERSON><PERSON><PERSON>", "description": "d3intro_exercise_selection"}, {"id": "8ff54b5cc0640152c563", "user": "j<PERSON><PERSON><PERSON>", "description": "d3intro_ex21"}, {"id": "bd880dbc71d2307e2a5b", "user": "j<PERSON><PERSON><PERSON>", "description": "d3intro_exercise_data_w_key_solved"}, {"id": "95685c2bcf9407e687d6"}, {"id": "94e7aebb7eb975291d9b", "user": "jpmarindiaz", "description": "fresh block"}, {"id": "e949444235af20478188", "user": "jpnoll", "description": "genome browser with GC"}, {"id": "d1b6461efdf225f53d0f", "user": "jpnoll", "description": "genome browser with GC"}, {"id": "0dfef7aa92676a47b201", "user": "jpnoll", "description": "genome browser"}, {"id": "b16dad1cea69952f5b72", "user": "jpnoll", "description": "genome browser"}, {"id": "a675d439b9ed06a856bb", "user": "jpnoll", "description": "genome browser"}, {"id": "3b3ae1013968c0a1613b", "user": "jud<PERSON><PERSON><PERSON><PERSON>", "description": "fresh block"}, {"id": "41c811c603360c27d0ae", "user": "jud<PERSON><PERSON><PERSON><PERSON>", "description": "Counter"}, {"id": "87cf840dd72fef25ac24", "user": "jud<PERSON><PERSON><PERSON><PERSON>", "description": "Counter"}, {"id": "bde995eccb68a6294dd9", "user": "jud<PERSON><PERSON><PERSON><PERSON>", "description": "Counter"}, {"id": "ba54603cfa4b6d7222c0", "user": "jud<PERSON><PERSON><PERSON><PERSON>", "description": "Counter"}, {"id": "13fa3e31fed7834eee10", "user": "RandomEtc", "description": "D3 Australia Conformal Conic"}, {"id": "6944484", "user": "syntagmatic", "description": "Canvas Force-Directed Graph"}, {"id": "7fedb282aca4df15b0d4", "user": "lambrex", "description": "genome browser"}, {"id": "ddd41979fd8e96c976f6"}, {"id": "c7cf338b400b90b04f95"}, {"id": "9ffb265094d8dffb8627", "user": "lambrex", "description": "genome browser"}, {"id": "eaab03204fa1acac43ea", "user": "ch<PERSON><PERSON><PERSON>", "description": "Genome Viewer"}, {"id": "0102759ebcb977f2a98b", "user": "wc<PERSON><PERSON>son11", "description": "Animate path in D3"}, {"id": "887677acf92b55b3accd739e77c60fe4", "user": "<PERSON><PERSON><PERSON>", "description": "genome browser with tool tip "}, {"id": "ac5b726c53ec02618de7795429c6f833", "user": "syntagmatic", "description": "Baseball Scatterplot Matrix"}, {"id": "5246b8a643393f0753a11b98129a3237", "user": "micahstubbs", "description": "canvas links positioning woes"}, {"id": "e330141d1279c6115a3a", "user": "emeeks", "description": "Ch. 11, Fig. 10 - D3.js in Action"}, {"id": "c6d3072074b1a9f717cf66068f34d8b6", "user": "dhoboy", "description": "China Air Quality Canvas"}, {"id": "1061eef4ce7545bdd52537dcef952e7e", "user": "syntagmatic", "description": "Air Quality Calendars"}, {"id": "428036f0f948a5c2b95f8a4b91931ee6", "user": "micahstubbs", "description": "ES2015 Sequences Sunburst"}, {"id": "b7e78429ce54abc2154f", "user": "micahstubbs", "description": "Sankey Particles - Transparent Links"}, {"id": "becd7b34d93b2d80d828", "user": "emeeks", "description": "Pie Transition Ch. 5 - D3.js in Action"}, {"id": "5703573bd695c66b6e99", "user": "<PERSON><PERSON><PERSON>", "description": "Browser-zoom robust foreignObject tooltips"}, {"id": "f6a30cdf04d252876b44", "user": "micahstubbs", "description": "Browser-zoom robust static foreignObject tooltips"}, {"id": "542f350f5af22fba72b5"}, {"id": "9c72a94ca5a0d04c68e4"}, {"id": "2814080d24a44c357781", "user": "mpmckenna8", "description": "breadBoard"}, {"id": "b8f13d84df4fbbaa98c0", "user": "mpmckenna8", "description": "breadBoard"}, {"id": "8e09d21c2a15d7234b6d", "user": "mpmckenna8", "description": "breadBoard"}, {"id": "6f5b472cabe8f85583989a40eef0303c", "user": "nbremer", "description": "Animated gradient - Simple rectangle"}, {"id": "a43dbd5690ccd5ac4c6cc392415140e7", "user": "nbremer", "description": "Linear SVG Gradient - Weather Radial"}, {"id": "a6690ca67800a2abafcd71ef4725f33f", "user": "nbremer", "description": "Animated gradient - <PERSON><PERSON>'s chart of <PERSON>'s Russia campaign"}, {"id": "5cd07f2cb4ad202a9facfbd5d2bc842e", "user": "nbremer", "description": "Linear SVG Gradient - A hexagonal SOM heatmap with color legend"}, {"id": "1d3dcaefadd4c8f33318fe017cf730d6", "user": "smith13mr", "description": "genome browser for final group project"}, {"id": "d9330fbe7bbf1a68e3d634602eb4d7a2", "user": "scresawn", "description": "genome browser for final group project"}, {"id": "f405977b9bf51851a6b86476992c6ce9", "user": "smith13mr", "description": "Two tooltips and fade out"}, {"id": "794e8d9eb8c153a1778cc487ff4465ff", "user": "scresawn", "description": "Two tooltips and fade out"}, {"id": "9306799", "user": "dbuezas", "description": "Pie charts labels"}, {"id": "1209499", "user": "biovisualize", "description": "Download generated SVG with preview (from mbostock)"}, {"id": "ca8347ecae78391b8cc9", "user": "scresawn", "description": "Download generated SVG with preview (from mbostock)"}, {"id": "07cbeabf93c7ece31c2d", "user": "thoma2nm", "description": "Bar Chart"}, {"id": "ca351b22bc0778e97d79", "user": "sliu72", "description": "Multi-Series Line Chart"}, {"id": "9e82d298e917564240ad98c881d6c75b", "user": "smith13mr", "description": "final exam question 2"}, {"id": "9adf74fb08394082c393d8290c00e843", "user": "smith13mr", "description": "genome browser"}, {"id": "4742802e9cf761912270", "user": "smith13mr", "description": "genome browser 2 with numbers"}, {"id": "9747494f9437c0567eed", "user": "smith13mr", "description": "genome browser 2 with numbers"}, {"id": "5085fcdd086cb5174867", "user": "smith13mr", "description": "genome browser 4 with numbers"}, {"id": "a5df54f0c34518424b00", "user": "smith13mr", "description": "genome browser with name numbers"}, {"id": "92841c212b11c534713782544cfdf10f", "user": "sxywu", "description": "<PERSON><PERSON> tweets #1"}, {"id": "8b7d381614a093195eec2636cab9ef2f", "user": "sxywu", "description": "<PERSON><PERSON> tweets #2"}, {"id": "9ea31a49cc0af1fdeed189619f651f5b", "user": "sxywu", "description": "<PERSON><PERSON> tweets #3"}, {"id": "c98b726ab1cc4f4b1477", "user": "veltman", "description": "Old atlas style #2"}, {"id": "606b23b77ecf3991d3b0", "user": "veltman", "description": "Old atlas style"}, {"id": "cc5dcff17db32e4e910f", "user": "veltman", "description": "Old atlas style #3"}, {"id": "f0f27bb61ef20a6b2e17d7231e2294c6", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Multi-Foci Force Layout Along Path"}, {"id": "5e1fbf27aa88a0f96680634bfd4079b3", "user": "wc<PERSON><PERSON>son11", "description": "D3.js Drag and Drop, Zoomable, Panning, Collapsible Tree with auto-sizing."}, {"id": "f494728af00496ea1aa4", "user": "zaccagbj", "description": "newer work on genome thing"}, {"id": "4d76ddafa3682bbcae37", "user": "zaccagbj", "description": "again newer work on genome thing"}, {"id": "17a54b0b2520fb6dcae3", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "mediseen0.2"}, {"id": "1554f5cc59f7b4b3403d", "user": "<PERSON><PERSON><PERSON><PERSON>", "description": "mediseen0.2 18march2016"}], "links": [{"source": "950642", "target": "4062045"}, {"source": "1341281", "target": "1341021"}, {"source": "4963273", "target": "4963004"}, {"source": "4963273", "target": "4962892"}, {"source": "4967213", "target": "4963273"}, {"source": "4967213", "target": "4963004"}, {"source": "4967213", "target": "4962892"}, {"source": "4969184", "target": "4060606"}, {"source": "4973620", "target": "4686432"}, {"source": "5044313", "target": "3757101"}, {"source": "5052095", "target": "5028304"}, {"source": "5100636", "target": "1346410"}, {"source": "5100636", "target": "1098617"}, {"source": "5100636", "target": "4341574"}, {"source": "5100636", "target": "4341417"}, {"source": "5107491", "target": "4183330"}, {"source": "5107491", "target": "9656675"}, {"source": "5126418", "target": "4707858"}, {"source": "5141278", "target": "1153292"}, {"source": "5141278", "target": "2706022"}, {"source": "5141528", "target": "5141278"}, {"source": "5155181", "target": "5141278"}, {"source": "5155181", "target": "5141528"}, {"source": "5181105", "target": "4342045"}, {"source": "5348789", "target": "3305854"}, {"source": "5386999", "target": "5180185"}, {"source": "5416405", "target": "5416440"}, {"source": "5518052", "target": "5519642"}, {"source": "5518052", "target": "5519819"}, {"source": "5518052", "target": "5520449"}, {"source": "5518052", "target": "5522467"}, {"source": "5518052", "target": "d22bbf92231876505e5d"}, {"source": "5518052", "target": "5553051"}, {"source": "5519642", "target": "5519819"}, {"source": "5519642", "target": "5520449"}, {"source": "5519642", "target": "5522467"}, {"source": "5519642", "target": "d22bbf92231876505e5d"}, {"source": "5519642", "target": "5553051"}, {"source": "5519819", "target": "5520449"}, {"source": "5519819", "target": "5522467"}, {"source": "5519819", "target": "d22bbf92231876505e5d"}, {"source": "5519819", "target": "5553051"}, {"source": "5520449", "target": "5522467"}, {"source": "5520449", "target": "d22bbf92231876505e5d"}, {"source": "5520449", "target": "5553051"}, {"source": "5522467", "target": "d22bbf92231876505e5d"}, {"source": "5522467", "target": "5553051"}, {"source": "5661984", "target": "1642989"}, {"source": "5662135", "target": "1705868"}, {"source": "5689783", "target": "4180634"}, {"source": "5732719", "target": "5732685"}, {"source": "5820393", "target": "5616813"}, {"source": "5874133", "target": "5874214"}, {"source": "5874133", "target": "5874227"}, {"source": "5874133", "target": "5874229"}, {"source": "5874133", "target": "5874233"}, {"source": "5874133", "target": "5874236"}, {"source": "5874214", "target": "5874227"}, {"source": "5874214", "target": "5874229"}, {"source": "5874214", "target": "5874233"}, {"source": "5874214", "target": "5874236"}, {"source": "5874214", "target": "5397362"}, {"source": "5874227", "target": "5874229"}, {"source": "5874227", "target": "5874233"}, {"source": "5874227", "target": "5874236"}, {"source": "5886992", "target": "4061961"}, {"source": "5993342", "target": "1129492"}, {"source": "6052681", "target": "6052814"}, {"source": "6112167", "target": "6187523"}, {"source": "6150151", "target": "5249328"}, {"source": "6173659", "target": "5249328"}, {"source": "6185069", "target": "3887118"}, {"source": "6203413", "target": "1703449"}, {"source": "6226150", "target": "5100636"}, {"source": "6226150", "target": "6228457"}, {"source": "6228457", "target": "5100636"}, {"source": "6279966", "target": "5691513"}, {"source": "6354551", "target": "5649592"}, {"source": "6419716", "target": "2846454"}, {"source": "6420534", "target": "4062045"}, {"source": "6420534", "target": "4060366"}, {"source": "6420534", "target": "1405439"}, {"source": "6420534", "target": "1734663"}, {"source": "6439398", "target": "3884955"}, {"source": "6459889", "target": "2206590"}, {"source": "6476579", "target": "3885304"}, {"source": "6506614", "target": "21746a9668ffdf6d8242"}, {"source": "6514960", "target": "5649592"}, {"source": "6521802", "target": "5649592"}, {"source": "6531064", "target": "5649592"}, {"source": "6574995", "target": "11094667"}, {"source": "6769077", "target": "5230564"}, {"source": "6944484", "target": "4062045"}, {"source": "7008736", "target": "5249328"}, {"source": "7008736", "target": "6622700"}, {"source": "7008736", "target": "6173659"}, {"source": "7015186", "target": "7023140"}, {"source": "7015186", "target": "7008736"}, {"source": "7015186", "target": "6521802"}, {"source": "7023140", "target": "7008736"}, {"source": "7023140", "target": "6521802"}, {"source": "7023794", "target": "7015186"}, {"source": "7025272", "target": "7023794"}, {"source": "7025272", "target": "6982528"}, {"source": "7025272", "target": "7008736"}, {"source": "7025272", "target": "4060606"}, {"source": "7051484", "target": "7051552"}, {"source": "7090426", "target": "4063423"}, {"source": "7188018", "target": "7032353"}, {"source": "7233233", "target": "4237768"}, {"source": "7233233", "target": "3846051"}, {"source": "7233233", "target": "4060366"}, {"source": "7315455", "target": "1157787"}, {"source": "7315455", "target": "6476579"}, {"source": "7328902", "target": "2295155"}, {"source": "7352810", "target": "3680999"}, {"source": "7475950", "target": "7328902"}, {"source": "7475950", "target": "2295155"}, {"source": "7483341", "target": "3750558"}, {"source": "7493693", "target": "7483341"}, {"source": "7493693", "target": "3680999"}, {"source": "7512487", "target": "7493693"}, {"source": "7512487", "target": "5001347"}, {"source": "7512487", "target": "6901037"}, {"source": "7517984", "target": "7512487"}, {"source": "7517984", "target": "6890861"}, {"source": "7518871", "target": "7517984"}, {"source": "7722907", "target": "7188018"}, {"source": "7830074", "target": "1153292"}, {"source": "7830074", "target": "2706022"}, {"source": "7910706", "target": "4339184"}, {"source": "7910706", "target": "1093025"}, {"source": "7938492", "target": "7910706"}, {"source": "8028264", "target": "7025272"}, {"source": "8028264", "target": "7962295"}, {"source": "8028264", "target": "7938492"}, {"source": "8028264", "target": "7960622"}, {"source": "8028264", "target": "7015186"}, {"source": "8028264", "target": "7023794"}, {"source": "8028264", "target": "4136647"}, {"source": "8028264", "target": "7493693"}, {"source": "8031205", "target": "8028264"}, {"source": "8185373", "target": "3757132"}, {"source": "8203750", "target": "4180634"}, {"source": "8244446", "target": "7910706"}, {"source": "8244446", "target": "7938492"}, {"source": "8244446", "target": "8260510"}, {"source": "8246732", "target": "1346395"}, {"source": "8272715", "target": "8260510"}, {"source": "8272715", "target": "8272105"}, {"source": "8272715", "target": "8028264"}, {"source": "8272715", "target": "8031205"}, {"source": "8566245", "target": "3014589"}, {"source": "8699346", "target": "4063530"}, {"source": "8746032", "target": "7483341"}, {"source": "8772179", "target": "8272715"}, {"source": "8772179", "target": "6521802"}, {"source": "8772179", "target": "8260510"}, {"source": "8784278", "target": "8772179"}, {"source": "8784942", "target": "6514960"}, {"source": "8784942", "target": "8784278"}, {"source": "8787102", "target": "8784942"}, {"source": "8788101", "target": "8784278"}, {"source": "8788101", "target": "4341134"}, {"source": "8882722", "target": "8784942"}, {"source": "8900555", "target": "8566245"}, {"source": "8947565", "target": "8882722"}, {"source": "8947565", "target": "8784942"}, {"source": "8947565", "target": "8955159"}, {"source": "8947565", "target": "8947871"}, {"source": "8947871", "target": "8955159"}, {"source": "8949471", "target": "6531064"}, {"source": "8949471", "target": "5649592"}, {"source": "8955159", "target": "8949471"}, {"source": "8968230", "target": "6514960"}, {"source": "8968230", "target": "8949471"}, {"source": "8968230", "target": "6521802"}, {"source": "9204795", "target": "4327678"}, {"source": "9205238", "target": "9205257"}, {"source": "9205238", "target": "9205264"}, {"source": "9205257", "target": "9205264"}, {"source": "9216751", "target": "9216081"}, {"source": "9239214", "target": "9265219"}, {"source": "9306799", "target": "9572040"}, {"source": "9417919", "target": "5126418"}, {"source": "9417919", "target": "4573883"}, {"source": "9733594", "target": "3886208"}, {"source": "9746628", "target": "1153292"}, {"source": "9746628", "target": "9746652"}, {"source": "9746652", "target": "1153292"}, {"source": "9842512", "target": "9845999"}, {"source": "9842512", "target": "9842179"}, {"source": "9842512", "target": "9842284"}, {"source": "9871048", "target": "9871234"}, {"source": "9871234", "target": "3808218"}, {"source": "9912897", "target": "9914167"}, {"source": "9912897", "target": "9871048"}, {"source": "9914167", "target": "9871048"}, {"source": "9921862", "target": "1642989"}, {"source": "9972460", "target": "4343214"}, {"source": "9972460", "target": "4963273"}, {"source": "10527804", "target": "11085004"}, {"source": "10527804", "target": "3795544"}, {"source": "10527804", "target": "5732029"}, {"source": "10527804", "target": "4183330"}, {"source": "10527804", "target": "2206590"}, {"source": "10527804", "target": "7607535"}, {"source": "10743959", "target": "4163057"}, {"source": "10989553", "target": "10990305"}, {"source": "11085004", "target": "5731632"}, {"source": "11085004", "target": "1256572"}, {"source": "11114288", "target": "7e8e57368175a1433791"}, {"source": "11273546", "target": "9885869"}, {"source": "11337491", "target": "3014589"}, {"source": "d67e0c64f0cb392f8a78", "target": "ce9c2deb92042df13179"}, {"source": "d67e0c64f0cb392f8a78", "target": "6958e230e69a377620e3"}, {"source": "6958e230e69a377620e3", "target": "ce9c2deb92042df13179"}, {"source": "818723e652995b6c110d", "target": "c8b4472a648b08a26720"}, {"source": "818723e652995b6c110d", "target": "a23f857ee922bea4bded"}, {"source": "a23f857ee922bea4bded", "target": "c8b4472a648b08a26720"}, {"source": "29bccce80df0f253c97e", "target": "310c99e53880faec2434"}, {"source": "ebec037138b5866ff0d3", "target": "76342abc327062128604"}, {"source": "0d1635533f6fb5ac4da3", "target": "3150059"}, {"source": "ba569633d51ebec6ec6e", "target": "3711652"}, {"source": "91254c459602b31ba549", "target": "eb9cddd11657887e1e0d"}, {"source": "28f1fbeb41df9753e120", "target": "5194141"}, {"source": "002ef2fb5c1dc2df5821", "target": "c256fda4965d38e7c59b"}, {"source": "c256fda4965d38e7c59b", "target": "1092fe86860def004ea7"}, {"source": "c256fda4965d38e7c59b", "target": "5100636"}, {"source": "1092fe86860def004ea7", "target": "3289530"}, {"source": "1092fe86860def004ea7", "target": "4248145"}, {"source": "e7a13ff417ff5257a067", "target": "b3831331f475fdcb13df"}, {"source": "e7a13ff417ff5257a067", "target": "9c87aed9a53a3ff20e29"}, {"source": "9c87aed9a53a3ff20e29", "target": "b3831331f475fdcb13df"}, {"source": "0e57e7fff27f2eee431f", "target": "1af08ad6cdb01707c33f"}, {"source": "0e57e7fff27f2eee431f", "target": "3885304"}, {"source": "0e57e7fff27f2eee431f", "target": "60b40877ef898f19aeb8"}, {"source": "0e57e7fff27f2eee431f", "target": "3887235"}, {"source": "0e57e7fff27f2eee431f", "target": "134ed87c99257e3f2e31"}, {"source": "0e57e7fff27f2eee431f", "target": "4060606"}, {"source": "0e57e7fff27f2eee431f", "target": "3cc1a2a289dddbd64688"}, {"source": "620ebec1c2841ea5a929", "target": "7e830bd65ad749237e4d"}, {"source": "8beea1d918ff4104f9ab", "target": "3894205"}, {"source": "e5daff57a04c2639125e", "target": "b3ff6ae1c120eea654b5"}, {"source": "e5daff57a04c2639125e", "target": "3902569"}, {"source": "e5daff57a04c2639125e", "target": "4618602"}, {"source": "c9b90689c1438f57d649", "target": "c2637e28b79fb3bfea13"}, {"source": "a22c42db65eb00d4e369", "target": "38744a17f9c0141bcd04"}, {"source": "38744a17f9c0141bcd04", "target": "b3ff6ae1c120eea654b5"}, {"source": "473f0cf66196a008cf99", "target": "b3ff6ae1c120eea654b5"}, {"source": "7030f35b72de721622b8", "target": "b3ff6ae1c120eea654b5"}, {"source": "e1aa61856118edfa624d", "target": "b3ff6ae1c120eea654b5"}, {"source": "2efd8084d345dd23ef96", "target": "4ad06b0d9ee2a390eb20"}, {"source": "b3b3ff937241182ee67e", "target": "29a00092e4dbb7c659f7"}, {"source": "3a81930c054bb8ee39bf", "target": "33bc932b850792e832f8"}, {"source": "bbc451a7b9f902954e7c", "target": "8fadc5ac9c2a9e7c5ba2"}, {"source": "bbc451a7b9f902954e7c", "target": "7352810"}, {"source": "bbc451a7b9f902954e7c", "target": "c2cc7242c8f800c736c4"}, {"source": "c2cc7242c8f800c736c4", "target": "8fadc5ac9c2a9e7c5ba2"}, {"source": "c2cc7242c8f800c736c4", "target": "7352810"}, {"source": "805413fb3b2efaada1ce", "target": "3943967"}, {"source": "805413fb3b2efaada1ce", "target": "752b97cef3f880a813ab"}, {"source": "a931688fa8cb65db4079", "target": "b6e1d23c16dc76371a92"}, {"source": "b6e1d23c16dc76371a92", "target": "87d038562333a7ad4a64"}, {"source": "87d5dd02739089986d4a", "target": "3019563"}, {"source": "2c736a99c2f57bfac7e4", "target": "3019563"}, {"source": "2c736a99c2f57bfac7e4", "target": "87d5dd02739089986d4a"}, {"source": "2c736a99c2f57bfac7e4", "target": "3a68b0c81991e2e94b19"}, {"source": "2c736a99c2f57bfac7e4", "target": "d1e9ea2850047562be09"}, {"source": "87d038562333a7ad4a64", "target": "1af08ad6cdb01707c33f"}, {"source": "87d038562333a7ad4a64", "target": "3cc1a2a289dddbd64688"}, {"source": "d5252d37917ab6eab032", "target": "d1e9ea2850047562be09"}, {"source": "d5252d37917ab6eab032", "target": "1667367"}, {"source": "d1e9ea2850047562be09", "target": "cf4b98fff0517ca04667"}, {"source": "d1e9ea2850047562be09", "target": "3cc1a2a289dddbd64688"}, {"source": "81271937fa94fdbdd854", "target": "47aceae44bb5f8b63d7b"}, {"source": "81271937fa94fdbdd854", "target": "60eec0e1727c6c628728"}, {"source": "3cc1a2a289dddbd64688", "target": "1af08ad6cdb01707c33f"}, {"source": "3cc1a2a289dddbd64688", "target": "3885304"}, {"source": "3cc1a2a289dddbd64688", "target": "60b40877ef898f19aeb8"}, {"source": "3cc1a2a289dddbd64688", "target": "3887235"}, {"source": "3cc1a2a289dddbd64688", "target": "134ed87c99257e3f2e31"}, {"source": "3cc1a2a289dddbd64688", "target": "4060606"}, {"source": "01aa2685f083b6c1b9fb", "target": "ba63c55dd2dbc3ab0127"}, {"source": "01aa2685f083b6c1b9fb", "target": "e2744ee563f7cab80350"}, {"source": "01aa2685f083b6c1b9fb", "target": "bd552e711b8325c64729"}, {"source": "db1e524cae5e4344b2e6", "target": "3a68b0c81991e2e94b19"}, {"source": "6c0ce7a12c7d5497350d", "target": "8033015"}, {"source": "950cbe78b4c307fa14a1", "target": "5577023"}, {"source": "66d926fe73211fd650ec", "target": "5537697"}, {"source": "9b193942e2fc4ac88c6e", "target": "4060606"}, {"source": "4df29e2f8c6e20ed2baf", "target": "a22c42db65eb00d4e369"}, {"source": "752b97cef3f880a813ab", "target": "6476579"}, {"source": "752b97cef3f880a813ab", "target": "6123708"}, {"source": "9e04ccfebeb84bcdc76c", "target": "015d34d6d3d562877e51"}, {"source": "3b811f05a0ce39d0d7cd", "target": "015402cce2caa074551e"}, {"source": "3b811f05a0ce39d0d7cd", "target": "015d34d6d3d562877e51"}, {"source": "3b811f05a0ce39d0d7cd", "target": "dd73d3d8925cdf50df86"}, {"source": "015d34d6d3d562877e51", "target": "015402cce2caa074551e"}, {"source": "015d34d6d3d562877e51", "target": "5905182da50a4667dc00"}, {"source": "015402cce2caa074551e", "target": "5f255332a9dcb9906f84"}, {"source": "dc5be7c1d700eb6ecf46", "target": "0854f60e489b5ee6e11a"}, {"source": "dc5be7c1d700eb6ecf46", "target": "40a7be58639daf5d510e"}, {"source": "40a7be58639daf5d510e", "target": "0854f60e489b5ee6e11a"}, {"source": "b602b35c682ec69b9025", "target": "95800d30829ab8ab4ba6"}, {"source": "f2aff83148a5f64f3222", "target": "f1b4a29749408dd826ef"}, {"source": "465725cdc547c7cc8491", "target": "44bb05aab218a40a4c12"}, {"source": "465725cdc547c7cc8491", "target": "21746a9668ffdf6d8242"}, {"source": "44bb05aab218a40a4c12", "target": "21746a9668ffdf6d8242"}, {"source": "a40254b6cb914018ff81", "target": "2975320"}, {"source": "a40254b6cb914018ff81", "target": "6100713"}, {"source": "eda532b01c24bf4ac1d6", "target": "7148ee2a60a09fed0b43"}, {"source": "a6916e88484f962d8f95", "target": "7090426"}, {"source": "d6658135f11023171587", "target": "3793462de638478937"}, {"source": "58f8fe3e167ef47145d0", "target": "adc2dfee7aef48ce5485"}, {"source": "d4ff502dc32b5b04eae4", "target": "adc2dfee7aef48ce5485"}, {"source": "859c7b7150c81cc512b3", "target": "782b91b09326541a0fc8"}, {"source": "859c7b7150c81cc512b3", "target": "a5df8dc94786a27d6420"}, {"source": "5c4288aa30de1b949433", "target": "67648b87e80fbc31ad20"}, {"source": "1a5e8d77ae8feb464167", "target": "d15a4f16fa618273e10f"}, {"source": "d15a4f16fa618273e10f", "target": "5014368"}, {"source": "7b051187fe329d705ee9", "target": "3a33bfdeda5b8f8c8cd2"}, {"source": "7b051187fe329d705ee9", "target": "b603c3e0f7a74794da87"}, {"source": "3a33bfdeda5b8f8c8cd2", "target": "b603c3e0f7a74794da87"}, {"source": "bf3d285e48189507e0ea", "target": "25e2db4abc9eaf080190"}, {"source": "25e2db4abc9eaf080190", "target": "3916621"}, {"source": "c11409af47b5950f0289", "target": "f9dacd23eece9d23669c"}, {"source": "b99e416bd99fc0191b50", "target": "5b16c2a5716d346e2f34"}, {"source": "b99e416bd99fc0191b50", "target": "c11409af47b5950f0289"}, {"source": "b99e416bd99fc0191b50", "target": "f9dacd23eece9d23669c"}, {"source": "5b16c2a5716d346e2f34", "target": "2e10bf27fa0f9f49588b"}, {"source": "5b16c2a5716d346e2f34", "target": "c11409af47b5950f0289"}, {"source": "5b16c2a5716d346e2f34", "target": "f9dacd23eece9d23669c"}, {"source": "2e10bf27fa0f9f49588b", "target": "dcd01f820aa098133596"}, {"source": "2e10bf27fa0f9f49588b", "target": "c11409af47b5950f0289"}, {"source": "2e10bf27fa0f9f49588b", "target": "f9dacd23eece9d23669c"}, {"source": "dcd01f820aa098133596", "target": "8295697aee7844da2299"}, {"source": "dcd01f820aa098133596", "target": "c11409af47b5950f0289"}, {"source": "dcd01f820aa098133596", "target": "f9dacd23eece9d23669c"}, {"source": "8295697aee7844da2299", "target": "1527c2dd26a26d9ee942"}, {"source": "8295697aee7844da2299", "target": "c11409af47b5950f0289"}, {"source": "8295697aee7844da2299", "target": "f9dacd23eece9d23669c"}, {"source": "1527c2dd26a26d9ee942", "target": "c11409af47b5950f0289"}, {"source": "1527c2dd26a26d9ee942", "target": "f9dacd23eece9d23669c"}, {"source": "a3684c52fb527c8fa415", "target": "f6952f1ef900d84be918"}, {"source": "a3684c52fb527c8fa415", "target": "69808ec7ec07542ed7df"}, {"source": "f6952f1ef900d84be918", "target": "69808ec7ec07542ed7df"}, {"source": "df7f87361bb7e297e751", "target": "8e25e1434cdd6db25b69"}, {"source": "f5922ed4d0ac1ac2161f", "target": "7833311"}, {"source": "d6a92a077e78fd33316a", "target": "7ea44d0bc364b6bddbd8"}, {"source": "d6a92a077e78fd33316a", "target": "d6696d28fb47fcf8a537"}, {"source": "d6696d28fb47fcf8a537", "target": "7ea44d0bc364b6bddbd8"}, {"source": "c21840890a4790632124", "target": "157333662ef11c151080"}, {"source": "42a531c5d87bfc91036e", "target": "4062045"}, {"source": "42a531c5d87bfc91036e", "target": "7f14a531dec565ca0884"}, {"source": "42a531c5d87bfc91036e", "target": "4bd6d92fd553bb9bac11"}, {"source": "e662bb2e2c0783ee82f9", "target": "8947871"}, {"source": "e662bb2e2c0783ee82f9", "target": "5109a9fbd5f9a7230f17"}, {"source": "e662bb2e2c0783ee82f9", "target": "77cfa8eb62e34833ae7c"}, {"source": "77cfa8eb62e34833ae7c", "target": "8947871"}, {"source": "77cfa8eb62e34833ae7c", "target": "5109a9fbd5f9a7230f17"}, {"source": "4bd6d92fd553bb9bac11", "target": "4062045"}, {"source": "4bd6d92fd553bb9bac11", "target": "7f14a531dec565ca0884"}, {"source": "7b63fef8ccfbad1f851e", "target": "6ac67b0d8ed673c9aa61"}, {"source": "7b63fef8ccfbad1f851e", "target": "1d679f0322174b65d032"}, {"source": "7b63fef8ccfbad1f851e", "target": "211bd42857358a60a936"}, {"source": "211bd42857358a60a936", "target": "6ac67b0d8ed673c9aa61"}, {"source": "211bd42857358a60a936", "target": "1d679f0322174b65d032"}, {"source": "5109a9fbd5f9a7230f17", "target": "8947871"}, {"source": "1d679f0322174b65d032", "target": "6ac67b0d8ed673c9aa61"}, {"source": "20d4098ce7ed88dde5c4", "target": "f3225fa363fbaccb8c9e"}, {"source": "20d4098ce7ed88dde5c4", "target": "a74519c5a055b2903b41"}, {"source": "20d4098ce7ed88dde5c4", "target": "98cf105e8531ff12013b"}, {"source": "20d4098ce7ed88dde5c4", "target": "e7f4634cd71473c9658c"}, {"source": "20d4098ce7ed88dde5c4", "target": "f8c41743ca81d7828c3a"}, {"source": "20d4098ce7ed88dde5c4", "target": "7492cc85b1311f69ba3f"}, {"source": "20d4098ce7ed88dde5c4", "target": "02ac156db28e61ff20bb"}, {"source": "20d4098ce7ed88dde5c4", "target": "ebda1656976d90e2a636"}, {"source": "1fa21f4ccf084f6369d3", "target": "1554783"}, {"source": "27969219a945e2bd20dc", "target": "bd552e711b8325c64729"}, {"source": "c741995e777c0e089418", "target": "c1f459768d09e0c334c1"}, {"source": "2b1446f496c3968f6ecd", "target": "3943967"}, {"source": "ebda1656976d90e2a636", "target": "f3225fa363fbaccb8c9e"}, {"source": "ebda1656976d90e2a636", "target": "a74519c5a055b2903b41"}, {"source": "ebda1656976d90e2a636", "target": "98cf105e8531ff12013b"}, {"source": "ebda1656976d90e2a636", "target": "e7f4634cd71473c9658c"}, {"source": "ebda1656976d90e2a636", "target": "f8c41743ca81d7828c3a"}, {"source": "ebda1656976d90e2a636", "target": "7492cc85b1311f69ba3f"}, {"source": "ebda1656976d90e2a636", "target": "02ac156db28e61ff20bb"}, {"source": "02ac156db28e61ff20bb", "target": "f3225fa363fbaccb8c9e"}, {"source": "02ac156db28e61ff20bb", "target": "a74519c5a055b2903b41"}, {"source": "02ac156db28e61ff20bb", "target": "98cf105e8531ff12013b"}, {"source": "02ac156db28e61ff20bb", "target": "e7f4634cd71473c9658c"}, {"source": "02ac156db28e61ff20bb", "target": "f8c41743ca81d7828c3a"}, {"source": "02ac156db28e61ff20bb", "target": "7492cc85b1311f69ba3f"}, {"source": "7492cc85b1311f69ba3f", "target": "f3225fa363fbaccb8c9e"}, {"source": "7492cc85b1311f69ba3f", "target": "a74519c5a055b2903b41"}, {"source": "7492cc85b1311f69ba3f", "target": "98cf105e8531ff12013b"}, {"source": "7492cc85b1311f69ba3f", "target": "e7f4634cd71473c9658c"}, {"source": "7492cc85b1311f69ba3f", "target": "f8c41743ca81d7828c3a"}, {"source": "a9a3b146cfe63ab68a6b", "target": "618daab0fc5a3ff494d9"}, {"source": "bd552e711b8325c64729", "target": "e2744ee563f7cab80350"}, {"source": "bd552e711b8325c64729", "target": "ba569633d51ebec6ec6e"}, {"source": "bd552e711b8325c64729", "target": "eec4a6cda2f573574a11"}, {"source": "7b226e90c7338c69606b", "target": "65ae9c0fc95337107448"}, {"source": "f8c41743ca81d7828c3a", "target": "f3225fa363fbaccb8c9e"}, {"source": "f8c41743ca81d7828c3a", "target": "a74519c5a055b2903b41"}, {"source": "f8c41743ca81d7828c3a", "target": "98cf105e8531ff12013b"}, {"source": "f8c41743ca81d7828c3a", "target": "e7f4634cd71473c9658c"}, {"source": "a74519c5a055b2903b41", "target": "f3225fa363fbaccb8c9e"}, {"source": "a5df8dc94786a27d6420", "target": "782b91b09326541a0fc8"}, {"source": "f80fdbc2c03dbc1ae4e8", "target": "e285b8e4c7b84710e463"}, {"source": "f80fdbc2c03dbc1ae4e8", "target": "a7c02c493896a33d5f37"}, {"source": "a7c02c493896a33d5f37", "target": "e285b8e4c7b84710e463"}, {"source": "0d95074b6d83a77dc3ad", "target": "3887051"}, {"source": "0d95074b6d83a77dc3ad", "target": "5577023"}, {"source": "be2abfb3155a38be4de4", "target": "1377729"}, {"source": "be2abfb3155a38be4de4", "target": "5653692"}, {"source": "be2abfb3155a38be4de4", "target": "950642"}, {"source": "93c5c349be14f459c98a", "target": "4679202"}, {"source": "b57f4cc89dacd38fcdcd", "target": "2fffa9abe50ac97603c7"}, {"source": "2fffa9abe50ac97603c7", "target": "b6bdb82045c2aa8225f5"}, {"source": "bc38c32d24e6fba4db84", "target": "1308257"}, {"source": "c88435cb568dcaf11308", "target": "6746848"}, {"source": "c8ef9d67acc044b368ef", "target": "298e07ea67a640b5d9f4"}, {"source": "8e73290130d9e25c6d9c", "target": "d8b1c5f7bc975de83d99"}, {"source": "1a75ec54489368b514f8", "target": "7d5925cb7d9721c60360"}, {"source": "1a75ec54489368b514f8", "target": "540b1b884b80b14a44c6"}, {"source": "1a75ec54489368b514f8", "target": "905bb4f399e0855e08fe"}, {"source": "e6f72fbca42e77be0eb3", "target": "f8c0220c54ec8347ea95"}, {"source": "e6f72fbca42e77be0eb3", "target": "aea3c7fc1acc0eca4d44"}, {"source": "aea3c7fc1acc0eca4d44", "target": "f8c0220c54ec8347ea95"}, {"source": "905bb4f399e0855e08fe", "target": "433a9b04f82a1c78af4b"}, {"source": "0ad500de10f9edfe5d15", "target": "895eb781d88148cfd8e5"}, {"source": "172a6a53ddbce792b10d", "target": "23137b412caf6e80b34a"}, {"source": "dcfcc3887ff56a9e1928", "target": "7e8e57368175a1433791"}, {"source": "13fa3e31fed7834eee10", "target": "9265674"}, {"source": "6a83de20f780b42fadb9", "target": "3b57a788a4b96e1af211"}, {"source": "c89a76096c070a5a7d23", "target": "9643738d5f79c7b594d0"}, {"source": "914d024adf10bface11a", "target": "4339184"}, {"source": "2af6e6e47935190936b1", "target": "3885304"}, {"source": "da8e72b5e680c827d6dd", "target": "8027637"}, {"source": "da8e72b5e680c827d6dd", "target": "8027835"}, {"source": "da8e72b5e680c827d6dd", "target": "06af5f439f24bf28aec0"}, {"source": "da8e72b5e680c827d6dd", "target": "06aa7e108b77745f1f24"}, {"source": "06aa7e108b77745f1f24", "target": "8027637"}, {"source": "06aa7e108b77745f1f24", "target": "8027835"}, {"source": "06aa7e108b77745f1f24", "target": "06af5f439f24bf28aec0"}, {"source": "06af5f439f24bf28aec0", "target": "8027637"}, {"source": "06af5f439f24bf28aec0", "target": "8027835"}, {"source": "3dfee2b74943398f0550", "target": "98129315d0f7df3b53e3"}, {"source": "7e473c251e68ce760e2a", "target": "98129315d0f7df3b53e3"}, {"source": "98129315d0f7df3b53e3", "target": "3887118"}, {"source": "f0b021b0a56fee82dd97", "target": "5fb51700f991e4101773"}, {"source": "f0b021b0a56fee82dd97", "target": "8822395"}, {"source": "5fb51700f991e4101773", "target": "8822395"}, {"source": "d7cbf8fd96ce42fed36d", "target": "3db48ae4c663ca028a6f"}, {"source": "d7cbf8fd96ce42fed36d", "target": "1405439"}, {"source": "a8aa67de3cd9f2e0554e", "target": "8260510"}, {"source": "833632f23c308ae2d58b", "target": "8f96bf393b94caff688b"}, {"source": "833632f23c308ae2d58b", "target": "3424eed86f55550622ef"}, {"source": "6210bd80bdd20181e1f4", "target": "e03a1a5c1a4a95f06c3b"}, {"source": "6210bd80bdd20181e1f4", "target": "79f16392f7e1bed77f07"}, {"source": "e03a1a5c1a4a95f06c3b", "target": "8f96bf393b94caff688b"}, {"source": "1036b853c3f52b8994a4", "target": "6521802"}, {"source": "1036b853c3f52b8994a4", "target": "0c991d6ed0e994e1c7ed"}, {"source": "1036b853c3f52b8994a4", "target": "ce638f8bd5e70cb809e1"}, {"source": "0c991d6ed0e994e1c7ed", "target": "ce638f8bd5e70cb809e1"}, {"source": "0c991d6ed0e994e1c7ed", "target": "cd8e12c7a18495980bd0"}, {"source": "0c991d6ed0e994e1c7ed", "target": "8272715"}, {"source": "ce638f8bd5e70cb809e1", "target": "6521802"}, {"source": "8ae3fd45fca0f18372d4", "target": "d8c76dfc03589e6dbf1f"}, {"source": "4afb54600dc9328d14bd", "target": "6521802"}, {"source": "bf5c4ea014d0b91f1f04", "target": "d3fd26207d7468b6c514"}, {"source": "5c5078b3cc3e54e4fec2", "target": "4063269"}, {"source": "3424eed86f55550622ef", "target": "2d502924bad6152acbe5"}, {"source": "564fcb398fdad08442c1", "target": "a50ccec1c202731fa8f1"}, {"source": "564fcb398fdad08442c1", "target": "230ff9a14751b8f3092d"}, {"source": "a50ccec1c202731fa8f1", "target": "a161bdb59b9dcec9a32e"}, {"source": "d5fc7956ba6896330ea1", "target": "a161bdb59b9dcec9a32e"}, {"source": "a161bdb59b9dcec9a32e", "target": "70da709633904c6de9d9"}, {"source": "a161bdb59b9dcec9a32e", "target": "16933e96f51571fbbcd6"}, {"source": "a161bdb59b9dcec9a32e", "target": "8543484"}, {"source": "dcce9b645783d5239a04", "target": "8968230"}, {"source": "dcce9b645783d5239a04", "target": "bffd66a05d5bf04aefa7"}, {"source": "dcce9b645783d5239a04", "target": "6514960"}, {"source": "bffd66a05d5bf04aefa7", "target": "6514960"}, {"source": "bffd66a05d5bf04aefa7", "target": "e2af207bc46782aa460c"}, {"source": "bffd66a05d5bf04aefa7", "target": "8784942"}, {"source": "bffd66a05d5bf04aefa7", "target": "8b9e134ca8bae13bb470"}, {"source": "e2af207bc46782aa460c", "target": "6514960"}, {"source": "c3d1662948a049fc80dd", "target": "8b9e134ca8bae13bb470"}, {"source": "a71ff1c9d6e163e2faea", "target": "5171361"}, {"source": "a71ff1c9d6e163e2faea", "target": "4360c8ac1dcaee373113"}, {"source": "7b816a50e66bb62208a7", "target": "14e2e4e14898f13e13c7"}, {"source": "0444bdd92c1f968c75ef", "target": "f5204d3d85bdaa1fd6ea"}, {"source": "f5204d3d85bdaa1fd6ea", "target": "baf7dd8d481d83b7f37e"}, {"source": "2fe464efd794bde5ed68", "target": "2d12a584a14910032ab8"}, {"source": "2bab520665d975780931", "target": "2d12a584a14910032ab8"}, {"source": "2d12a584a14910032ab8", "target": "3055104"}, {"source": "de262983e3ba0c0f89c1", "target": "6ac67b0d8ed673c9aa61"}, {"source": "de262983e3ba0c0f89c1", "target": "1d679f0322174b65d032"}, {"source": "51ccb8b4f111ef4aec1c", "target": "4b6ab7914183dbf7a16f"}, {"source": "51ccb8b4f111ef4aec1c", "target": "3b5f766647e15e23e5b8"}, {"source": "56aa8232fb56247094ae", "target": "3885304"}, {"source": "3b5f766647e15e23e5b8", "target": "4b6ab7914183dbf7a16f"}, {"source": "3ef5a223bdb760a228b4", "target": "671c0e7adfb1ce7060b8"}, {"source": "8ba86c4fded2c67b34c7", "target": "5cce4b8ca41650f14bb4"}, {"source": "8ba86c4fded2c67b34c7", "target": "515f713d41186e18e384"}, {"source": "8ba86c4fded2c67b34c7", "target": "680375cb6a66760ea011"}, {"source": "680375cb6a66760ea011", "target": "5cce4b8ca41650f14bb4"}, {"source": "680375cb6a66760ea011", "target": "515f713d41186e18e384"}, {"source": "515f713d41186e18e384", "target": "5cce4b8ca41650f14bb4"}, {"source": "c5f71b45dbd87402e6f2", "target": "98f56f3e840143a4eccf"}, {"source": "0102759ebcb977f2a98b", "target": "4063326"}, {"source": "4b42493b70dbc9e1ce02", "target": "de262983e3ba0c0f89c1"}, {"source": "4b42493b70dbc9e1ce02", "target": "0d2e658691a4f93cad92"}, {"source": "a8d01671b04b326d36de", "target": "4282586"}, {"source": "bb29b599bce91048ec40", "target": "6f26d011aebc4c75272b"}, {"source": "cc818fff7a1049cc0efe", "target": "7ea44d0bc364b6bddbd8"}, {"source": "cc818fff7a1049cc0efe", "target": "d6696d28fb47fcf8a537"}, {"source": "cc818fff7a1049cc0efe", "target": "d6a92a077e78fd33316a"}, {"source": "92248ec5049ba86bf125", "target": "29cc3cc4078091fd2115"}, {"source": "2ceaea7f0a4e37ca5a5d", "target": "5cce4b8ca41650f14bb4"}, {"source": "2ceaea7f0a4e37ca5a5d", "target": "515f713d41186e18e384"}, {"source": "2ceaea7f0a4e37ca5a5d", "target": "680375cb6a66760ea011"}, {"source": "2ceaea7f0a4e37ca5a5d", "target": "8ba86c4fded2c67b34c7"}, {"source": "58457ea613b84f7544b1", "target": "53c6d59ea70afaf19cf1"}, {"source": "3cda05ca68cba260cb81", "target": "5c136de85de1c2abb6fc"}, {"source": "00e6248efe5605c1fa5c", "target": "07ed66cc6fc394d6ea53"}, {"source": "db24422abdb20150a9dd", "target": "d7e4f9199c15d71258b5"}, {"source": "db24422abdb20150a9dd", "target": "667e4df76848e72f250b"}, {"source": "667e4df76848e72f250b", "target": "5a801d9153059b226bbd"}, {"source": "0d2e658691a4f93cad92", "target": "de262983e3ba0c0f89c1"}, {"source": "89de858deaf6f8fc78ba", "target": "80097d54d4ec4ce83222"}, {"source": "40f0f319001394b5f8af", "target": "34d2ce39d3b8c2f8a577"}, {"source": "40f0f319001394b5f8af", "target": "19a3f0afed794cf720b3"}, {"source": "40f0f319001394b5f8af", "target": "f1a875885437608d17e8"}, {"source": "f1a875885437608d17e8", "target": "34d2ce39d3b8c2f8a577"}, {"source": "f1a875885437608d17e8", "target": "19a3f0afed794cf720b3"}, {"source": "f1a875885437608d17e8", "target": "e025a50dbaae7186e516"}, {"source": "d0a42741ce0cf6cdc3ab", "target": "11b02f8917fac66d6fe5"}, {"source": "d0a42741ce0cf6cdc3ab", "target": "e025a50dbaae7186e516"}, {"source": "e025a50dbaae7186e516", "target": "40d5e61963abf20da067"}, {"source": "e025a50dbaae7186e516", "target": "11b02f8917fac66d6fe5"}, {"source": "e025a50dbaae7186e516", "target": "be3744cfe1b318bf4035"}, {"source": "6567fb027971588bc779", "target": "3757119"}, {"source": "6567fb027971588bc779", "target": "14e29858efdd19253787"}, {"source": "be3744cfe1b318bf4035", "target": "40d5e61963abf20da067"}, {"source": "be3744cfe1b318bf4035", "target": "11b02f8917fac66d6fe5"}, {"source": "be4a45ec74357e7d9b10", "target": "1da93bab4cdc708f41ae"}, {"source": "daf6bc9db8b0a28e3973", "target": "1da93bab4cdc708f41ae"}, {"source": "11b02f8917fac66d6fe5", "target": "40d5e61963abf20da067"}, {"source": "40d5e61963abf20da067", "target": "94f1376b946c9d217014"}, {"source": "94f1376b946c9d217014", "target": "1c23d9c7070deb6f8488"}, {"source": "1c23d9c7070deb6f8488", "target": "950cbe78b4c307fa14a1"}, {"source": "a08a1080b88344b0c8a7", "target": "9e04ccfebeb84bcdc76c"}, {"source": "92abca0ebac031d77521", "target": "666179ad5587254694bc"}, {"source": "92abca0ebac031d77521", "target": "010ffad3577afec10ef6"}, {"source": "010ffad3577afec10ef6", "target": "666179ad5587254694bc"}, {"source": "e749224c89f82788cb18", "target": "9673c96a682fe3948379"}, {"source": "e749224c89f82788cb18", "target": "21f99959d48dd0d0c746"}, {"source": "9673c96a682fe3948379", "target": "21f99959d48dd0d0c746"}, {"source": "f372f1555d1ab41dadd1", "target": "211bd42857358a60a936"}, {"source": "f372f1555d1ab41dadd1", "target": "4403522"}, {"source": "f372f1555d1ab41dadd1", "target": "3176159"}, {"source": "f372f1555d1ab41dadd1", "target": "3886208"}, {"source": "835c17a94abade5c2ca1", "target": "0055d87cfaecc5b06cd2"}, {"source": "0055d87cfaecc5b06cd2", "target": "4a7f517bec51adc6e7bc"}, {"source": "1919bd8c2f574caa17ba", "target": "2206590"}, {"source": "1919bd8c2f574caa17ba", "target": "29cc3cc4078091fd2115"}, {"source": "78b10a3836254f8b6d8e", "target": "29cc3cc4078091fd2115"}, {"source": "78b10a3836254f8b6d8e", "target": "92248ec5049ba86bf125"}, {"source": "2aead353c3446ee9acca", "target": "5cce4b8ca41650f14bb4"}, {"source": "2aead353c3446ee9acca", "target": "515f713d41186e18e384"}, {"source": "2aead353c3446ee9acca", "target": "680375cb6a66760ea011"}, {"source": "2aead353c3446ee9acca", "target": "8ba86c4fded2c67b34c7"}, {"source": "2aead353c3446ee9acca", "target": "2ceaea7f0a4e37ca5a5d"}, {"source": "8a173cfcb9171627c7f1", "target": "6ac67b0d8ed673c9aa61"}, {"source": "8a173cfcb9171627c7f1", "target": "f2f6883ac7c965d09b90"}, {"source": "8a173cfcb9171627c7f1", "target": "1da93bab4cdc708f41ae"}, {"source": "8a173cfcb9171627c7f1", "target": "daf6bc9db8b0a28e3973"}, {"source": "8a173cfcb9171627c7f1", "target": "be4a45ec74357e7d9b10"}, {"source": "8a173cfcb9171627c7f1", "target": "db1e524cae5e4344b2e6"}, {"source": "b66a37afe6581eb5bc0c", "target": "6daa5503fc60d4a89a11"}, {"source": "a61e208c37ea177cf83a", "target": "15f52d2ec0b035b53c6f"}, {"source": "5a801d9153059b226bbd", "target": "b6e4ec3859b2f732dfc0"}, {"source": "8a7f9bed9715c0eb678a", "target": "7ef773bdf11999c9eebb"}, {"source": "d1c32b2e77120f560061", "target": "230ff9a14751b8f3092d"}, {"source": "018e79e4b62e86dfd988", "target": "66eee134d6fd3babb716"}, {"source": "00336c9eb59ee956426d", "target": "2920551"}, {"source": "23b5bc37d39c9f9dd5e7", "target": "ea64575d70ecf3354874"}, {"source": "23b5bc37d39c9f9dd5e7", "target": "b5df5023965f65719d7a"}, {"source": "23b5bc37d39c9f9dd5e7", "target": "93297e55e7535dd833a1"}, {"source": "93297e55e7535dd833a1", "target": "ea64575d70ecf3354874"}, {"source": "93297e55e7535dd833a1", "target": "b5df5023965f65719d7a"}, {"source": "b956ee7215b7b50bc78a", "target": "4248145"}, {"source": "3e46da46186e1dc1a02e", "target": "3885304"}, {"source": "9fc66f76d0217475f2e4", "target": "f8e9ae0a4c101d978f84"}, {"source": "fcef0e6dac231ef2e54b", "target": "9358409"}, {"source": "fcef0e6dac231ef2e54b", "target": "61a4bd0cfc373cf08884"}, {"source": "fcef0e6dac231ef2e54b", "target": "1db896c1a38d89ae71b4"}, {"source": "1db896c1a38d89ae71b4", "target": "9358409"}, {"source": "1db896c1a38d89ae71b4", "target": "61a4bd0cfc373cf08884"}, {"source": "61a4bd0cfc373cf08884", "target": "9358409"}, {"source": "6bd2b990bea368bd6358", "target": "3d66eaf6306fe3b279e6"}, {"source": "6bd2b990bea368bd6358", "target": "e52b02edb554747370c5"}, {"source": "e52b02edb554747370c5", "target": "3d66eaf6306fe3b279e6"}, {"source": "5f168b5c884b1f9c36a5", "target": "3886208"}, {"source": "5f168b5c884b1f9c36a5", "target": "8325617"}, {"source": "3601e88d5317ab2be0e8", "target": "3885304"}, {"source": "294eae4fec5e31d0390d", "target": "280d83080497c8c13152"}, {"source": "294eae4fec5e31d0390d", "target": "487ef139e7025406c4c1"}, {"source": "487ef139e7025406c4c1", "target": "280d83080497c8c13152"}, {"source": "4506b5bf37fab13586f7", "target": "1667367"}, {"source": "a3b5571be92a0513b2f9", "target": "4062085"}, {"source": "a3b5571be92a0513b2f9", "target": "ca5eb04b731b7676104a"}, {"source": "a3b5571be92a0513b2f9", "target": "e8ea925f7c61b353f694"}, {"source": "a3b5571be92a0513b2f9", "target": "47ec2268b4e1edfda9d7"}, {"source": "47ec2268b4e1edfda9d7", "target": "4062085"}, {"source": "47ec2268b4e1edfda9d7", "target": "ca5eb04b731b7676104a"}, {"source": "47ec2268b4e1edfda9d7", "target": "e8ea925f7c61b353f694"}, {"source": "e8ea925f7c61b353f694", "target": "4062085"}, {"source": "e8ea925f7c61b353f694", "target": "ca5eb04b731b7676104a"}, {"source": "ca5eb04b731b7676104a", "target": "4062085"}, {"source": "fa6292523b1680ecbb15", "target": "308aa0fb4aeb2771b6cc"}, {"source": "fe31fe34099bbb41e0a7", "target": "3885304"}, {"source": "e266096cad90266aa85e", "target": "3885304"}, {"source": "e266096cad90266aa85e", "target": "6b62ef1ddf6523b1f0fd"}, {"source": "6b62ef1ddf6523b1f0fd", "target": "3885304"}, {"source": "f641200424b306c4b724", "target": "3885304"}, {"source": "f641200424b306c4b724", "target": "df9af361ca4b8f34aec9"}, {"source": "df9af361ca4b8f34aec9", "target": "3885304"}, {"source": "f2e095a4f77bc51b6010", "target": "8d7bad2d954ee09ea347"}, {"source": "f2e095a4f77bc51b6010", "target": "d1f3811a4964d484017c"}, {"source": "f2e095a4f77bc51b6010", "target": "262b5344222f227dd9d3"}, {"source": "f2e095a4f77bc51b6010", "target": "f633095702b6f478cc12"}, {"source": "f633095702b6f478cc12", "target": "8d7bad2d954ee09ea347"}, {"source": "f633095702b6f478cc12", "target": "d1f3811a4964d484017c"}, {"source": "f633095702b6f478cc12", "target": "262b5344222f227dd9d3"}, {"source": "262b5344222f227dd9d3", "target": "8d7bad2d954ee09ea347"}, {"source": "262b5344222f227dd9d3", "target": "d1f3811a4964d484017c"}, {"source": "d1f3811a4964d484017c", "target": "8d7bad2d954ee09ea347"}, {"source": "2ed04b7552f1ef9f8e0c", "target": "3885304"}, {"source": "2ed04b7552f1ef9f8e0c", "target": "9e3f368898e5fca662f2"}, {"source": "9e3f368898e5fca662f2", "target": "3885304"}, {"source": "aa2b8867d2f13ead838b", "target": "3885304"}, {"source": "75878caf6912fe6bfe85", "target": "3885304"}, {"source": "75878caf6912fe6bfe85", "target": "c5d9a583437b19493ba3"}, {"source": "75878caf6912fe6bfe85", "target": "a7ef0b8473773b3ccda1"}, {"source": "75878caf6912fe6bfe85", "target": "a2db15a154b360c591c7"}, {"source": "a2db15a154b360c591c7", "target": "3885304"}, {"source": "a2db15a154b360c591c7", "target": "c5d9a583437b19493ba3"}, {"source": "a2db15a154b360c591c7", "target": "a7ef0b8473773b3ccda1"}, {"source": "a7ef0b8473773b3ccda1", "target": "3885304"}, {"source": "a7ef0b8473773b3ccda1", "target": "c5d9a583437b19493ba3"}, {"source": "c5d9a583437b19493ba3", "target": "3885304"}, {"source": "4082de18058c055efe84", "target": "3885304"}, {"source": "1733c62207e0bf614f06", "target": "3885304"}, {"source": "8b896db8aac59dac7a37", "target": "f37b07b92633781a46f7"}, {"source": "8b896db8aac59dac7a37", "target": "9b16c3815f461d8c0817"}, {"source": "9b16c3815f461d8c0817", "target": "f37b07b92633781a46f7"}, {"source": "3ef8e84b6a3482c9efa9", "target": "c32d1c61d0d09dcda690"}, {"source": "3ef8e84b6a3482c9efa9", "target": "38cd48fe454b017122aa"}, {"source": "38cd48fe454b017122aa", "target": "c32d1c61d0d09dcda690"}, {"source": "c23d14a90a18c0ba9498", "target": "3885304"}, {"source": "c23d14a90a18c0ba9498", "target": "33a6d86c5036e61549b5"}, {"source": "c23d14a90a18c0ba9498", "target": "eca6a6ab132db0eb3278"}, {"source": "eca6a6ab132db0eb3278", "target": "3885304"}, {"source": "eca6a6ab132db0eb3278", "target": "33a6d86c5036e61549b5"}, {"source": "33a6d86c5036e61549b5", "target": "3885304"}, {"source": "07cbeabf93c7ece31c2d", "target": "3885304"}, {"source": "07cbeabf93c7ece31c2d", "target": "5cebc9137ffcd6271071"}, {"source": "07cbeabf93c7ece31c2d", "target": "59ad8df8112637667982"}, {"source": "59ad8df8112637667982", "target": "3885304"}, {"source": "59ad8df8112637667982", "target": "5cebc9137ffcd6271071"}, {"source": "0a4fc38df5249e1614a1", "target": "3885304"}, {"source": "8ed968fc7b4ccb04630c", "target": "3885304"}, {"source": "01deb69af82797bd8738", "target": "3885304"}, {"source": "01deb69af82797bd8738", "target": "18c2975af798328a528f"}, {"source": "18c2975af798328a528f", "target": "3885304"}, {"source": "99df29f258860e53c5e2", "target": "3878819"}, {"source": "99df29f258860e53c5e2", "target": "1014829"}, {"source": "99df29f258860e53c5e2", "target": "1238376"}, {"source": "db57b9e8546b4b83a405", "target": "66474fc2225415d1ebd3"}, {"source": "db57b9e8546b4b83a405", "target": "ea0a8c361ba849711628"}, {"source": "db57b9e8546b4b83a405", "target": "671c0e7adfb1ce7060b8"}, {"source": "ea0a8c361ba849711628", "target": "66474fc2225415d1ebd3"}, {"source": "ea0a8c361ba849711628", "target": "4f068e1a8833a7d2f2fb"}, {"source": "ea0a8c361ba849711628", "target": "671c0e7adfb1ce7060b8"}, {"source": "4f068e1a8833a7d2f2fb", "target": "4248145"}, {"source": "66474fc2225415d1ebd3", "target": "3f6e737fd8e50f6cbda2"}, {"source": "66474fc2225415d1ebd3", "target": "671c0e7adfb1ce7060b8"}, {"source": "3f6e737fd8e50f6cbda2", "target": "42b1361b49ed2cc4e0ae"}, {"source": "3f6e737fd8e50f6cbda2", "target": "671c0e7adfb1ce7060b8"}, {"source": "42b1361b49ed2cc4e0ae", "target": "3ef5a223bdb760a228b4"}, {"source": "42b1361b49ed2cc4e0ae", "target": "671c0e7adfb1ce7060b8"}, {"source": "4ee4c58c9dfc2a41ed26", "target": "4248145"}, {"source": "26c887df79dc869059ba", "target": "8a173cfcb9171627c7f1"}, {"source": "26c887df79dc869059ba", "target": "037488ed37f0e1cbfe32"}, {"source": "26c887df79dc869059ba", "target": "6420534"}, {"source": "b1cd9a8ae8f2f9d67ea9", "target": "0cfe97cda6ed02d49591"}, {"source": "4b86c29e1669b75653de", "target": "66d926fe73211fd650ec"}, {"source": "9bae6529ccab4b902a50", "target": "a35687613ab0f4991f37"}, {"source": "c4a8380f6b3a5ae6668d", "target": "1582075"}, {"source": "57f23fbddd23a0386030", "target": "4360892"}, {"source": "41b50ea8bb206e44fec2", "target": "a0d2efed63aeefce5cb0"}, {"source": "b3f5eb034f440cfb6809", "target": "a003bf90d75a54c7ccb6"}, {"source": "f56bb7f0e3c73358b752", "target": "0b4b038fd4dd3a046168"}, {"source": "f56bb7f0e3c73358b752", "target": "ddc9632a718e306b6e71"}, {"source": "f56bb7f0e3c73358b752", "target": "3f0e150ff6a86838f810"}, {"source": "f3708ba3311007a9b4a6", "target": "8033015"}, {"source": "6783e81b15f3205e9ff2", "target": "162e96df868aa79167ac"}, {"source": "f25a996484a0588cb8c2", "target": "542e6e08a2a9f7c2ebac"}, {"source": "f25a996484a0588cb8c2", "target": "2f2183747543c804b8ca"}, {"source": "f25a996484a0588cb8c2", "target": "9d62b7a2c4335a0fd1e6"}, {"source": "9d62b7a2c4335a0fd1e6", "target": "542e6e08a2a9f7c2ebac"}, {"source": "9d62b7a2c4335a0fd1e6", "target": "2f2183747543c804b8ca"}, {"source": "2f2183747543c804b8ca", "target": "542e6e08a2a9f7c2ebac"}, {"source": "9154d57067918cdb4cf5", "target": "3969722"}, {"source": "b84846a0511acfeaf925", "target": "3231298"}, {"source": "744228c927ee95871559", "target": "1440766"}, {"source": "744228c927ee95871559", "target": "8ad986db8b3f5cc82e69"}, {"source": "810749b7dbcf0eca1e4e", "target": "3887051"}, {"source": "b887532acfb8b001940d", "target": "341ed17c343e2520bde6"}, {"source": "b887532acfb8b001940d", "target": "04a00e72a27edafa5ab8"}, {"source": "b887532acfb8b001940d", "target": "3c280b21451e269ba759"}, {"source": "b887532acfb8b001940d", "target": "06972c1773b30ccdb2d8"}, {"source": "b887532acfb8b001940d", "target": "4c45db68b16ff90254de"}, {"source": "b887532acfb8b001940d", "target": "77428cb3e0875e9683d9"}, {"source": "b887532acfb8b001940d", "target": "4dbe405dc614fd08ad77"}, {"source": "b887532acfb8b001940d", "target": "b3dbd57c3ec465a3d925"}, {"source": "b887532acfb8b001940d", "target": "5df8dd084a93ad878cfc"}, {"source": "b887532acfb8b001940d", "target": "bb56b979c5a6117b334b"}, {"source": "bb56b979c5a6117b334b", "target": "341ed17c343e2520bde6"}, {"source": "bb56b979c5a6117b334b", "target": "04a00e72a27edafa5ab8"}, {"source": "bb56b979c5a6117b334b", "target": "3c280b21451e269ba759"}, {"source": "bb56b979c5a6117b334b", "target": "06972c1773b30ccdb2d8"}, {"source": "bb56b979c5a6117b334b", "target": "4c45db68b16ff90254de"}, {"source": "bb56b979c5a6117b334b", "target": "77428cb3e0875e9683d9"}, {"source": "bb56b979c5a6117b334b", "target": "4dbe405dc614fd08ad77"}, {"source": "bb56b979c5a6117b334b", "target": "b3dbd57c3ec465a3d925"}, {"source": "bb56b979c5a6117b334b", "target": "5df8dd084a93ad878cfc"}, {"source": "5df8dd084a93ad878cfc", "target": "341ed17c343e2520bde6"}, {"source": "5df8dd084a93ad878cfc", "target": "04a00e72a27edafa5ab8"}, {"source": "5df8dd084a93ad878cfc", "target": "3c280b21451e269ba759"}, {"source": "5df8dd084a93ad878cfc", "target": "06972c1773b30ccdb2d8"}, {"source": "5df8dd084a93ad878cfc", "target": "4c45db68b16ff90254de"}, {"source": "5df8dd084a93ad878cfc", "target": "77428cb3e0875e9683d9"}, {"source": "5df8dd084a93ad878cfc", "target": "4dbe405dc614fd08ad77"}, {"source": "5df8dd084a93ad878cfc", "target": "b3dbd57c3ec465a3d925"}, {"source": "b3dbd57c3ec465a3d925", "target": "341ed17c343e2520bde6"}, {"source": "b3dbd57c3ec465a3d925", "target": "04a00e72a27edafa5ab8"}, {"source": "b3dbd57c3ec465a3d925", "target": "3c280b21451e269ba759"}, {"source": "b3dbd57c3ec465a3d925", "target": "06972c1773b30ccdb2d8"}, {"source": "b3dbd57c3ec465a3d925", "target": "4c45db68b16ff90254de"}, {"source": "b3dbd57c3ec465a3d925", "target": "77428cb3e0875e9683d9"}, {"source": "b3dbd57c3ec465a3d925", "target": "4dbe405dc614fd08ad77"}, {"source": "4dbe405dc614fd08ad77", "target": "341ed17c343e2520bde6"}, {"source": "4dbe405dc614fd08ad77", "target": "04a00e72a27edafa5ab8"}, {"source": "4dbe405dc614fd08ad77", "target": "3c280b21451e269ba759"}, {"source": "4dbe405dc614fd08ad77", "target": "06972c1773b30ccdb2d8"}, {"source": "4dbe405dc614fd08ad77", "target": "4c45db68b16ff90254de"}, {"source": "4dbe405dc614fd08ad77", "target": "77428cb3e0875e9683d9"}, {"source": "77428cb3e0875e9683d9", "target": "341ed17c343e2520bde6"}, {"source": "77428cb3e0875e9683d9", "target": "04a00e72a27edafa5ab8"}, {"source": "77428cb3e0875e9683d9", "target": "3c280b21451e269ba759"}, {"source": "77428cb3e0875e9683d9", "target": "06972c1773b30ccdb2d8"}, {"source": "77428cb3e0875e9683d9", "target": "4c45db68b16ff90254de"}, {"source": "4c45db68b16ff90254de", "target": "341ed17c343e2520bde6"}, {"source": "4c45db68b16ff90254de", "target": "04a00e72a27edafa5ab8"}, {"source": "4c45db68b16ff90254de", "target": "3c280b21451e269ba759"}, {"source": "4c45db68b16ff90254de", "target": "06972c1773b30ccdb2d8"}, {"source": "06972c1773b30ccdb2d8", "target": "341ed17c343e2520bde6"}, {"source": "06972c1773b30ccdb2d8", "target": "04a00e72a27edafa5ab8"}, {"source": "06972c1773b30ccdb2d8", "target": "3c280b21451e269ba759"}, {"source": "3c280b21451e269ba759", "target": "341ed17c343e2520bde6"}, {"source": "3c280b21451e269ba759", "target": "04a00e72a27edafa5ab8"}, {"source": "04a00e72a27edafa5ab8", "target": "341ed17c343e2520bde6"}, {"source": "f73384946e17e4b1b46a", "target": "52239ed074878b9a60ca"}, {"source": "52239ed074878b9a60ca", "target": "2f93011f91b398b936be"}, {"source": "2481e1f3b1d92002317e", "target": "9499896"}, {"source": "c0da0fb4a6c9e4c393de", "target": "a9e99b949d0d9b1e6b85"}, {"source": "c0da0fb4a6c9e4c393de", "target": "c5e83b964f5cf7c5edda"}, {"source": "c5e83b964f5cf7c5edda", "target": "a9e99b949d0d9b1e6b85"}, {"source": "09bc0b1f7512757ad152", "target": "b1014a71757ce72444e1"}, {"source": "09bc0b1f7512757ad152", "target": "34784700eb8fb594f17f"}, {"source": "09bc0b1f7512757ad152", "target": "9eea6a43904b1a158e92"}, {"source": "55d327542393530662c3", "target": "d0a42741ce0cf6cdc3ab"}, {"source": "55d327542393530662c3", "target": "40d5e61963abf20da067"}, {"source": "55d327542393530662c3", "target": "60a1ba782ce09cf2091e"}, {"source": "55d327542393530662c3", "target": "3757119"}, {"source": "55d327542393530662c3", "target": "6567fb027971588bc779"}, {"source": "38845ef1d0c11b40dfd8", "target": "94f1376b946c9d217014"}, {"source": "38845ef1d0c11b40dfd8", "target": "60a1ba782ce09cf2091e"}, {"source": "60a1ba782ce09cf2091e", "target": "94f1376b946c9d217014"}, {"source": "851f506aac452bf962d3", "target": "8838736cfa6f3f960e2c"}, {"source": "8838736cfa6f3f960e2c", "target": "430efaf8f592d018bd3e"}, {"source": "430efaf8f592d018bd3e", "target": "a08a1080b88344b0c8a7"}, {"source": "9938078a93a4ba380a0e", "target": "014b461f460cf8fa3b77"}, {"source": "014b461f460cf8fa3b77", "target": "1af08ad6cdb01707c33f"}, {"source": "014b461f460cf8fa3b77", "target": "46050d18d5ec1ab401fa"}, {"source": "9eea6a43904b1a158e92", "target": "b1014a71757ce72444e1"}, {"source": "9eea6a43904b1a158e92", "target": "34784700eb8fb594f17f"}, {"source": "34784700eb8fb594f17f", "target": "3883195"}, {"source": "34784700eb8fb594f17f", "target": "e05c76ab9450cbc214ac"}, {"source": "b1014a71757ce72444e1", "target": "e05c76ab9450cbc214ac"}, {"source": "e6a1a8b53e87cadd00aa", "target": "1297670a35e6e0841636"}, {"source": "b50089030f88e5f58c07", "target": "5446416"}, {"source": "131ad62a0ef5a8e6968b", "target": "a3ee04578325668bd3f8"}, {"source": "131ad62a0ef5a8e6968b", "target": "5a4ab3ca8b3b7b57d234"}, {"source": "131ad62a0ef5a8e6968b", "target": "0f7abbfc6ab01513d89a"}, {"source": "131ad62a0ef5a8e6968b", "target": "d9ad0499316f09c598a3"}, {"source": "131ad62a0ef5a8e6968b", "target": "2a7a31815153d26b39f6"}, {"source": "131ad62a0ef5a8e6968b", "target": "69c42b901d0ed52d480a"}, {"source": "131ad62a0ef5a8e6968b", "target": "3bb8a779948659a5b101"}, {"source": "131ad62a0ef5a8e6968b", "target": "07bbe91f368e5ce0b180"}, {"source": "131ad62a0ef5a8e6968b", "target": "a5e1b098533228613f28"}, {"source": "131ad62a0ef5a8e6968b", "target": "de4a9e0010795ace76b8"}, {"source": "a3ee04578325668bd3f8", "target": "5a4ab3ca8b3b7b57d234"}, {"source": "a3ee04578325668bd3f8", "target": "0f7abbfc6ab01513d89a"}, {"source": "a3ee04578325668bd3f8", "target": "d9ad0499316f09c598a3"}, {"source": "a3ee04578325668bd3f8", "target": "2a7a31815153d26b39f6"}, {"source": "a3ee04578325668bd3f8", "target": "69c42b901d0ed52d480a"}, {"source": "a3ee04578325668bd3f8", "target": "3bb8a779948659a5b101"}, {"source": "a3ee04578325668bd3f8", "target": "07bbe91f368e5ce0b180"}, {"source": "a3ee04578325668bd3f8", "target": "a5e1b098533228613f28"}, {"source": "a3ee04578325668bd3f8", "target": "de4a9e0010795ace76b8"}, {"source": "69c42b901d0ed52d480a", "target": "5a4ab3ca8b3b7b57d234"}, {"source": "69c42b901d0ed52d480a", "target": "0f7abbfc6ab01513d89a"}, {"source": "69c42b901d0ed52d480a", "target": "d9ad0499316f09c598a3"}, {"source": "69c42b901d0ed52d480a", "target": "2a7a31815153d26b39f6"}, {"source": "69c42b901d0ed52d480a", "target": "3bb8a779948659a5b101"}, {"source": "69c42b901d0ed52d480a", "target": "07bbe91f368e5ce0b180"}, {"source": "69c42b901d0ed52d480a", "target": "a5e1b098533228613f28"}, {"source": "69c42b901d0ed52d480a", "target": "de4a9e0010795ace76b8"}, {"source": "2a7a31815153d26b39f6", "target": "5a4ab3ca8b3b7b57d234"}, {"source": "2a7a31815153d26b39f6", "target": "0f7abbfc6ab01513d89a"}, {"source": "2a7a31815153d26b39f6", "target": "d9ad0499316f09c598a3"}, {"source": "2a7a31815153d26b39f6", "target": "3bb8a779948659a5b101"}, {"source": "2a7a31815153d26b39f6", "target": "07bbe91f368e5ce0b180"}, {"source": "2a7a31815153d26b39f6", "target": "a5e1b098533228613f28"}, {"source": "2a7a31815153d26b39f6", "target": "de4a9e0010795ace76b8"}, {"source": "d9ad0499316f09c598a3", "target": "5a4ab3ca8b3b7b57d234"}, {"source": "d9ad0499316f09c598a3", "target": "0f7abbfc6ab01513d89a"}, {"source": "d9ad0499316f09c598a3", "target": "3bb8a779948659a5b101"}, {"source": "d9ad0499316f09c598a3", "target": "07bbe91f368e5ce0b180"}, {"source": "d9ad0499316f09c598a3", "target": "a5e1b098533228613f28"}, {"source": "d9ad0499316f09c598a3", "target": "de4a9e0010795ace76b8"}, {"source": "0f7abbfc6ab01513d89a", "target": "5a4ab3ca8b3b7b57d234"}, {"source": "0f7abbfc6ab01513d89a", "target": "3bb8a779948659a5b101"}, {"source": "0f7abbfc6ab01513d89a", "target": "07bbe91f368e5ce0b180"}, {"source": "0f7abbfc6ab01513d89a", "target": "a5e1b098533228613f28"}, {"source": "0f7abbfc6ab01513d89a", "target": "de4a9e0010795ace76b8"}, {"source": "5a4ab3ca8b3b7b57d234", "target": "3bb8a779948659a5b101"}, {"source": "5a4ab3ca8b3b7b57d234", "target": "07bbe91f368e5ce0b180"}, {"source": "5a4ab3ca8b3b7b57d234", "target": "a5e1b098533228613f28"}, {"source": "5a4ab3ca8b3b7b57d234", "target": "de4a9e0010795ace76b8"}, {"source": "de4a9e0010795ace76b8", "target": "3bb8a779948659a5b101"}, {"source": "de4a9e0010795ace76b8", "target": "07bbe91f368e5ce0b180"}, {"source": "de4a9e0010795ace76b8", "target": "a5e1b098533228613f28"}, {"source": "a5e1b098533228613f28", "target": "3bb8a779948659a5b101"}, {"source": "a5e1b098533228613f28", "target": "07bbe91f368e5ce0b180"}, {"source": "07bbe91f368e5ce0b180", "target": "3bb8a779948659a5b101"}, {"source": "c31c5eb3bf1c5a72bde9", "target": "39d0395522fb4882de38"}, {"source": "7d15b6ea45e5b37c20c4", "target": "df90b219b9443e457e0d"}, {"source": "7d15b6ea45e5b37c20c4", "target": "bc36d0af347f32152e07"}, {"source": "7b66ad387b75d6077348", "target": "d517a035d0abb5cd0919"}, {"source": "9ea56e041f6847dbe944", "target": "6193058"}, {"source": "94c3203a8f86d34a6af7", "target": "6476579"}, {"source": "94c3203a8f86d34a6af7", "target": "6123708"}, {"source": "94c3203a8f86d34a6af7", "target": "752b97cef3f880a813ab"}, {"source": "94c3203a8f86d34a6af7", "target": "86e51152d02e6280e9f3"}, {"source": "1e7a161a8bf6f545b8f8", "target": "50667ddd436c39f66b84"}, {"source": "1fd196cd99f8d58a56d3", "target": "8be32e6f1f32920ba841"}, {"source": "dc1ce756527c072885dc", "target": "18cb7a77b2d9de597b86"}, {"source": "b95748220bd4752fb33a", "target": "18cb7a77b2d9de597b86"}, {"source": "bdc0474429e6567bc320", "target": "3887118,"}, {"source": "707cc158d2520c4e4602", "target": "8287bd24c157edb5bb2b"}, {"source": "e09fcd2621b42d07a9fe", "target": "acb1dde273bff654262c"}, {"source": "0faeb2237488f7aa526b", "target": "66fa642ce3eb6a0de885"}, {"source": "6e602654b41a48709612", "target": "3884955"}, {"source": "d6c5a1923f53a60876ad", "target": "3884955"}, {"source": "d6c5a1923f53a60876ad", "target": "6be047ed3c064b505c8f"}, {"source": "6be047ed3c064b505c8f", "target": "3884955"}, {"source": "533e9ec746874e1a36cd", "target": "3884955"}, {"source": "6e75ad8f8664c245d0a0", "target": "3884955"}, {"source": "09f8c8eb06feff3e6dea", "target": "2b1e4cc603310e9abc86"}, {"source": "09f8c8eb06feff3e6dea", "target": "17567df60d22eed325b4"}, {"source": "09f8c8eb06feff3e6dea", "target": "e758727fb4e111fdf045"}, {"source": "09f8c8eb06feff3e6dea", "target": "b3f97b9b6a27a2878f5b"}, {"source": "09f8c8eb06feff3e6dea", "target": "51cd71f0b6854e57e6a1"}, {"source": "09f8c8eb06feff3e6dea", "target": "c55265d611e30e7e95f0"}, {"source": "09f8c8eb06feff3e6dea", "target": "0514ad3e56d05722f997"}, {"source": "0514ad3e56d05722f997", "target": "2b1e4cc603310e9abc86"}, {"source": "0514ad3e56d05722f997", "target": "17567df60d22eed325b4"}, {"source": "0514ad3e56d05722f997", "target": "e758727fb4e111fdf045"}, {"source": "0514ad3e56d05722f997", "target": "b3f97b9b6a27a2878f5b"}, {"source": "0514ad3e56d05722f997", "target": "51cd71f0b6854e57e6a1"}, {"source": "0514ad3e56d05722f997", "target": "c55265d611e30e7e95f0"}, {"source": "c55265d611e30e7e95f0", "target": "2b1e4cc603310e9abc86"}, {"source": "c55265d611e30e7e95f0", "target": "17567df60d22eed325b4"}, {"source": "c55265d611e30e7e95f0", "target": "e758727fb4e111fdf045"}, {"source": "c55265d611e30e7e95f0", "target": "b3f97b9b6a27a2878f5b"}, {"source": "c55265d611e30e7e95f0", "target": "51cd71f0b6854e57e6a1"}, {"source": "51cd71f0b6854e57e6a1", "target": "2b1e4cc603310e9abc86"}, {"source": "51cd71f0b6854e57e6a1", "target": "17567df60d22eed325b4"}, {"source": "51cd71f0b6854e57e6a1", "target": "e758727fb4e111fdf045"}, {"source": "51cd71f0b6854e57e6a1", "target": "b3f97b9b6a27a2878f5b"}, {"source": "b3f97b9b6a27a2878f5b", "target": "2b1e4cc603310e9abc86"}, {"source": "b3f97b9b6a27a2878f5b", "target": "17567df60d22eed325b4"}, {"source": "b3f97b9b6a27a2878f5b", "target": "e758727fb4e111fdf045"}, {"source": "e758727fb4e111fdf045", "target": "2b1e4cc603310e9abc86"}, {"source": "e758727fb4e111fdf045", "target": "17567df60d22eed325b4"}, {"source": "17567df60d22eed325b4", "target": "2b1e4cc603310e9abc86"}, {"source": "264ed380895fea0d3844", "target": "3884955"}, {"source": "3fa3c1a523c8f3c2cfb9", "target": "e749224c89f82788cb18"}, {"source": "3fa3c1a523c8f3c2cfb9", "target": "9673c96a682fe3948379"}, {"source": "3fa3c1a523c8f3c2cfb9", "target": "21f99959d48dd0d0c746"}, {"source": "89e1e69c888e8241ed92", "target": "1e6da47724c39156adb3"}, {"source": "89e1e69c888e8241ed92", "target": "0a8fe1caa2ac025c8e86"}, {"source": "9da0a8c4e7391243e941", "target": "3884955"}, {"source": "9da0a8c4e7391243e941", "target": "fa5deb2022e829449688"}, {"source": "9da0a8c4e7391243e941", "target": "189bbf2dad71071b9969"}, {"source": "9da0a8c4e7391243e941", "target": "2583342b5b484e70899b"}, {"source": "ca1f73e81c2440b46eed", "target": "3884955"}, {"source": "ca1f73e81c2440b46eed", "target": "5cf2da9490fac8f59799"}, {"source": "5cf2da9490fac8f59799", "target": "3884955"}, {"source": "a416105e509593928ec5", "target": "3884955"}, {"source": "a7b5b1f23019e065d8aa", "target": "3884955"}, {"source": "ca351b22bc0778e97d79", "target": "3884955"}, {"source": "ca351b22bc0778e97d79", "target": "f80a387b1ce45adfbe08"}, {"source": "f80a387b1ce45adfbe08", "target": "3884955"}, {"source": "d63a29287faa83227f65", "target": "3884955"}, {"source": "d63a29287faa83227f65", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "d63a29287faa83227f65", "target": "29d80cf22eda5b55e2c1"}, {"source": "d63a29287faa83227f65", "target": "7c289a9dbf38ceb665d8"}, {"source": "d63a29287faa83227f65", "target": "a45d78cc14704ced3f88"}, {"source": "d63a29287faa83227f65", "target": "deb677307bfb906b38e6"}, {"source": "d63a29287faa83227f65", "target": "a9711c2f39239465f30e"}, {"source": "d63a29287faa83227f65", "target": "de369cc33c7c02015263"}, {"source": "d63a29287faa83227f65", "target": "6e75e8d20da020b9de19"}, {"source": "d63a29287faa83227f65", "target": "1727593e326e81b0951f"}, {"source": "d63a29287faa83227f65", "target": "4db859d1e3fd8e8b8642"}, {"source": "d63a29287faa83227f65", "target": "a2e10229636d23f13dbb"}, {"source": "d63a29287faa83227f65", "target": "d7aded22d8ab1a5ff063"}, {"source": "d63a29287faa83227f65", "target": "527111d4bf58cfbaa8f7"}, {"source": "d63a29287faa83227f65", "target": "4119e1e519d687eb7ffc"}, {"source": "d63a29287faa83227f65", "target": "aaa180c9379a902aba3e"}, {"source": "d63a29287faa83227f65", "target": "5359c5b7020527a68e60"}, {"source": "d63a29287faa83227f65", "target": "aa154290d2f39a1ab54b"}, {"source": "d63a29287faa83227f65", "target": "ab47787d16eb43d8af34"}, {"source": "d63a29287faa83227f65", "target": "185510fc80ac8a874cd1"}, {"source": "d63a29287faa83227f65", "target": "7ae92335446d3ad639d0"}, {"source": "d63a29287faa83227f65", "target": "5740eb91f7f2f4bef0ae"}, {"source": "d63a29287faa83227f65", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "d63a29287faa83227f65", "target": "87df80ba235efb82e9de"}, {"source": "d63a29287faa83227f65", "target": "7b28e2aca81bd450e15e"}, {"source": "d63a29287faa83227f65", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "d63a29287faa83227f65", "target": "640803240b9db8f610af"}, {"source": "d63a29287faa83227f65", "target": "3da6b1e5522c57dd8106"}, {"source": "d63a29287faa83227f65", "target": "c7b3934926cee4f7b39e"}, {"source": "d63a29287faa83227f65", "target": "80509e9d1cdc66568152"}, {"source": "d63a29287faa83227f65", "target": "a592a0a644f806ee56a7"}, {"source": "d63a29287faa83227f65", "target": "ab22a21c4fb7d977ea7b"}, {"source": "d63a29287faa83227f65", "target": "b74bb2749f8a96c4c241"}, {"source": "d63a29287faa83227f65", "target": "b7f4298f7d2892ba4b1b"}, {"source": "d63a29287faa83227f65", "target": "d865b3a6873f39fe0d75"}, {"source": "d63a29287faa83227f65", "target": "65ea67b5e062b5ba666b"}, {"source": "d63a29287faa83227f65", "target": "921af093736051ab15a8"}, {"source": "921af093736051ab15a8", "target": "3884955"}, {"source": "921af093736051ab15a8", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "921af093736051ab15a8", "target": "29d80cf22eda5b55e2c1"}, {"source": "921af093736051ab15a8", "target": "7c289a9dbf38ceb665d8"}, {"source": "921af093736051ab15a8", "target": "a45d78cc14704ced3f88"}, {"source": "921af093736051ab15a8", "target": "deb677307bfb906b38e6"}, {"source": "921af093736051ab15a8", "target": "a9711c2f39239465f30e"}, {"source": "921af093736051ab15a8", "target": "de369cc33c7c02015263"}, {"source": "921af093736051ab15a8", "target": "6e75e8d20da020b9de19"}, {"source": "921af093736051ab15a8", "target": "1727593e326e81b0951f"}, {"source": "921af093736051ab15a8", "target": "4db859d1e3fd8e8b8642"}, {"source": "921af093736051ab15a8", "target": "a2e10229636d23f13dbb"}, {"source": "921af093736051ab15a8", "target": "d7aded22d8ab1a5ff063"}, {"source": "921af093736051ab15a8", "target": "527111d4bf58cfbaa8f7"}, {"source": "921af093736051ab15a8", "target": "4119e1e519d687eb7ffc"}, {"source": "921af093736051ab15a8", "target": "aaa180c9379a902aba3e"}, {"source": "921af093736051ab15a8", "target": "5359c5b7020527a68e60"}, {"source": "921af093736051ab15a8", "target": "aa154290d2f39a1ab54b"}, {"source": "921af093736051ab15a8", "target": "ab47787d16eb43d8af34"}, {"source": "921af093736051ab15a8", "target": "185510fc80ac8a874cd1"}, {"source": "921af093736051ab15a8", "target": "7ae92335446d3ad639d0"}, {"source": "921af093736051ab15a8", "target": "5740eb91f7f2f4bef0ae"}, {"source": "921af093736051ab15a8", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "921af093736051ab15a8", "target": "87df80ba235efb82e9de"}, {"source": "921af093736051ab15a8", "target": "7b28e2aca81bd450e15e"}, {"source": "921af093736051ab15a8", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "921af093736051ab15a8", "target": "640803240b9db8f610af"}, {"source": "921af093736051ab15a8", "target": "3da6b1e5522c57dd8106"}, {"source": "921af093736051ab15a8", "target": "c7b3934926cee4f7b39e"}, {"source": "921af093736051ab15a8", "target": "80509e9d1cdc66568152"}, {"source": "921af093736051ab15a8", "target": "a592a0a644f806ee56a7"}, {"source": "921af093736051ab15a8", "target": "ab22a21c4fb7d977ea7b"}, {"source": "921af093736051ab15a8", "target": "b74bb2749f8a96c4c241"}, {"source": "921af093736051ab15a8", "target": "b7f4298f7d2892ba4b1b"}, {"source": "921af093736051ab15a8", "target": "d865b3a6873f39fe0d75"}, {"source": "921af093736051ab15a8", "target": "65ea67b5e062b5ba666b"}, {"source": "65ea67b5e062b5ba666b", "target": "3884955"}, {"source": "65ea67b5e062b5ba666b", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "65ea67b5e062b5ba666b", "target": "29d80cf22eda5b55e2c1"}, {"source": "65ea67b5e062b5ba666b", "target": "7c289a9dbf38ceb665d8"}, {"source": "65ea67b5e062b5ba666b", "target": "a45d78cc14704ced3f88"}, {"source": "65ea67b5e062b5ba666b", "target": "deb677307bfb906b38e6"}, {"source": "65ea67b5e062b5ba666b", "target": "a9711c2f39239465f30e"}, {"source": "65ea67b5e062b5ba666b", "target": "de369cc33c7c02015263"}, {"source": "65ea67b5e062b5ba666b", "target": "6e75e8d20da020b9de19"}, {"source": "65ea67b5e062b5ba666b", "target": "1727593e326e81b0951f"}, {"source": "65ea67b5e062b5ba666b", "target": "4db859d1e3fd8e8b8642"}, {"source": "65ea67b5e062b5ba666b", "target": "a2e10229636d23f13dbb"}, {"source": "65ea67b5e062b5ba666b", "target": "d7aded22d8ab1a5ff063"}, {"source": "65ea67b5e062b5ba666b", "target": "527111d4bf58cfbaa8f7"}, {"source": "65ea67b5e062b5ba666b", "target": "4119e1e519d687eb7ffc"}, {"source": "65ea67b5e062b5ba666b", "target": "aaa180c9379a902aba3e"}, {"source": "65ea67b5e062b5ba666b", "target": "5359c5b7020527a68e60"}, {"source": "65ea67b5e062b5ba666b", "target": "aa154290d2f39a1ab54b"}, {"source": "65ea67b5e062b5ba666b", "target": "ab47787d16eb43d8af34"}, {"source": "65ea67b5e062b5ba666b", "target": "185510fc80ac8a874cd1"}, {"source": "65ea67b5e062b5ba666b", "target": "7ae92335446d3ad639d0"}, {"source": "65ea67b5e062b5ba666b", "target": "5740eb91f7f2f4bef0ae"}, {"source": "65ea67b5e062b5ba666b", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "65ea67b5e062b5ba666b", "target": "87df80ba235efb82e9de"}, {"source": "65ea67b5e062b5ba666b", "target": "7b28e2aca81bd450e15e"}, {"source": "65ea67b5e062b5ba666b", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "65ea67b5e062b5ba666b", "target": "640803240b9db8f610af"}, {"source": "65ea67b5e062b5ba666b", "target": "3da6b1e5522c57dd8106"}, {"source": "65ea67b5e062b5ba666b", "target": "c7b3934926cee4f7b39e"}, {"source": "65ea67b5e062b5ba666b", "target": "80509e9d1cdc66568152"}, {"source": "65ea67b5e062b5ba666b", "target": "a592a0a644f806ee56a7"}, {"source": "65ea67b5e062b5ba666b", "target": "ab22a21c4fb7d977ea7b"}, {"source": "65ea67b5e062b5ba666b", "target": "b74bb2749f8a96c4c241"}, {"source": "65ea67b5e062b5ba666b", "target": "b7f4298f7d2892ba4b1b"}, {"source": "65ea67b5e062b5ba666b", "target": "d865b3a6873f39fe0d75"}, {"source": "d865b3a6873f39fe0d75", "target": "3884955"}, {"source": "d865b3a6873f39fe0d75", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "d865b3a6873f39fe0d75", "target": "29d80cf22eda5b55e2c1"}, {"source": "d865b3a6873f39fe0d75", "target": "7c289a9dbf38ceb665d8"}, {"source": "d865b3a6873f39fe0d75", "target": "a45d78cc14704ced3f88"}, {"source": "d865b3a6873f39fe0d75", "target": "deb677307bfb906b38e6"}, {"source": "d865b3a6873f39fe0d75", "target": "a9711c2f39239465f30e"}, {"source": "d865b3a6873f39fe0d75", "target": "de369cc33c7c02015263"}, {"source": "d865b3a6873f39fe0d75", "target": "6e75e8d20da020b9de19"}, {"source": "d865b3a6873f39fe0d75", "target": "1727593e326e81b0951f"}, {"source": "d865b3a6873f39fe0d75", "target": "4db859d1e3fd8e8b8642"}, {"source": "d865b3a6873f39fe0d75", "target": "a2e10229636d23f13dbb"}, {"source": "d865b3a6873f39fe0d75", "target": "d7aded22d8ab1a5ff063"}, {"source": "d865b3a6873f39fe0d75", "target": "527111d4bf58cfbaa8f7"}, {"source": "d865b3a6873f39fe0d75", "target": "4119e1e519d687eb7ffc"}, {"source": "d865b3a6873f39fe0d75", "target": "aaa180c9379a902aba3e"}, {"source": "d865b3a6873f39fe0d75", "target": "5359c5b7020527a68e60"}, {"source": "d865b3a6873f39fe0d75", "target": "aa154290d2f39a1ab54b"}, {"source": "d865b3a6873f39fe0d75", "target": "ab47787d16eb43d8af34"}, {"source": "d865b3a6873f39fe0d75", "target": "185510fc80ac8a874cd1"}, {"source": "d865b3a6873f39fe0d75", "target": "7ae92335446d3ad639d0"}, {"source": "d865b3a6873f39fe0d75", "target": "5740eb91f7f2f4bef0ae"}, {"source": "d865b3a6873f39fe0d75", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "d865b3a6873f39fe0d75", "target": "87df80ba235efb82e9de"}, {"source": "d865b3a6873f39fe0d75", "target": "7b28e2aca81bd450e15e"}, {"source": "d865b3a6873f39fe0d75", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "d865b3a6873f39fe0d75", "target": "640803240b9db8f610af"}, {"source": "d865b3a6873f39fe0d75", "target": "3da6b1e5522c57dd8106"}, {"source": "d865b3a6873f39fe0d75", "target": "c7b3934926cee4f7b39e"}, {"source": "d865b3a6873f39fe0d75", "target": "80509e9d1cdc66568152"}, {"source": "d865b3a6873f39fe0d75", "target": "a592a0a644f806ee56a7"}, {"source": "d865b3a6873f39fe0d75", "target": "ab22a21c4fb7d977ea7b"}, {"source": "d865b3a6873f39fe0d75", "target": "b74bb2749f8a96c4c241"}, {"source": "d865b3a6873f39fe0d75", "target": "b7f4298f7d2892ba4b1b"}, {"source": "b7f4298f7d2892ba4b1b", "target": "3884955"}, {"source": "b7f4298f7d2892ba4b1b", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "b7f4298f7d2892ba4b1b", "target": "29d80cf22eda5b55e2c1"}, {"source": "b7f4298f7d2892ba4b1b", "target": "7c289a9dbf38ceb665d8"}, {"source": "b7f4298f7d2892ba4b1b", "target": "a45d78cc14704ced3f88"}, {"source": "b7f4298f7d2892ba4b1b", "target": "deb677307bfb906b38e6"}, {"source": "b7f4298f7d2892ba4b1b", "target": "a9711c2f39239465f30e"}, {"source": "b7f4298f7d2892ba4b1b", "target": "de369cc33c7c02015263"}, {"source": "b7f4298f7d2892ba4b1b", "target": "6e75e8d20da020b9de19"}, {"source": "b7f4298f7d2892ba4b1b", "target": "1727593e326e81b0951f"}, {"source": "b7f4298f7d2892ba4b1b", "target": "4db859d1e3fd8e8b8642"}, {"source": "b7f4298f7d2892ba4b1b", "target": "a2e10229636d23f13dbb"}, {"source": "b7f4298f7d2892ba4b1b", "target": "d7aded22d8ab1a5ff063"}, {"source": "b7f4298f7d2892ba4b1b", "target": "527111d4bf58cfbaa8f7"}, {"source": "b7f4298f7d2892ba4b1b", "target": "4119e1e519d687eb7ffc"}, {"source": "b7f4298f7d2892ba4b1b", "target": "aaa180c9379a902aba3e"}, {"source": "b7f4298f7d2892ba4b1b", "target": "5359c5b7020527a68e60"}, {"source": "b7f4298f7d2892ba4b1b", "target": "aa154290d2f39a1ab54b"}, {"source": "b7f4298f7d2892ba4b1b", "target": "ab47787d16eb43d8af34"}, {"source": "b7f4298f7d2892ba4b1b", "target": "185510fc80ac8a874cd1"}, {"source": "b7f4298f7d2892ba4b1b", "target": "7ae92335446d3ad639d0"}, {"source": "b7f4298f7d2892ba4b1b", "target": "5740eb91f7f2f4bef0ae"}, {"source": "b7f4298f7d2892ba4b1b", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "b7f4298f7d2892ba4b1b", "target": "87df80ba235efb82e9de"}, {"source": "b7f4298f7d2892ba4b1b", "target": "7b28e2aca81bd450e15e"}, {"source": "b7f4298f7d2892ba4b1b", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "b7f4298f7d2892ba4b1b", "target": "640803240b9db8f610af"}, {"source": "b7f4298f7d2892ba4b1b", "target": "3da6b1e5522c57dd8106"}, {"source": "b7f4298f7d2892ba4b1b", "target": "c7b3934926cee4f7b39e"}, {"source": "b7f4298f7d2892ba4b1b", "target": "80509e9d1cdc66568152"}, {"source": "b7f4298f7d2892ba4b1b", "target": "a592a0a644f806ee56a7"}, {"source": "b7f4298f7d2892ba4b1b", "target": "ab22a21c4fb7d977ea7b"}, {"source": "b7f4298f7d2892ba4b1b", "target": "b74bb2749f8a96c4c241"}, {"source": "b74bb2749f8a96c4c241", "target": "3884955"}, {"source": "b74bb2749f8a96c4c241", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "b74bb2749f8a96c4c241", "target": "29d80cf22eda5b55e2c1"}, {"source": "b74bb2749f8a96c4c241", "target": "7c289a9dbf38ceb665d8"}, {"source": "b74bb2749f8a96c4c241", "target": "a45d78cc14704ced3f88"}, {"source": "b74bb2749f8a96c4c241", "target": "deb677307bfb906b38e6"}, {"source": "b74bb2749f8a96c4c241", "target": "a9711c2f39239465f30e"}, {"source": "b74bb2749f8a96c4c241", "target": "de369cc33c7c02015263"}, {"source": "b74bb2749f8a96c4c241", "target": "6e75e8d20da020b9de19"}, {"source": "b74bb2749f8a96c4c241", "target": "1727593e326e81b0951f"}, {"source": "b74bb2749f8a96c4c241", "target": "4db859d1e3fd8e8b8642"}, {"source": "b74bb2749f8a96c4c241", "target": "a2e10229636d23f13dbb"}, {"source": "b74bb2749f8a96c4c241", "target": "d7aded22d8ab1a5ff063"}, {"source": "b74bb2749f8a96c4c241", "target": "527111d4bf58cfbaa8f7"}, {"source": "b74bb2749f8a96c4c241", "target": "4119e1e519d687eb7ffc"}, {"source": "b74bb2749f8a96c4c241", "target": "aaa180c9379a902aba3e"}, {"source": "b74bb2749f8a96c4c241", "target": "5359c5b7020527a68e60"}, {"source": "b74bb2749f8a96c4c241", "target": "aa154290d2f39a1ab54b"}, {"source": "b74bb2749f8a96c4c241", "target": "ab47787d16eb43d8af34"}, {"source": "b74bb2749f8a96c4c241", "target": "185510fc80ac8a874cd1"}, {"source": "b74bb2749f8a96c4c241", "target": "7ae92335446d3ad639d0"}, {"source": "b74bb2749f8a96c4c241", "target": "5740eb91f7f2f4bef0ae"}, {"source": "b74bb2749f8a96c4c241", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "b74bb2749f8a96c4c241", "target": "87df80ba235efb82e9de"}, {"source": "b74bb2749f8a96c4c241", "target": "7b28e2aca81bd450e15e"}, {"source": "b74bb2749f8a96c4c241", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "b74bb2749f8a96c4c241", "target": "640803240b9db8f610af"}, {"source": "b74bb2749f8a96c4c241", "target": "3da6b1e5522c57dd8106"}, {"source": "b74bb2749f8a96c4c241", "target": "c7b3934926cee4f7b39e"}, {"source": "b74bb2749f8a96c4c241", "target": "80509e9d1cdc66568152"}, {"source": "b74bb2749f8a96c4c241", "target": "a592a0a644f806ee56a7"}, {"source": "b74bb2749f8a96c4c241", "target": "ab22a21c4fb7d977ea7b"}, {"source": "ab22a21c4fb7d977ea7b", "target": "3884955"}, {"source": "ab22a21c4fb7d977ea7b", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "ab22a21c4fb7d977ea7b", "target": "29d80cf22eda5b55e2c1"}, {"source": "ab22a21c4fb7d977ea7b", "target": "7c289a9dbf38ceb665d8"}, {"source": "ab22a21c4fb7d977ea7b", "target": "a45d78cc14704ced3f88"}, {"source": "ab22a21c4fb7d977ea7b", "target": "deb677307bfb906b38e6"}, {"source": "ab22a21c4fb7d977ea7b", "target": "a9711c2f39239465f30e"}, {"source": "ab22a21c4fb7d977ea7b", "target": "de369cc33c7c02015263"}, {"source": "ab22a21c4fb7d977ea7b", "target": "6e75e8d20da020b9de19"}, {"source": "ab22a21c4fb7d977ea7b", "target": "1727593e326e81b0951f"}, {"source": "ab22a21c4fb7d977ea7b", "target": "4db859d1e3fd8e8b8642"}, {"source": "ab22a21c4fb7d977ea7b", "target": "a2e10229636d23f13dbb"}, {"source": "ab22a21c4fb7d977ea7b", "target": "d7aded22d8ab1a5ff063"}, {"source": "ab22a21c4fb7d977ea7b", "target": "527111d4bf58cfbaa8f7"}, {"source": "ab22a21c4fb7d977ea7b", "target": "4119e1e519d687eb7ffc"}, {"source": "ab22a21c4fb7d977ea7b", "target": "aaa180c9379a902aba3e"}, {"source": "ab22a21c4fb7d977ea7b", "target": "5359c5b7020527a68e60"}, {"source": "ab22a21c4fb7d977ea7b", "target": "aa154290d2f39a1ab54b"}, {"source": "ab22a21c4fb7d977ea7b", "target": "ab47787d16eb43d8af34"}, {"source": "ab22a21c4fb7d977ea7b", "target": "185510fc80ac8a874cd1"}, {"source": "ab22a21c4fb7d977ea7b", "target": "7ae92335446d3ad639d0"}, {"source": "ab22a21c4fb7d977ea7b", "target": "5740eb91f7f2f4bef0ae"}, {"source": "ab22a21c4fb7d977ea7b", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "ab22a21c4fb7d977ea7b", "target": "87df80ba235efb82e9de"}, {"source": "ab22a21c4fb7d977ea7b", "target": "7b28e2aca81bd450e15e"}, {"source": "ab22a21c4fb7d977ea7b", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "ab22a21c4fb7d977ea7b", "target": "640803240b9db8f610af"}, {"source": "ab22a21c4fb7d977ea7b", "target": "3da6b1e5522c57dd8106"}, {"source": "ab22a21c4fb7d977ea7b", "target": "c7b3934926cee4f7b39e"}, {"source": "ab22a21c4fb7d977ea7b", "target": "80509e9d1cdc66568152"}, {"source": "ab22a21c4fb7d977ea7b", "target": "a592a0a644f806ee56a7"}, {"source": "a592a0a644f806ee56a7", "target": "3884955"}, {"source": "a592a0a644f806ee56a7", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "a592a0a644f806ee56a7", "target": "29d80cf22eda5b55e2c1"}, {"source": "a592a0a644f806ee56a7", "target": "7c289a9dbf38ceb665d8"}, {"source": "a592a0a644f806ee56a7", "target": "a45d78cc14704ced3f88"}, {"source": "a592a0a644f806ee56a7", "target": "deb677307bfb906b38e6"}, {"source": "a592a0a644f806ee56a7", "target": "a9711c2f39239465f30e"}, {"source": "a592a0a644f806ee56a7", "target": "de369cc33c7c02015263"}, {"source": "a592a0a644f806ee56a7", "target": "6e75e8d20da020b9de19"}, {"source": "a592a0a644f806ee56a7", "target": "1727593e326e81b0951f"}, {"source": "a592a0a644f806ee56a7", "target": "4db859d1e3fd8e8b8642"}, {"source": "a592a0a644f806ee56a7", "target": "a2e10229636d23f13dbb"}, {"source": "a592a0a644f806ee56a7", "target": "d7aded22d8ab1a5ff063"}, {"source": "a592a0a644f806ee56a7", "target": "527111d4bf58cfbaa8f7"}, {"source": "a592a0a644f806ee56a7", "target": "4119e1e519d687eb7ffc"}, {"source": "a592a0a644f806ee56a7", "target": "aaa180c9379a902aba3e"}, {"source": "a592a0a644f806ee56a7", "target": "5359c5b7020527a68e60"}, {"source": "a592a0a644f806ee56a7", "target": "aa154290d2f39a1ab54b"}, {"source": "a592a0a644f806ee56a7", "target": "ab47787d16eb43d8af34"}, {"source": "a592a0a644f806ee56a7", "target": "185510fc80ac8a874cd1"}, {"source": "a592a0a644f806ee56a7", "target": "7ae92335446d3ad639d0"}, {"source": "a592a0a644f806ee56a7", "target": "5740eb91f7f2f4bef0ae"}, {"source": "a592a0a644f806ee56a7", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "a592a0a644f806ee56a7", "target": "87df80ba235efb82e9de"}, {"source": "a592a0a644f806ee56a7", "target": "7b28e2aca81bd450e15e"}, {"source": "a592a0a644f806ee56a7", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "a592a0a644f806ee56a7", "target": "640803240b9db8f610af"}, {"source": "a592a0a644f806ee56a7", "target": "3da6b1e5522c57dd8106"}, {"source": "a592a0a644f806ee56a7", "target": "c7b3934926cee4f7b39e"}, {"source": "a592a0a644f806ee56a7", "target": "80509e9d1cdc66568152"}, {"source": "80509e9d1cdc66568152", "target": "3884955"}, {"source": "80509e9d1cdc66568152", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "80509e9d1cdc66568152", "target": "29d80cf22eda5b55e2c1"}, {"source": "80509e9d1cdc66568152", "target": "7c289a9dbf38ceb665d8"}, {"source": "80509e9d1cdc66568152", "target": "a45d78cc14704ced3f88"}, {"source": "80509e9d1cdc66568152", "target": "deb677307bfb906b38e6"}, {"source": "80509e9d1cdc66568152", "target": "a9711c2f39239465f30e"}, {"source": "80509e9d1cdc66568152", "target": "de369cc33c7c02015263"}, {"source": "80509e9d1cdc66568152", "target": "6e75e8d20da020b9de19"}, {"source": "80509e9d1cdc66568152", "target": "1727593e326e81b0951f"}, {"source": "80509e9d1cdc66568152", "target": "4db859d1e3fd8e8b8642"}, {"source": "80509e9d1cdc66568152", "target": "a2e10229636d23f13dbb"}, {"source": "80509e9d1cdc66568152", "target": "d7aded22d8ab1a5ff063"}, {"source": "80509e9d1cdc66568152", "target": "527111d4bf58cfbaa8f7"}, {"source": "80509e9d1cdc66568152", "target": "4119e1e519d687eb7ffc"}, {"source": "80509e9d1cdc66568152", "target": "aaa180c9379a902aba3e"}, {"source": "80509e9d1cdc66568152", "target": "5359c5b7020527a68e60"}, {"source": "80509e9d1cdc66568152", "target": "aa154290d2f39a1ab54b"}, {"source": "80509e9d1cdc66568152", "target": "ab47787d16eb43d8af34"}, {"source": "80509e9d1cdc66568152", "target": "185510fc80ac8a874cd1"}, {"source": "80509e9d1cdc66568152", "target": "7ae92335446d3ad639d0"}, {"source": "80509e9d1cdc66568152", "target": "5740eb91f7f2f4bef0ae"}, {"source": "80509e9d1cdc66568152", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "80509e9d1cdc66568152", "target": "87df80ba235efb82e9de"}, {"source": "80509e9d1cdc66568152", "target": "7b28e2aca81bd450e15e"}, {"source": "80509e9d1cdc66568152", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "80509e9d1cdc66568152", "target": "640803240b9db8f610af"}, {"source": "80509e9d1cdc66568152", "target": "3da6b1e5522c57dd8106"}, {"source": "80509e9d1cdc66568152", "target": "c7b3934926cee4f7b39e"}, {"source": "c7b3934926cee4f7b39e", "target": "3884955"}, {"source": "c7b3934926cee4f7b39e", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "c7b3934926cee4f7b39e", "target": "29d80cf22eda5b55e2c1"}, {"source": "c7b3934926cee4f7b39e", "target": "7c289a9dbf38ceb665d8"}, {"source": "c7b3934926cee4f7b39e", "target": "a45d78cc14704ced3f88"}, {"source": "c7b3934926cee4f7b39e", "target": "deb677307bfb906b38e6"}, {"source": "c7b3934926cee4f7b39e", "target": "a9711c2f39239465f30e"}, {"source": "c7b3934926cee4f7b39e", "target": "de369cc33c7c02015263"}, {"source": "c7b3934926cee4f7b39e", "target": "6e75e8d20da020b9de19"}, {"source": "c7b3934926cee4f7b39e", "target": "1727593e326e81b0951f"}, {"source": "c7b3934926cee4f7b39e", "target": "4db859d1e3fd8e8b8642"}, {"source": "c7b3934926cee4f7b39e", "target": "a2e10229636d23f13dbb"}, {"source": "c7b3934926cee4f7b39e", "target": "d7aded22d8ab1a5ff063"}, {"source": "c7b3934926cee4f7b39e", "target": "527111d4bf58cfbaa8f7"}, {"source": "c7b3934926cee4f7b39e", "target": "4119e1e519d687eb7ffc"}, {"source": "c7b3934926cee4f7b39e", "target": "aaa180c9379a902aba3e"}, {"source": "c7b3934926cee4f7b39e", "target": "5359c5b7020527a68e60"}, {"source": "c7b3934926cee4f7b39e", "target": "aa154290d2f39a1ab54b"}, {"source": "c7b3934926cee4f7b39e", "target": "ab47787d16eb43d8af34"}, {"source": "c7b3934926cee4f7b39e", "target": "185510fc80ac8a874cd1"}, {"source": "c7b3934926cee4f7b39e", "target": "7ae92335446d3ad639d0"}, {"source": "c7b3934926cee4f7b39e", "target": "5740eb91f7f2f4bef0ae"}, {"source": "c7b3934926cee4f7b39e", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "c7b3934926cee4f7b39e", "target": "87df80ba235efb82e9de"}, {"source": "c7b3934926cee4f7b39e", "target": "7b28e2aca81bd450e15e"}, {"source": "c7b3934926cee4f7b39e", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "c7b3934926cee4f7b39e", "target": "640803240b9db8f610af"}, {"source": "c7b3934926cee4f7b39e", "target": "3da6b1e5522c57dd8106"}, {"source": "3da6b1e5522c57dd8106", "target": "3884955"}, {"source": "3da6b1e5522c57dd8106", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "3da6b1e5522c57dd8106", "target": "29d80cf22eda5b55e2c1"}, {"source": "3da6b1e5522c57dd8106", "target": "7c289a9dbf38ceb665d8"}, {"source": "3da6b1e5522c57dd8106", "target": "a45d78cc14704ced3f88"}, {"source": "3da6b1e5522c57dd8106", "target": "deb677307bfb906b38e6"}, {"source": "3da6b1e5522c57dd8106", "target": "a9711c2f39239465f30e"}, {"source": "3da6b1e5522c57dd8106", "target": "de369cc33c7c02015263"}, {"source": "3da6b1e5522c57dd8106", "target": "6e75e8d20da020b9de19"}, {"source": "3da6b1e5522c57dd8106", "target": "1727593e326e81b0951f"}, {"source": "3da6b1e5522c57dd8106", "target": "4db859d1e3fd8e8b8642"}, {"source": "3da6b1e5522c57dd8106", "target": "a2e10229636d23f13dbb"}, {"source": "3da6b1e5522c57dd8106", "target": "d7aded22d8ab1a5ff063"}, {"source": "3da6b1e5522c57dd8106", "target": "527111d4bf58cfbaa8f7"}, {"source": "3da6b1e5522c57dd8106", "target": "4119e1e519d687eb7ffc"}, {"source": "3da6b1e5522c57dd8106", "target": "aaa180c9379a902aba3e"}, {"source": "3da6b1e5522c57dd8106", "target": "5359c5b7020527a68e60"}, {"source": "3da6b1e5522c57dd8106", "target": "aa154290d2f39a1ab54b"}, {"source": "3da6b1e5522c57dd8106", "target": "ab47787d16eb43d8af34"}, {"source": "3da6b1e5522c57dd8106", "target": "185510fc80ac8a874cd1"}, {"source": "3da6b1e5522c57dd8106", "target": "7ae92335446d3ad639d0"}, {"source": "3da6b1e5522c57dd8106", "target": "5740eb91f7f2f4bef0ae"}, {"source": "3da6b1e5522c57dd8106", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "3da6b1e5522c57dd8106", "target": "87df80ba235efb82e9de"}, {"source": "3da6b1e5522c57dd8106", "target": "7b28e2aca81bd450e15e"}, {"source": "3da6b1e5522c57dd8106", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "3da6b1e5522c57dd8106", "target": "640803240b9db8f610af"}, {"source": "640803240b9db8f610af", "target": "3884955"}, {"source": "640803240b9db8f610af", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "640803240b9db8f610af", "target": "29d80cf22eda5b55e2c1"}, {"source": "640803240b9db8f610af", "target": "7c289a9dbf38ceb665d8"}, {"source": "640803240b9db8f610af", "target": "a45d78cc14704ced3f88"}, {"source": "640803240b9db8f610af", "target": "deb677307bfb906b38e6"}, {"source": "640803240b9db8f610af", "target": "a9711c2f39239465f30e"}, {"source": "640803240b9db8f610af", "target": "de369cc33c7c02015263"}, {"source": "640803240b9db8f610af", "target": "6e75e8d20da020b9de19"}, {"source": "640803240b9db8f610af", "target": "1727593e326e81b0951f"}, {"source": "640803240b9db8f610af", "target": "4db859d1e3fd8e8b8642"}, {"source": "640803240b9db8f610af", "target": "a2e10229636d23f13dbb"}, {"source": "640803240b9db8f610af", "target": "d7aded22d8ab1a5ff063"}, {"source": "640803240b9db8f610af", "target": "527111d4bf58cfbaa8f7"}, {"source": "640803240b9db8f610af", "target": "4119e1e519d687eb7ffc"}, {"source": "640803240b9db8f610af", "target": "aaa180c9379a902aba3e"}, {"source": "640803240b9db8f610af", "target": "5359c5b7020527a68e60"}, {"source": "640803240b9db8f610af", "target": "aa154290d2f39a1ab54b"}, {"source": "640803240b9db8f610af", "target": "ab47787d16eb43d8af34"}, {"source": "640803240b9db8f610af", "target": "185510fc80ac8a874cd1"}, {"source": "640803240b9db8f610af", "target": "7ae92335446d3ad639d0"}, {"source": "640803240b9db8f610af", "target": "5740eb91f7f2f4bef0ae"}, {"source": "640803240b9db8f610af", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "640803240b9db8f610af", "target": "87df80ba235efb82e9de"}, {"source": "640803240b9db8f610af", "target": "7b28e2aca81bd450e15e"}, {"source": "640803240b9db8f610af", "target": "6d6fa3b5fbd160e0afb4"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "3884955"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "29d80cf22eda5b55e2c1"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "7c289a9dbf38ceb665d8"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "a45d78cc14704ced3f88"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "deb677307bfb906b38e6"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "a9711c2f39239465f30e"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "de369cc33c7c02015263"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "6e75e8d20da020b9de19"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "1727593e326e81b0951f"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "4db859d1e3fd8e8b8642"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "a2e10229636d23f13dbb"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "d7aded22d8ab1a5ff063"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "527111d4bf58cfbaa8f7"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "4119e1e519d687eb7ffc"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "aaa180c9379a902aba3e"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "5359c5b7020527a68e60"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "aa154290d2f39a1ab54b"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "ab47787d16eb43d8af34"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "185510fc80ac8a874cd1"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "7ae92335446d3ad639d0"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "5740eb91f7f2f4bef0ae"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "87df80ba235efb82e9de"}, {"source": "6d6fa3b5fbd160e0afb4", "target": "7b28e2aca81bd450e15e"}, {"source": "7b28e2aca81bd450e15e", "target": "3884955"}, {"source": "7b28e2aca81bd450e15e", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "7b28e2aca81bd450e15e", "target": "29d80cf22eda5b55e2c1"}, {"source": "7b28e2aca81bd450e15e", "target": "7c289a9dbf38ceb665d8"}, {"source": "7b28e2aca81bd450e15e", "target": "a45d78cc14704ced3f88"}, {"source": "7b28e2aca81bd450e15e", "target": "deb677307bfb906b38e6"}, {"source": "7b28e2aca81bd450e15e", "target": "a9711c2f39239465f30e"}, {"source": "7b28e2aca81bd450e15e", "target": "de369cc33c7c02015263"}, {"source": "7b28e2aca81bd450e15e", "target": "6e75e8d20da020b9de19"}, {"source": "7b28e2aca81bd450e15e", "target": "1727593e326e81b0951f"}, {"source": "7b28e2aca81bd450e15e", "target": "4db859d1e3fd8e8b8642"}, {"source": "7b28e2aca81bd450e15e", "target": "a2e10229636d23f13dbb"}, {"source": "7b28e2aca81bd450e15e", "target": "d7aded22d8ab1a5ff063"}, {"source": "7b28e2aca81bd450e15e", "target": "527111d4bf58cfbaa8f7"}, {"source": "7b28e2aca81bd450e15e", "target": "4119e1e519d687eb7ffc"}, {"source": "7b28e2aca81bd450e15e", "target": "aaa180c9379a902aba3e"}, {"source": "7b28e2aca81bd450e15e", "target": "5359c5b7020527a68e60"}, {"source": "7b28e2aca81bd450e15e", "target": "aa154290d2f39a1ab54b"}, {"source": "7b28e2aca81bd450e15e", "target": "ab47787d16eb43d8af34"}, {"source": "7b28e2aca81bd450e15e", "target": "185510fc80ac8a874cd1"}, {"source": "7b28e2aca81bd450e15e", "target": "7ae92335446d3ad639d0"}, {"source": "7b28e2aca81bd450e15e", "target": "5740eb91f7f2f4bef0ae"}, {"source": "7b28e2aca81bd450e15e", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "7b28e2aca81bd450e15e", "target": "87df80ba235efb82e9de"}, {"source": "87df80ba235efb82e9de", "target": "3884955"}, {"source": "87df80ba235efb82e9de", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "87df80ba235efb82e9de", "target": "29d80cf22eda5b55e2c1"}, {"source": "87df80ba235efb82e9de", "target": "7c289a9dbf38ceb665d8"}, {"source": "87df80ba235efb82e9de", "target": "a45d78cc14704ced3f88"}, {"source": "87df80ba235efb82e9de", "target": "deb677307bfb906b38e6"}, {"source": "87df80ba235efb82e9de", "target": "a9711c2f39239465f30e"}, {"source": "87df80ba235efb82e9de", "target": "de369cc33c7c02015263"}, {"source": "87df80ba235efb82e9de", "target": "6e75e8d20da020b9de19"}, {"source": "87df80ba235efb82e9de", "target": "1727593e326e81b0951f"}, {"source": "87df80ba235efb82e9de", "target": "4db859d1e3fd8e8b8642"}, {"source": "87df80ba235efb82e9de", "target": "a2e10229636d23f13dbb"}, {"source": "87df80ba235efb82e9de", "target": "d7aded22d8ab1a5ff063"}, {"source": "87df80ba235efb82e9de", "target": "527111d4bf58cfbaa8f7"}, {"source": "87df80ba235efb82e9de", "target": "4119e1e519d687eb7ffc"}, {"source": "87df80ba235efb82e9de", "target": "aaa180c9379a902aba3e"}, {"source": "87df80ba235efb82e9de", "target": "5359c5b7020527a68e60"}, {"source": "87df80ba235efb82e9de", "target": "aa154290d2f39a1ab54b"}, {"source": "87df80ba235efb82e9de", "target": "ab47787d16eb43d8af34"}, {"source": "87df80ba235efb82e9de", "target": "185510fc80ac8a874cd1"}, {"source": "87df80ba235efb82e9de", "target": "7ae92335446d3ad639d0"}, {"source": "87df80ba235efb82e9de", "target": "5740eb91f7f2f4bef0ae"}, {"source": "87df80ba235efb82e9de", "target": "7d8f7ff7f31b2789b1f9"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "3884955"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "29d80cf22eda5b55e2c1"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "7c289a9dbf38ceb665d8"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "a45d78cc14704ced3f88"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "deb677307bfb906b38e6"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "a9711c2f39239465f30e"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "de369cc33c7c02015263"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "6e75e8d20da020b9de19"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "1727593e326e81b0951f"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "4db859d1e3fd8e8b8642"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "a2e10229636d23f13dbb"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "d7aded22d8ab1a5ff063"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "527111d4bf58cfbaa8f7"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "4119e1e519d687eb7ffc"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "aaa180c9379a902aba3e"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "5359c5b7020527a68e60"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "aa154290d2f39a1ab54b"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "ab47787d16eb43d8af34"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "185510fc80ac8a874cd1"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "7ae92335446d3ad639d0"}, {"source": "7d8f7ff7f31b2789b1f9", "target": "5740eb91f7f2f4bef0ae"}, {"source": "5740eb91f7f2f4bef0ae", "target": "3884955"}, {"source": "5740eb91f7f2f4bef0ae", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "5740eb91f7f2f4bef0ae", "target": "29d80cf22eda5b55e2c1"}, {"source": "5740eb91f7f2f4bef0ae", "target": "7c289a9dbf38ceb665d8"}, {"source": "5740eb91f7f2f4bef0ae", "target": "a45d78cc14704ced3f88"}, {"source": "5740eb91f7f2f4bef0ae", "target": "deb677307bfb906b38e6"}, {"source": "5740eb91f7f2f4bef0ae", "target": "a9711c2f39239465f30e"}, {"source": "5740eb91f7f2f4bef0ae", "target": "de369cc33c7c02015263"}, {"source": "5740eb91f7f2f4bef0ae", "target": "6e75e8d20da020b9de19"}, {"source": "5740eb91f7f2f4bef0ae", "target": "1727593e326e81b0951f"}, {"source": "5740eb91f7f2f4bef0ae", "target": "4db859d1e3fd8e8b8642"}, {"source": "5740eb91f7f2f4bef0ae", "target": "a2e10229636d23f13dbb"}, {"source": "5740eb91f7f2f4bef0ae", "target": "d7aded22d8ab1a5ff063"}, {"source": "5740eb91f7f2f4bef0ae", "target": "527111d4bf58cfbaa8f7"}, {"source": "5740eb91f7f2f4bef0ae", "target": "4119e1e519d687eb7ffc"}, {"source": "5740eb91f7f2f4bef0ae", "target": "aaa180c9379a902aba3e"}, {"source": "5740eb91f7f2f4bef0ae", "target": "5359c5b7020527a68e60"}, {"source": "5740eb91f7f2f4bef0ae", "target": "aa154290d2f39a1ab54b"}, {"source": "5740eb91f7f2f4bef0ae", "target": "ab47787d16eb43d8af34"}, {"source": "5740eb91f7f2f4bef0ae", "target": "185510fc80ac8a874cd1"}, {"source": "5740eb91f7f2f4bef0ae", "target": "7ae92335446d3ad639d0"}, {"source": "7ae92335446d3ad639d0", "target": "3884955"}, {"source": "7ae92335446d3ad639d0", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "7ae92335446d3ad639d0", "target": "29d80cf22eda5b55e2c1"}, {"source": "7ae92335446d3ad639d0", "target": "7c289a9dbf38ceb665d8"}, {"source": "7ae92335446d3ad639d0", "target": "a45d78cc14704ced3f88"}, {"source": "7ae92335446d3ad639d0", "target": "deb677307bfb906b38e6"}, {"source": "7ae92335446d3ad639d0", "target": "a9711c2f39239465f30e"}, {"source": "7ae92335446d3ad639d0", "target": "de369cc33c7c02015263"}, {"source": "7ae92335446d3ad639d0", "target": "6e75e8d20da020b9de19"}, {"source": "7ae92335446d3ad639d0", "target": "1727593e326e81b0951f"}, {"source": "7ae92335446d3ad639d0", "target": "4db859d1e3fd8e8b8642"}, {"source": "7ae92335446d3ad639d0", "target": "a2e10229636d23f13dbb"}, {"source": "7ae92335446d3ad639d0", "target": "d7aded22d8ab1a5ff063"}, {"source": "7ae92335446d3ad639d0", "target": "527111d4bf58cfbaa8f7"}, {"source": "7ae92335446d3ad639d0", "target": "4119e1e519d687eb7ffc"}, {"source": "7ae92335446d3ad639d0", "target": "aaa180c9379a902aba3e"}, {"source": "7ae92335446d3ad639d0", "target": "5359c5b7020527a68e60"}, {"source": "7ae92335446d3ad639d0", "target": "aa154290d2f39a1ab54b"}, {"source": "7ae92335446d3ad639d0", "target": "ab47787d16eb43d8af34"}, {"source": "7ae92335446d3ad639d0", "target": "185510fc80ac8a874cd1"}, {"source": "185510fc80ac8a874cd1", "target": "3884955"}, {"source": "185510fc80ac8a874cd1", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "185510fc80ac8a874cd1", "target": "29d80cf22eda5b55e2c1"}, {"source": "185510fc80ac8a874cd1", "target": "7c289a9dbf38ceb665d8"}, {"source": "185510fc80ac8a874cd1", "target": "a45d78cc14704ced3f88"}, {"source": "185510fc80ac8a874cd1", "target": "deb677307bfb906b38e6"}, {"source": "185510fc80ac8a874cd1", "target": "a9711c2f39239465f30e"}, {"source": "185510fc80ac8a874cd1", "target": "de369cc33c7c02015263"}, {"source": "185510fc80ac8a874cd1", "target": "6e75e8d20da020b9de19"}, {"source": "185510fc80ac8a874cd1", "target": "1727593e326e81b0951f"}, {"source": "185510fc80ac8a874cd1", "target": "4db859d1e3fd8e8b8642"}, {"source": "185510fc80ac8a874cd1", "target": "a2e10229636d23f13dbb"}, {"source": "185510fc80ac8a874cd1", "target": "d7aded22d8ab1a5ff063"}, {"source": "185510fc80ac8a874cd1", "target": "527111d4bf58cfbaa8f7"}, {"source": "185510fc80ac8a874cd1", "target": "4119e1e519d687eb7ffc"}, {"source": "185510fc80ac8a874cd1", "target": "aaa180c9379a902aba3e"}, {"source": "185510fc80ac8a874cd1", "target": "5359c5b7020527a68e60"}, {"source": "185510fc80ac8a874cd1", "target": "aa154290d2f39a1ab54b"}, {"source": "185510fc80ac8a874cd1", "target": "ab47787d16eb43d8af34"}, {"source": "ab47787d16eb43d8af34", "target": "3884955"}, {"source": "ab47787d16eb43d8af34", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "ab47787d16eb43d8af34", "target": "29d80cf22eda5b55e2c1"}, {"source": "ab47787d16eb43d8af34", "target": "7c289a9dbf38ceb665d8"}, {"source": "ab47787d16eb43d8af34", "target": "a45d78cc14704ced3f88"}, {"source": "ab47787d16eb43d8af34", "target": "deb677307bfb906b38e6"}, {"source": "ab47787d16eb43d8af34", "target": "a9711c2f39239465f30e"}, {"source": "ab47787d16eb43d8af34", "target": "de369cc33c7c02015263"}, {"source": "ab47787d16eb43d8af34", "target": "6e75e8d20da020b9de19"}, {"source": "ab47787d16eb43d8af34", "target": "1727593e326e81b0951f"}, {"source": "ab47787d16eb43d8af34", "target": "4db859d1e3fd8e8b8642"}, {"source": "ab47787d16eb43d8af34", "target": "a2e10229636d23f13dbb"}, {"source": "ab47787d16eb43d8af34", "target": "d7aded22d8ab1a5ff063"}, {"source": "ab47787d16eb43d8af34", "target": "527111d4bf58cfbaa8f7"}, {"source": "ab47787d16eb43d8af34", "target": "4119e1e519d687eb7ffc"}, {"source": "ab47787d16eb43d8af34", "target": "aaa180c9379a902aba3e"}, {"source": "ab47787d16eb43d8af34", "target": "5359c5b7020527a68e60"}, {"source": "ab47787d16eb43d8af34", "target": "aa154290d2f39a1ab54b"}, {"source": "aa154290d2f39a1ab54b", "target": "3884955"}, {"source": "aa154290d2f39a1ab54b", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "aa154290d2f39a1ab54b", "target": "29d80cf22eda5b55e2c1"}, {"source": "aa154290d2f39a1ab54b", "target": "7c289a9dbf38ceb665d8"}, {"source": "aa154290d2f39a1ab54b", "target": "a45d78cc14704ced3f88"}, {"source": "aa154290d2f39a1ab54b", "target": "deb677307bfb906b38e6"}, {"source": "aa154290d2f39a1ab54b", "target": "a9711c2f39239465f30e"}, {"source": "aa154290d2f39a1ab54b", "target": "de369cc33c7c02015263"}, {"source": "aa154290d2f39a1ab54b", "target": "6e75e8d20da020b9de19"}, {"source": "aa154290d2f39a1ab54b", "target": "1727593e326e81b0951f"}, {"source": "aa154290d2f39a1ab54b", "target": "4db859d1e3fd8e8b8642"}, {"source": "aa154290d2f39a1ab54b", "target": "a2e10229636d23f13dbb"}, {"source": "aa154290d2f39a1ab54b", "target": "d7aded22d8ab1a5ff063"}, {"source": "aa154290d2f39a1ab54b", "target": "527111d4bf58cfbaa8f7"}, {"source": "aa154290d2f39a1ab54b", "target": "4119e1e519d687eb7ffc"}, {"source": "aa154290d2f39a1ab54b", "target": "aaa180c9379a902aba3e"}, {"source": "aa154290d2f39a1ab54b", "target": "5359c5b7020527a68e60"}, {"source": "5359c5b7020527a68e60", "target": "3884955"}, {"source": "5359c5b7020527a68e60", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "5359c5b7020527a68e60", "target": "29d80cf22eda5b55e2c1"}, {"source": "5359c5b7020527a68e60", "target": "7c289a9dbf38ceb665d8"}, {"source": "5359c5b7020527a68e60", "target": "a45d78cc14704ced3f88"}, {"source": "5359c5b7020527a68e60", "target": "deb677307bfb906b38e6"}, {"source": "5359c5b7020527a68e60", "target": "a9711c2f39239465f30e"}, {"source": "5359c5b7020527a68e60", "target": "de369cc33c7c02015263"}, {"source": "5359c5b7020527a68e60", "target": "6e75e8d20da020b9de19"}, {"source": "5359c5b7020527a68e60", "target": "1727593e326e81b0951f"}, {"source": "5359c5b7020527a68e60", "target": "4db859d1e3fd8e8b8642"}, {"source": "5359c5b7020527a68e60", "target": "a2e10229636d23f13dbb"}, {"source": "5359c5b7020527a68e60", "target": "d7aded22d8ab1a5ff063"}, {"source": "5359c5b7020527a68e60", "target": "527111d4bf58cfbaa8f7"}, {"source": "5359c5b7020527a68e60", "target": "4119e1e519d687eb7ffc"}, {"source": "5359c5b7020527a68e60", "target": "aaa180c9379a902aba3e"}, {"source": "aaa180c9379a902aba3e", "target": "3884955"}, {"source": "aaa180c9379a902aba3e", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "aaa180c9379a902aba3e", "target": "29d80cf22eda5b55e2c1"}, {"source": "aaa180c9379a902aba3e", "target": "7c289a9dbf38ceb665d8"}, {"source": "aaa180c9379a902aba3e", "target": "a45d78cc14704ced3f88"}, {"source": "aaa180c9379a902aba3e", "target": "deb677307bfb906b38e6"}, {"source": "aaa180c9379a902aba3e", "target": "a9711c2f39239465f30e"}, {"source": "aaa180c9379a902aba3e", "target": "de369cc33c7c02015263"}, {"source": "aaa180c9379a902aba3e", "target": "6e75e8d20da020b9de19"}, {"source": "aaa180c9379a902aba3e", "target": "1727593e326e81b0951f"}, {"source": "aaa180c9379a902aba3e", "target": "4db859d1e3fd8e8b8642"}, {"source": "aaa180c9379a902aba3e", "target": "a2e10229636d23f13dbb"}, {"source": "aaa180c9379a902aba3e", "target": "d7aded22d8ab1a5ff063"}, {"source": "aaa180c9379a902aba3e", "target": "527111d4bf58cfbaa8f7"}, {"source": "aaa180c9379a902aba3e", "target": "4119e1e519d687eb7ffc"}, {"source": "4119e1e519d687eb7ffc", "target": "3884955"}, {"source": "4119e1e519d687eb7ffc", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "4119e1e519d687eb7ffc", "target": "29d80cf22eda5b55e2c1"}, {"source": "4119e1e519d687eb7ffc", "target": "7c289a9dbf38ceb665d8"}, {"source": "4119e1e519d687eb7ffc", "target": "a45d78cc14704ced3f88"}, {"source": "4119e1e519d687eb7ffc", "target": "deb677307bfb906b38e6"}, {"source": "4119e1e519d687eb7ffc", "target": "a9711c2f39239465f30e"}, {"source": "4119e1e519d687eb7ffc", "target": "de369cc33c7c02015263"}, {"source": "4119e1e519d687eb7ffc", "target": "6e75e8d20da020b9de19"}, {"source": "4119e1e519d687eb7ffc", "target": "1727593e326e81b0951f"}, {"source": "4119e1e519d687eb7ffc", "target": "4db859d1e3fd8e8b8642"}, {"source": "4119e1e519d687eb7ffc", "target": "a2e10229636d23f13dbb"}, {"source": "4119e1e519d687eb7ffc", "target": "d7aded22d8ab1a5ff063"}, {"source": "4119e1e519d687eb7ffc", "target": "527111d4bf58cfbaa8f7"}, {"source": "527111d4bf58cfbaa8f7", "target": "3884955"}, {"source": "527111d4bf58cfbaa8f7", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "527111d4bf58cfbaa8f7", "target": "29d80cf22eda5b55e2c1"}, {"source": "527111d4bf58cfbaa8f7", "target": "7c289a9dbf38ceb665d8"}, {"source": "527111d4bf58cfbaa8f7", "target": "a45d78cc14704ced3f88"}, {"source": "527111d4bf58cfbaa8f7", "target": "deb677307bfb906b38e6"}, {"source": "527111d4bf58cfbaa8f7", "target": "a9711c2f39239465f30e"}, {"source": "527111d4bf58cfbaa8f7", "target": "de369cc33c7c02015263"}, {"source": "527111d4bf58cfbaa8f7", "target": "6e75e8d20da020b9de19"}, {"source": "527111d4bf58cfbaa8f7", "target": "1727593e326e81b0951f"}, {"source": "527111d4bf58cfbaa8f7", "target": "4db859d1e3fd8e8b8642"}, {"source": "527111d4bf58cfbaa8f7", "target": "a2e10229636d23f13dbb"}, {"source": "527111d4bf58cfbaa8f7", "target": "d7aded22d8ab1a5ff063"}, {"source": "d7aded22d8ab1a5ff063", "target": "3884955"}, {"source": "d7aded22d8ab1a5ff063", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "d7aded22d8ab1a5ff063", "target": "29d80cf22eda5b55e2c1"}, {"source": "d7aded22d8ab1a5ff063", "target": "7c289a9dbf38ceb665d8"}, {"source": "d7aded22d8ab1a5ff063", "target": "a45d78cc14704ced3f88"}, {"source": "d7aded22d8ab1a5ff063", "target": "deb677307bfb906b38e6"}, {"source": "d7aded22d8ab1a5ff063", "target": "a9711c2f39239465f30e"}, {"source": "d7aded22d8ab1a5ff063", "target": "de369cc33c7c02015263"}, {"source": "d7aded22d8ab1a5ff063", "target": "6e75e8d20da020b9de19"}, {"source": "d7aded22d8ab1a5ff063", "target": "1727593e326e81b0951f"}, {"source": "d7aded22d8ab1a5ff063", "target": "4db859d1e3fd8e8b8642"}, {"source": "d7aded22d8ab1a5ff063", "target": "a2e10229636d23f13dbb"}, {"source": "a2e10229636d23f13dbb", "target": "3884955"}, {"source": "a2e10229636d23f13dbb", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "a2e10229636d23f13dbb", "target": "29d80cf22eda5b55e2c1"}, {"source": "a2e10229636d23f13dbb", "target": "7c289a9dbf38ceb665d8"}, {"source": "a2e10229636d23f13dbb", "target": "a45d78cc14704ced3f88"}, {"source": "a2e10229636d23f13dbb", "target": "deb677307bfb906b38e6"}, {"source": "a2e10229636d23f13dbb", "target": "a9711c2f39239465f30e"}, {"source": "a2e10229636d23f13dbb", "target": "de369cc33c7c02015263"}, {"source": "a2e10229636d23f13dbb", "target": "6e75e8d20da020b9de19"}, {"source": "a2e10229636d23f13dbb", "target": "1727593e326e81b0951f"}, {"source": "a2e10229636d23f13dbb", "target": "4db859d1e3fd8e8b8642"}, {"source": "4db859d1e3fd8e8b8642", "target": "3884955"}, {"source": "4db859d1e3fd8e8b8642", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "4db859d1e3fd8e8b8642", "target": "29d80cf22eda5b55e2c1"}, {"source": "4db859d1e3fd8e8b8642", "target": "7c289a9dbf38ceb665d8"}, {"source": "4db859d1e3fd8e8b8642", "target": "a45d78cc14704ced3f88"}, {"source": "4db859d1e3fd8e8b8642", "target": "deb677307bfb906b38e6"}, {"source": "4db859d1e3fd8e8b8642", "target": "a9711c2f39239465f30e"}, {"source": "4db859d1e3fd8e8b8642", "target": "de369cc33c7c02015263"}, {"source": "4db859d1e3fd8e8b8642", "target": "6e75e8d20da020b9de19"}, {"source": "4db859d1e3fd8e8b8642", "target": "1727593e326e81b0951f"}, {"source": "1727593e326e81b0951f", "target": "3884955"}, {"source": "1727593e326e81b0951f", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "1727593e326e81b0951f", "target": "29d80cf22eda5b55e2c1"}, {"source": "1727593e326e81b0951f", "target": "7c289a9dbf38ceb665d8"}, {"source": "1727593e326e81b0951f", "target": "a45d78cc14704ced3f88"}, {"source": "1727593e326e81b0951f", "target": "deb677307bfb906b38e6"}, {"source": "1727593e326e81b0951f", "target": "a9711c2f39239465f30e"}, {"source": "1727593e326e81b0951f", "target": "de369cc33c7c02015263"}, {"source": "1727593e326e81b0951f", "target": "6e75e8d20da020b9de19"}, {"source": "6e75e8d20da020b9de19", "target": "3884955"}, {"source": "6e75e8d20da020b9de19", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "6e75e8d20da020b9de19", "target": "29d80cf22eda5b55e2c1"}, {"source": "6e75e8d20da020b9de19", "target": "7c289a9dbf38ceb665d8"}, {"source": "6e75e8d20da020b9de19", "target": "a45d78cc14704ced3f88"}, {"source": "6e75e8d20da020b9de19", "target": "deb677307bfb906b38e6"}, {"source": "6e75e8d20da020b9de19", "target": "a9711c2f39239465f30e"}, {"source": "6e75e8d20da020b9de19", "target": "de369cc33c7c02015263"}, {"source": "de369cc33c7c02015263", "target": "3884955"}, {"source": "de369cc33c7c02015263", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "de369cc33c7c02015263", "target": "29d80cf22eda5b55e2c1"}, {"source": "de369cc33c7c02015263", "target": "7c289a9dbf38ceb665d8"}, {"source": "de369cc33c7c02015263", "target": "a45d78cc14704ced3f88"}, {"source": "de369cc33c7c02015263", "target": "deb677307bfb906b38e6"}, {"source": "de369cc33c7c02015263", "target": "a9711c2f39239465f30e"}, {"source": "a9711c2f39239465f30e", "target": "3884955"}, {"source": "a9711c2f39239465f30e", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "a9711c2f39239465f30e", "target": "29d80cf22eda5b55e2c1"}, {"source": "a9711c2f39239465f30e", "target": "7c289a9dbf38ceb665d8"}, {"source": "a9711c2f39239465f30e", "target": "a45d78cc14704ced3f88"}, {"source": "a9711c2f39239465f30e", "target": "deb677307bfb906b38e6"}, {"source": "deb677307bfb906b38e6", "target": "3884955"}, {"source": "deb677307bfb906b38e6", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "deb677307bfb906b38e6", "target": "29d80cf22eda5b55e2c1"}, {"source": "deb677307bfb906b38e6", "target": "7c289a9dbf38ceb665d8"}, {"source": "deb677307bfb906b38e6", "target": "a45d78cc14704ced3f88"}, {"source": "a45d78cc14704ced3f88", "target": "3884955"}, {"source": "a45d78cc14704ced3f88", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "a45d78cc14704ced3f88", "target": "29d80cf22eda5b55e2c1"}, {"source": "a45d78cc14704ced3f88", "target": "7c289a9dbf38ceb665d8"}, {"source": "7c289a9dbf38ceb665d8", "target": "3884955"}, {"source": "7c289a9dbf38ceb665d8", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "7c289a9dbf38ceb665d8", "target": "29d80cf22eda5b55e2c1"}, {"source": "29d80cf22eda5b55e2c1", "target": "3884955"}, {"source": "29d80cf22eda5b55e2c1", "target": "9d9ccdf86fb59ab6c6e0"}, {"source": "9d9ccdf86fb59ab6c6e0", "target": "3884955"}, {"source": "9f2c2f3e48fc6b3e51ff", "target": "3884955"}, {"source": "9f2c2f3e48fc6b3e51ff", "target": "ac5b6c28390fb4af7a04"}, {"source": "ac5b6c28390fb4af7a04", "target": "3884955"}, {"source": "f00d5c23c2d79f37653e", "target": "3884955"}, {"source": "f00d5c23c2d79f37653e", "target": "8a6e0ca67f054dd9ef29"}, {"source": "f00d5c23c2d79f37653e", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "f00d5c23c2d79f37653e", "target": "1f02ba8c57e84bbf0d08"}, {"source": "f00d5c23c2d79f37653e", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "f00d5c23c2d79f37653e", "target": "7b912a3693e7dee2633e"}, {"source": "f00d5c23c2d79f37653e", "target": "0dc207c08decd57319f3"}, {"source": "f00d5c23c2d79f37653e", "target": "3ffc84f3def0025a4243"}, {"source": "f00d5c23c2d79f37653e", "target": "333c0477264183d25c12"}, {"source": "f00d5c23c2d79f37653e", "target": "a69684b04fde3f873ee8"}, {"source": "f00d5c23c2d79f37653e", "target": "10f43d81d7fa62872872"}, {"source": "10f43d81d7fa62872872", "target": "3884955"}, {"source": "10f43d81d7fa62872872", "target": "8a6e0ca67f054dd9ef29"}, {"source": "10f43d81d7fa62872872", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "10f43d81d7fa62872872", "target": "1f02ba8c57e84bbf0d08"}, {"source": "10f43d81d7fa62872872", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "10f43d81d7fa62872872", "target": "7b912a3693e7dee2633e"}, {"source": "10f43d81d7fa62872872", "target": "0dc207c08decd57319f3"}, {"source": "10f43d81d7fa62872872", "target": "3ffc84f3def0025a4243"}, {"source": "10f43d81d7fa62872872", "target": "333c0477264183d25c12"}, {"source": "10f43d81d7fa62872872", "target": "a69684b04fde3f873ee8"}, {"source": "a69684b04fde3f873ee8", "target": "3884955"}, {"source": "a69684b04fde3f873ee8", "target": "8a6e0ca67f054dd9ef29"}, {"source": "a69684b04fde3f873ee8", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "a69684b04fde3f873ee8", "target": "1f02ba8c57e84bbf0d08"}, {"source": "a69684b04fde3f873ee8", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "a69684b04fde3f873ee8", "target": "7b912a3693e7dee2633e"}, {"source": "a69684b04fde3f873ee8", "target": "0dc207c08decd57319f3"}, {"source": "a69684b04fde3f873ee8", "target": "3ffc84f3def0025a4243"}, {"source": "a69684b04fde3f873ee8", "target": "333c0477264183d25c12"}, {"source": "333c0477264183d25c12", "target": "3884955"}, {"source": "333c0477264183d25c12", "target": "8a6e0ca67f054dd9ef29"}, {"source": "333c0477264183d25c12", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "333c0477264183d25c12", "target": "1f02ba8c57e84bbf0d08"}, {"source": "333c0477264183d25c12", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "333c0477264183d25c12", "target": "7b912a3693e7dee2633e"}, {"source": "333c0477264183d25c12", "target": "0dc207c08decd57319f3"}, {"source": "333c0477264183d25c12", "target": "3ffc84f3def0025a4243"}, {"source": "3ffc84f3def0025a4243", "target": "3884955"}, {"source": "3ffc84f3def0025a4243", "target": "8a6e0ca67f054dd9ef29"}, {"source": "3ffc84f3def0025a4243", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "3ffc84f3def0025a4243", "target": "1f02ba8c57e84bbf0d08"}, {"source": "3ffc84f3def0025a4243", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "3ffc84f3def0025a4243", "target": "7b912a3693e7dee2633e"}, {"source": "3ffc84f3def0025a4243", "target": "0dc207c08decd57319f3"}, {"source": "0dc207c08decd57319f3", "target": "3884955"}, {"source": "0dc207c08decd57319f3", "target": "8a6e0ca67f054dd9ef29"}, {"source": "0dc207c08decd57319f3", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "0dc207c08decd57319f3", "target": "1f02ba8c57e84bbf0d08"}, {"source": "0dc207c08decd57319f3", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "0dc207c08decd57319f3", "target": "7b912a3693e7dee2633e"}, {"source": "7b912a3693e7dee2633e", "target": "3884955"}, {"source": "7b912a3693e7dee2633e", "target": "8a6e0ca67f054dd9ef29"}, {"source": "7b912a3693e7dee2633e", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "7b912a3693e7dee2633e", "target": "1f02ba8c57e84bbf0d08"}, {"source": "7b912a3693e7dee2633e", "target": "d3a03c0a140b6d4b4e2c"}, {"source": "d3a03c0a140b6d4b4e2c", "target": "3884955"}, {"source": "d3a03c0a140b6d4b4e2c", "target": "8a6e0ca67f054dd9ef29"}, {"source": "d3a03c0a140b6d4b4e2c", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "d3a03c0a140b6d4b4e2c", "target": "1f02ba8c57e84bbf0d08"}, {"source": "1f02ba8c57e84bbf0d08", "target": "3884955"}, {"source": "1f02ba8c57e84bbf0d08", "target": "8a6e0ca67f054dd9ef29"}, {"source": "1f02ba8c57e84bbf0d08", "target": "a2c2ab6da0a0aa3d8fd1"}, {"source": "a2c2ab6da0a0aa3d8fd1", "target": "3884955"}, {"source": "a2c2ab6da0a0aa3d8fd1", "target": "8a6e0ca67f054dd9ef29"}, {"source": "8a6e0ca67f054dd9ef29", "target": "3884955"}, {"source": "4df06aacdf53e24c90b7", "target": "3884955"}, {"source": "4df06aacdf53e24c90b7", "target": "2fbe51dcbc0d27cf897b"}, {"source": "4df06aacdf53e24c90b7", "target": "ea72de53eb3daad14f73"}, {"source": "4df06aacdf53e24c90b7", "target": "f3e6415f79b078b75fe4"}, {"source": "4df06aacdf53e24c90b7", "target": "d3b1e127741d5be5bbc3"}, {"source": "4df06aacdf53e24c90b7", "target": "fea2240ea499f36b7ec4"}, {"source": "4df06aacdf53e24c90b7", "target": "d721b55071e31f8da8c3"}, {"source": "d721b55071e31f8da8c3", "target": "3884955"}, {"source": "d721b55071e31f8da8c3", "target": "2fbe51dcbc0d27cf897b"}, {"source": "d721b55071e31f8da8c3", "target": "ea72de53eb3daad14f73"}, {"source": "d721b55071e31f8da8c3", "target": "f3e6415f79b078b75fe4"}, {"source": "d721b55071e31f8da8c3", "target": "d3b1e127741d5be5bbc3"}, {"source": "d721b55071e31f8da8c3", "target": "fea2240ea499f36b7ec4"}, {"source": "fea2240ea499f36b7ec4", "target": "3884955"}, {"source": "fea2240ea499f36b7ec4", "target": "2fbe51dcbc0d27cf897b"}, {"source": "fea2240ea499f36b7ec4", "target": "ea72de53eb3daad14f73"}, {"source": "fea2240ea499f36b7ec4", "target": "f3e6415f79b078b75fe4"}, {"source": "fea2240ea499f36b7ec4", "target": "d3b1e127741d5be5bbc3"}, {"source": "d3b1e127741d5be5bbc3", "target": "3884955"}, {"source": "d3b1e127741d5be5bbc3", "target": "2fbe51dcbc0d27cf897b"}, {"source": "d3b1e127741d5be5bbc3", "target": "ea72de53eb3daad14f73"}, {"source": "d3b1e127741d5be5bbc3", "target": "f3e6415f79b078b75fe4"}, {"source": "f3e6415f79b078b75fe4", "target": "3884955"}, {"source": "f3e6415f79b078b75fe4", "target": "2fbe51dcbc0d27cf897b"}, {"source": "f3e6415f79b078b75fe4", "target": "ea72de53eb3daad14f73"}, {"source": "ea72de53eb3daad14f73", "target": "3884955"}, {"source": "ea72de53eb3daad14f73", "target": "2fbe51dcbc0d27cf897b"}, {"source": "2fbe51dcbc0d27cf897b", "target": "3884955"}, {"source": "e503f9cd7655acf5d9a7", "target": "00e295827c51ffa80104"}, {"source": "e503f9cd7655acf5d9a7", "target": "9b23673c0786fa5b1a75"}, {"source": "9b23673c0786fa5b1a75", "target": "00e295827c51ffa80104"}, {"source": "28c466de83d5100f009e", "target": "3884955"}, {"source": "28c466de83d5100f009e", "target": "cf3031700f42670dccf6"}, {"source": "28c466de83d5100f009e", "target": "97a40f1c0684bc5bd0d5"}, {"source": "28c466de83d5100f009e", "target": "81d0c04cbd78c95cc674"}, {"source": "28c466de83d5100f009e", "target": "a807f2ba95e19db2a0f5"}, {"source": "28c466de83d5100f009e", "target": "70b669815dbad375ea75"}, {"source": "70b669815dbad375ea75", "target": "3884955"}, {"source": "70b669815dbad375ea75", "target": "cf3031700f42670dccf6"}, {"source": "70b669815dbad375ea75", "target": "97a40f1c0684bc5bd0d5"}, {"source": "70b669815dbad375ea75", "target": "81d0c04cbd78c95cc674"}, {"source": "70b669815dbad375ea75", "target": "a807f2ba95e19db2a0f5"}, {"source": "a807f2ba95e19db2a0f5", "target": "3884955"}, {"source": "a807f2ba95e19db2a0f5", "target": "cf3031700f42670dccf6"}, {"source": "a807f2ba95e19db2a0f5", "target": "97a40f1c0684bc5bd0d5"}, {"source": "a807f2ba95e19db2a0f5", "target": "81d0c04cbd78c95cc674"}, {"source": "81d0c04cbd78c95cc674", "target": "3884955"}, {"source": "81d0c04cbd78c95cc674", "target": "cf3031700f42670dccf6"}, {"source": "81d0c04cbd78c95cc674", "target": "97a40f1c0684bc5bd0d5"}, {"source": "97a40f1c0684bc5bd0d5", "target": "3884955"}, {"source": "97a40f1c0684bc5bd0d5", "target": "cf3031700f42670dccf6"}, {"source": "cf3031700f42670dccf6", "target": "3884955"}, {"source": "a4658f02960b71612a57", "target": "3884955"}, {"source": "a4658f02960b71612a57", "target": "cf29a41485972f2a15cf"}, {"source": "c81883c957e8537be10f", "target": "3884955"}, {"source": "c81883c957e8537be10f", "target": "cf29a41485972f2a15cf"}, {"source": "c81883c957e8537be10f", "target": "4e9a0662dc81944593f9"}, {"source": "4e9a0662dc81944593f9", "target": "3884955"}, {"source": "4e9a0662dc81944593f9", "target": "cf29a41485972f2a15cf"}, {"source": "cf29a41485972f2a15cf", "target": "3884955"}, {"source": "a0e1406d6bb56ff70269", "target": "3884955"}, {"source": "a0e1406d6bb56ff70269", "target": "1aef402d919da5d776b5"}, {"source": "a0e1406d6bb56ff70269", "target": "fdb820d9d828400cb227"}, {"source": "fdb820d9d828400cb227", "target": "3884955"}, {"source": "fdb820d9d828400cb227", "target": "1aef402d919da5d776b5"}, {"source": "1aef402d919da5d776b5", "target": "3884955"}, {"source": "9ccba191aa29181821d2", "target": "ed2a8aff3d202713525f"}, {"source": "965cf81685bc8a9021a8", "target": "3885304"}, {"source": "0da60479257cc3d90a62", "target": "4349486"}, {"source": "7ac51fe69e41fc6c606a", "target": "3887235."}, {"source": "4384949715de645a77ef", "target": "4689130"}, {"source": "4384949715de645a77ef", "target": "60e84ea5cdffa49356e4"}, {"source": "4384949715de645a77ef", "target": "384dd445a6a7ae9f6086"}, {"source": "384dd445a6a7ae9f6086", "target": "4689130"}, {"source": "384dd445a6a7ae9f6086", "target": "60e84ea5cdffa49356e4"}, {"source": "60e84ea5cdffa49356e4", "target": "4689130"}, {"source": "da81217dee2d551d1411", "target": "6738109"}, {"source": "da81217dee2d551d1411", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "da81217dee2d551d1411", "target": "23e98ddcc9ff35b6517f"}, {"source": "da81217dee2d551d1411", "target": "7b807fa90c485937b2ea"}, {"source": "da81217dee2d551d1411", "target": "bf6871220d8db6efd10f"}, {"source": "da81217dee2d551d1411", "target": "b378b3a76c1e12038c43"}, {"source": "da81217dee2d551d1411", "target": "74cc63dc0a9ae18297de"}, {"source": "da81217dee2d551d1411", "target": "96a3758777f9bb5e91c6"}, {"source": "da81217dee2d551d1411", "target": "981721947501c7fc2fbc"}, {"source": "da81217dee2d551d1411", "target": "5c4e011cc543373478e8"}, {"source": "da81217dee2d551d1411", "target": "b95717283160c8c293e0"}, {"source": "da81217dee2d551d1411", "target": "cdc8b048f1e7a56fae0e"}, {"source": "cdc8b048f1e7a56fae0e", "target": "6738109"}, {"source": "cdc8b048f1e7a56fae0e", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "cdc8b048f1e7a56fae0e", "target": "23e98ddcc9ff35b6517f"}, {"source": "cdc8b048f1e7a56fae0e", "target": "7b807fa90c485937b2ea"}, {"source": "cdc8b048f1e7a56fae0e", "target": "bf6871220d8db6efd10f"}, {"source": "cdc8b048f1e7a56fae0e", "target": "b378b3a76c1e12038c43"}, {"source": "cdc8b048f1e7a56fae0e", "target": "74cc63dc0a9ae18297de"}, {"source": "cdc8b048f1e7a56fae0e", "target": "96a3758777f9bb5e91c6"}, {"source": "cdc8b048f1e7a56fae0e", "target": "981721947501c7fc2fbc"}, {"source": "cdc8b048f1e7a56fae0e", "target": "5c4e011cc543373478e8"}, {"source": "cdc8b048f1e7a56fae0e", "target": "b95717283160c8c293e0"}, {"source": "b95717283160c8c293e0", "target": "6738109"}, {"source": "b95717283160c8c293e0", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "b95717283160c8c293e0", "target": "23e98ddcc9ff35b6517f"}, {"source": "b95717283160c8c293e0", "target": "7b807fa90c485937b2ea"}, {"source": "b95717283160c8c293e0", "target": "bf6871220d8db6efd10f"}, {"source": "b95717283160c8c293e0", "target": "b378b3a76c1e12038c43"}, {"source": "b95717283160c8c293e0", "target": "74cc63dc0a9ae18297de"}, {"source": "b95717283160c8c293e0", "target": "96a3758777f9bb5e91c6"}, {"source": "b95717283160c8c293e0", "target": "981721947501c7fc2fbc"}, {"source": "b95717283160c8c293e0", "target": "5c4e011cc543373478e8"}, {"source": "5c4e011cc543373478e8", "target": "6738109"}, {"source": "5c4e011cc543373478e8", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "5c4e011cc543373478e8", "target": "23e98ddcc9ff35b6517f"}, {"source": "5c4e011cc543373478e8", "target": "7b807fa90c485937b2ea"}, {"source": "5c4e011cc543373478e8", "target": "bf6871220d8db6efd10f"}, {"source": "5c4e011cc543373478e8", "target": "b378b3a76c1e12038c43"}, {"source": "5c4e011cc543373478e8", "target": "74cc63dc0a9ae18297de"}, {"source": "5c4e011cc543373478e8", "target": "96a3758777f9bb5e91c6"}, {"source": "5c4e011cc543373478e8", "target": "981721947501c7fc2fbc"}, {"source": "981721947501c7fc2fbc", "target": "6738109"}, {"source": "981721947501c7fc2fbc", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "981721947501c7fc2fbc", "target": "23e98ddcc9ff35b6517f"}, {"source": "981721947501c7fc2fbc", "target": "7b807fa90c485937b2ea"}, {"source": "981721947501c7fc2fbc", "target": "bf6871220d8db6efd10f"}, {"source": "981721947501c7fc2fbc", "target": "b378b3a76c1e12038c43"}, {"source": "981721947501c7fc2fbc", "target": "74cc63dc0a9ae18297de"}, {"source": "981721947501c7fc2fbc", "target": "96a3758777f9bb5e91c6"}, {"source": "96a3758777f9bb5e91c6", "target": "6738109"}, {"source": "96a3758777f9bb5e91c6", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "96a3758777f9bb5e91c6", "target": "23e98ddcc9ff35b6517f"}, {"source": "96a3758777f9bb5e91c6", "target": "7b807fa90c485937b2ea"}, {"source": "96a3758777f9bb5e91c6", "target": "bf6871220d8db6efd10f"}, {"source": "96a3758777f9bb5e91c6", "target": "b378b3a76c1e12038c43"}, {"source": "96a3758777f9bb5e91c6", "target": "74cc63dc0a9ae18297de"}, {"source": "74cc63dc0a9ae18297de", "target": "6738109"}, {"source": "74cc63dc0a9ae18297de", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "74cc63dc0a9ae18297de", "target": "23e98ddcc9ff35b6517f"}, {"source": "74cc63dc0a9ae18297de", "target": "7b807fa90c485937b2ea"}, {"source": "74cc63dc0a9ae18297de", "target": "bf6871220d8db6efd10f"}, {"source": "74cc63dc0a9ae18297de", "target": "b378b3a76c1e12038c43"}, {"source": "b378b3a76c1e12038c43", "target": "6738109"}, {"source": "b378b3a76c1e12038c43", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "b378b3a76c1e12038c43", "target": "23e98ddcc9ff35b6517f"}, {"source": "b378b3a76c1e12038c43", "target": "7b807fa90c485937b2ea"}, {"source": "b378b3a76c1e12038c43", "target": "bf6871220d8db6efd10f"}, {"source": "bf6871220d8db6efd10f", "target": "6738109"}, {"source": "bf6871220d8db6efd10f", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "bf6871220d8db6efd10f", "target": "23e98ddcc9ff35b6517f"}, {"source": "bf6871220d8db6efd10f", "target": "7b807fa90c485937b2ea"}, {"source": "7b807fa90c485937b2ea", "target": "6738109"}, {"source": "7b807fa90c485937b2ea", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "7b807fa90c485937b2ea", "target": "23e98ddcc9ff35b6517f"}, {"source": "23e98ddcc9ff35b6517f", "target": "6738109"}, {"source": "23e98ddcc9ff35b6517f", "target": "1f1ab0ec1cc8cee5970f"}, {"source": "1f1ab0ec1cc8cee5970f", "target": "6738109"}, {"source": "becd7b34d93b2d80d828", "target": "5ada6a7d00115f0e2f1e"}, {"source": "ee8d0ab88229d0e07f7f", "target": "a4679ee453aa9357977c"}, {"source": "5fbda7f410d31da35e42", "target": "4063318"}, {"source": "5b3161f6be8bd2baac52", "target": "019307194abe050e1117"}, {"source": "5b3161f6be8bd2baac52", "target": "f810911f5f84b94f2e3e"}, {"source": "f810911f5f84b94f2e3e", "target": "019307194abe050e1117"}, {"source": "23ca2f006b720c784fc2", "target": "a4530669c16faced0d57"}, {"source": "23ca2f006b720c784fc2", "target": "ea9be02dff5b6c3a18e2"}, {"source": "23ca2f006b720c784fc2", "target": "febe139365a1819a6953"}, {"source": "23ca2f006b720c784fc2", "target": "9704fe9249dfbc5c7da9"}, {"source": "9704fe9249dfbc5c7da9", "target": "a4530669c16faced0d57"}, {"source": "9704fe9249dfbc5c7da9", "target": "ea9be02dff5b6c3a18e2"}, {"source": "9704fe9249dfbc5c7da9", "target": "febe139365a1819a6953"}, {"source": "febe139365a1819a6953", "target": "a4530669c16faced0d57"}, {"source": "febe139365a1819a6953", "target": "ea9be02dff5b6c3a18e2"}, {"source": "ea9be02dff5b6c3a18e2", "target": "a4530669c16faced0d57"}, {"source": "78e3a730e77c677e168c", "target": "4562ac558aa8fb77269c"}, {"source": "8c462789ffbb04351a11", "target": "89e1e69c888e8241ed92"}, {"source": "8c462789ffbb04351a11", "target": "5118ba2eb78edfcf645e"}, {"source": "8c462789ffbb04351a11", "target": "1e6da47724c39156adb3"}, {"source": "8c462789ffbb04351a11", "target": "0a8fe1caa2ac025c8e86"}, {"source": "77ddbd1e08c11e18a0f7", "target": "6185069"}, {"source": "77ddbd1e08c11e18a0f7", "target": "f73384946e17e4b1b46a"}, {"source": "08fc85ae18559fe95ad3", "target": "ce9c2deb92042df13179"}, {"source": "08fc85ae18559fe95ad3", "target": "6958e230e69a377620e3"}, {"source": "3ad7359dc3d6f0d25734", "target": "4062045"}, {"source": "a82271eb40bed573f921", "target": "4c7a8a89a25bd86a61a8"}, {"source": "f6ddea14600dc5093506", "target": "4d62de0846a2554b113b"}, {"source": "4d62de0846a2554b113b", "target": "2dca5c7c84eb5d3c72a8"}, {"source": "23460413ea085c024bf8", "target": "d1b8c76c16ceab5d3b45"}, {"source": "6a922b0fb6abbb815158", "target": "1071413ad6b5b542a1a3"}, {"source": "6a922b0fb6abbb815158", "target": "03edaa335f93b5a9ee57"}, {"source": "6a922b0fb6abbb815158", "target": "b100d04bda697f95f246"}, {"source": "1071413ad6b5b542a1a3", "target": "03edaa335f93b5a9ee57"}, {"source": "1071413ad6b5b542a1a3", "target": "b100d04bda697f95f246"}, {"source": "03edaa335f93b5a9ee57", "target": "b100d04bda697f95f246"}, {"source": "03edaa335f93b5a9ee57", "target": "4188334"}, {"source": "03edaa335f93b5a9ee57", "target": "4090846"}, {"source": "38149d05ea247cbcebb1", "target": "7833311"}, {"source": "936ef76e148c655b1058", "target": "5d4eac580beb1fbb0031"}, {"source": "936ef76e148c655b1058", "target": "7fdc5a209906a8e6789b"}, {"source": "7fdc5a209906a8e6789b", "target": "5d4eac580beb1fbb0031"}, {"source": "e9b3bfc6f43b4bd237e2", "target": "3750941"}, {"source": "e9b3bfc6f43b4bd237e2", "target": "5100636"}, {"source": "5703573bd695c66b6e99", "target": "10699411"}, {"source": "a639c8d8fb6ad384a8e3", "target": "d15be2c0ace80aef3503"}, {"source": "c22e71c05e3c3bfadcae", "target": "5593150"}, {"source": "913d0c1716e92e9fe29c", "target": "2787020caf01beea88cd"}, {"source": "4affa5bdb5e8c87ea5e5", "target": "20f120d7ab0580f9a8c5"}, {"source": "cbd4c10fed8942c87332", "target": "c81710601e2232aaedc0"}, {"source": "2986e55a01c7008d4b5b", "target": "10743959"}, {"source": "12c2e8a92d521144e146", "target": "8603837"}, {"source": "01215dfa696fb3ec66f6", "target": "b8a8fcb885b6840bfde5"}, {"source": "e263c1e6b71ff8fe0976", "target": "b8a8fcb885b6840bfde5"}, {"source": "d5ce25776cb31dde851b", "target": "b8a8fcb885b6840bfde5"}, {"source": "247b71fdef129a907f35", "target": "ccd1b2d7ce54e59e455e"}, {"source": "a679815370ad5e8ce609", "target": "77ddbd1e08c11e18a0f7"}, {"source": "a104fffc6e3c2abc1c8d", "target": "a0c5ef180272fac3aea6"}, {"source": "a104fffc6e3c2abc1c8d", "target": "5e498878a37abb60b902"}, {"source": "a104fffc6e3c2abc1c8d", "target": "4e199eb9812a066e7f2d"}, {"source": "5118ba2eb78edfcf645e", "target": "89e1e69c888e8241ed92"}, {"source": "5118ba2eb78edfcf645e", "target": "1e6da47724c39156adb3"}, {"source": "5118ba2eb78edfcf645e", "target": "0a8fe1caa2ac025c8e86"}, {"source": "4d79d0cdb41a16903b7e", "target": "96447623ef4d342ee09b"}, {"source": "4e199eb9812a066e7f2d", "target": "a0c5ef180272fac3aea6"}, {"source": "4e199eb9812a066e7f2d", "target": "5e498878a37abb60b902"}, {"source": "5e498878a37abb60b902", "target": "a0c5ef180272fac3aea6"}, {"source": "002ba028b31588848738", "target": "96447623ef4d342ee09b"}, {"source": "002ba028b31588848738", "target": "8d667f0989d66f03294b"}, {"source": "f4035e412a7822abf074", "target": "a0c5ef180272fac3aea6"}, {"source": "f4035e412a7822abf074", "target": "5e498878a37abb60b902"}, {"source": "f4035e412a7822abf074", "target": "4e199eb9812a066e7f2d"}, {"source": "5d0b0777aa681f849090", "target": "b8a8fcb885b6840bfde5"}, {"source": "abdff74d4f8ebf9b3350", "target": "b8a8fcb885b6840bfde5"}, {"source": "1cccb8a71e8c5aaf6c08", "target": "0dfac0e7c3d33fcca86b"}, {"source": "8d863db116f226727c71", "target": "b8a8fcb885b6840bfde5"}, {"source": "6a2d04e4eb8ae8a72223", "target": "b8a8fcb885b6840bfde5"}, {"source": "f2540e53c4e327a145d0", "target": "e552ead70d7baaa77739"}, {"source": "2cce0f9305ec755a53e7", "target": "b8a8fcb885b6840bfde5"}, {"source": "d30e1f3cd1f7219bd0e5", "target": "b8a8fcb885b6840bfde5"}, {"source": "7caf3e0c1bff5f4ffaac", "target": "b8a8fcb885b6840bfde5"}, {"source": "7caf3e0c1bff5f4ffaac", "target": "f1b2b824bd9cccea57df"}, {"source": "f1b2b824bd9cccea57df", "target": "b8a8fcb885b6840bfde5"}, {"source": "2dd0548aee7cd62dd496", "target": "b8a8fcb885b6840bfde5"}, {"source": "2dd0548aee7cd62dd496", "target": "a1c9688944f950218658"}, {"source": "36511a38ee44a254c172", "target": "b8a8fcb885b6840bfde5"}, {"source": "ed790586548e472f1f57", "target": "64b015914153b85419ce"}, {"source": "43c5ecc0fe2199ea84dc", "target": "b8a8fcb885b6840bfde5"}, {"source": "1a32a92131723a1cc808", "target": "646c80e683c3fdecf5b7"}, {"source": "847da6bc1fd5e0b0579a", "target": "b8a8fcb885b6840bfde5"}, {"source": "0c1b97dc48a6096ea7ce", "target": "b8a8fcb885b6840bfde5"}, {"source": "00c36fbe045e5d0290fc", "target": "b8a8fcb885b6840bfde5"}, {"source": "4742802e9cf761912270", "target": "b8a8fcb885b6840bfde5"}, {"source": "9c856f4944e50b734b85", "target": "b8a8fcb885b6840bfde5"}, {"source": "9c856f4944e50b734b85", "target": "fb8e1e6750843954ead0"}, {"source": "fb8e1e6750843954ead0", "target": "b8a8fcb885b6840bfde5"}, {"source": "e0408aa623ee83b1eb8d", "target": "b8a8fcb885b6840bfde5"}, {"source": "e0408aa623ee83b1eb8d", "target": "e785328c22638497dcb5"}, {"source": "17b1a9d5f1788c35b255", "target": "b8a8fcb885b6840bfde5"}, {"source": "ba2ffcaaf8c8b7541661", "target": "b8a8fcb885b6840bfde5"}, {"source": "32db87891b734b6c7267", "target": "66daabcfeed118d590f6"}, {"source": "54181ad2cb45d7e41ac8", "target": "29c2c89bcdf66c2553c2"}, {"source": "54181ad2cb45d7e41ac8", "target": "4c0c9844596a2d00938a"}, {"source": "4c0c9844596a2d00938a", "target": "29c2c89bcdf66c2553c2"}, {"source": "9f3884a9f9474e9beedd", "target": "fbb43f7cf188fa8f2d15"}, {"source": "93e01a9c651ebb8f0968", "target": "b8a8fcb885b6840bfde5"}, {"source": "93e01a9c651ebb8f0968", "target": "00c36fbe045e5d0290fc"}, {"source": "f6db3b84de562142fe4b", "target": "b8a8fcb885b6840bfde5"}, {"source": "f6db3b84de562142fe4b", "target": "955beda9851111b7c4b1"}, {"source": "f6db3b84de562142fe4b", "target": "dce660b60d70576d6447"}, {"source": "f6db3b84de562142fe4b", "target": "545566453d4c45d1f4a3"}, {"source": "545566453d4c45d1f4a3", "target": "b8a8fcb885b6840bfde5"}, {"source": "545566453d4c45d1f4a3", "target": "955beda9851111b7c4b1"}, {"source": "545566453d4c45d1f4a3", "target": "dce660b60d70576d6447"}, {"source": "955beda9851111b7c4b1", "target": "b8a8fcb885b6840bfde5"}, {"source": "9c001d2ad72ea5c8f91c", "target": "b8a8fcb885b6840bfde5"}, {"source": "9c001d2ad72ea5c8f91c", "target": "a1c9688944f950218658"}, {"source": "9c001d2ad72ea5c8f91c", "target": "2dd0548aee7cd62dd496"}, {"source": "9c001d2ad72ea5c8f91c", "target": "ffe781dfde208dd3db46"}, {"source": "9c001d2ad72ea5c8f91c", "target": "5905838e05c8035329cb"}, {"source": "9c001d2ad72ea5c8f91c", "target": "e7da8d7aac86fce33bee"}, {"source": "9c001d2ad72ea5c8f91c", "target": "2312026badf1ad2a6cf4"}, {"source": "9c001d2ad72ea5c8f91c", "target": "8c67e661fa919f87f182"}, {"source": "92e1dc0212d0cbf99a82", "target": "b8a8fcb885b6840bfde5"}, {"source": "92e1dc0212d0cbf99a82", "target": "a1c9688944f950218658"}, {"source": "92e1dc0212d0cbf99a82", "target": "2dd0548aee7cd62dd496"}, {"source": "92e1dc0212d0cbf99a82", "target": "ffe781dfde208dd3db46"}, {"source": "92e1dc0212d0cbf99a82", "target": "5905838e05c8035329cb"}, {"source": "92e1dc0212d0cbf99a82", "target": "e7da8d7aac86fce33bee"}, {"source": "92e1dc0212d0cbf99a82", "target": "2312026badf1ad2a6cf4"}, {"source": "92e1dc0212d0cbf99a82", "target": "8c67e661fa919f87f182"}, {"source": "8c67e661fa919f87f182", "target": "b8a8fcb885b6840bfde5"}, {"source": "8c67e661fa919f87f182", "target": "a1c9688944f950218658"}, {"source": "8c67e661fa919f87f182", "target": "2dd0548aee7cd62dd496"}, {"source": "8c67e661fa919f87f182", "target": "ffe781dfde208dd3db46"}, {"source": "8c67e661fa919f87f182", "target": "5905838e05c8035329cb"}, {"source": "8c67e661fa919f87f182", "target": "e7da8d7aac86fce33bee"}, {"source": "8c67e661fa919f87f182", "target": "2312026badf1ad2a6cf4"}, {"source": "2312026badf1ad2a6cf4", "target": "b8a8fcb885b6840bfde5"}, {"source": "2312026badf1ad2a6cf4", "target": "a1c9688944f950218658"}, {"source": "2312026badf1ad2a6cf4", "target": "2dd0548aee7cd62dd496"}, {"source": "2312026badf1ad2a6cf4", "target": "ffe781dfde208dd3db46"}, {"source": "2312026badf1ad2a6cf4", "target": "5905838e05c8035329cb"}, {"source": "2312026badf1ad2a6cf4", "target": "e7da8d7aac86fce33bee"}, {"source": "fb082e67cd821b4362ef", "target": "744228c927ee95871559"}, {"source": "024452c42b94723b401d", "target": "0c4cf357ab5ed3e90382"}, {"source": "0c4cf357ab5ed3e90382", "target": "b956ee7215b7b50bc78a"}, {"source": "a1a7d04bdd34c2096eaf", "target": "b956ee7215b7b50bc78a"}, {"source": "13933a693dc8fac38c62", "target": "b8a8fcb885b6840bfde5"}, {"source": "13933a693dc8fac38c62", "target": "1d8085294b6094e617e9"}, {"source": "13933a693dc8fac38c62", "target": "a3cd99de2763a9a696d1"}, {"source": "a3cd99de2763a9a696d1", "target": "b8a8fcb885b6840bfde5"}, {"source": "a3cd99de2763a9a696d1", "target": "1d8085294b6094e617e9"}, {"source": "1d8085294b6094e617e9", "target": "b8a8fcb885b6840bfde5"}, {"source": "90c4cfd299ca35bc54d8", "target": "db6533daf51fb05b0af6"}, {"source": "90c4cfd299ca35bc54d8", "target": "7aa370b761c4763c0901"}, {"source": "90c4cfd299ca35bc54d8", "target": "c8bb2ca99a6a3b894217"}, {"source": "90c4cfd299ca35bc54d8", "target": "ed16bc3c6fe8c4b051db"}, {"source": "ed16bc3c6fe8c4b051db", "target": "db6533daf51fb05b0af6"}, {"source": "ed16bc3c6fe8c4b051db", "target": "7aa370b761c4763c0901"}, {"source": "ed16bc3c6fe8c4b051db", "target": "c8bb2ca99a6a3b894217"}, {"source": "c8bb2ca99a6a3b894217", "target": "db6533daf51fb05b0af6"}, {"source": "c8bb2ca99a6a3b894217", "target": "7aa370b761c4763c0901"}, {"source": "eb69ef5fbcd2330a7d61", "target": "3231298"}, {"source": "eb69ef5fbcd2330a7d61", "target": "7402f55e19e1e609f014"}, {"source": "eb69ef5fbcd2330a7d61", "target": "36014350348825ded276"}, {"source": "eb69ef5fbcd2330a7d61", "target": "0568c594c55b8951b94c"}, {"source": "eb69ef5fbcd2330a7d61", "target": "fd78a64d3ff9f03907c4"}, {"source": "fd78a64d3ff9f03907c4", "target": "3231298"}, {"source": "fd78a64d3ff9f03907c4", "target": "7402f55e19e1e609f014"}, {"source": "fd78a64d3ff9f03907c4", "target": "36014350348825ded276"}, {"source": "fd78a64d3ff9f03907c4", "target": "0568c594c55b8951b94c"}, {"source": "36014350348825ded276", "target": "3231298"}, {"source": "36014350348825ded276", "target": "7402f55e19e1e609f014"}, {"source": "7402f55e19e1e609f014", "target": "3231298"}, {"source": "5cc8033ab729fbd9a4c9", "target": "522d3842f27e2581f0d9"}, {"source": "5cc8033ab729fbd9a4c9", "target": "9d0251925a6bf6409533"}, {"source": "5cc8033ab729fbd9a4c9", "target": "201b1e8e5a25dfcf76d4"}, {"source": "5cc8033ab729fbd9a4c9", "target": "26b6602160cd14c7e143"}, {"source": "26b6602160cd14c7e143", "target": "522d3842f27e2581f0d9"}, {"source": "26b6602160cd14c7e143", "target": "9d0251925a6bf6409533"}, {"source": "26b6602160cd14c7e143", "target": "201b1e8e5a25dfcf76d4"}, {"source": "201b1e8e5a25dfcf76d4", "target": "522d3842f27e2581f0d9"}, {"source": "201b1e8e5a25dfcf76d4", "target": "9d0251925a6bf6409533"}, {"source": "9d0251925a6bf6409533", "target": "522d3842f27e2581f0d9"}, {"source": "0568c594c55b8951b94c", "target": "3231298"}, {"source": "0568c594c55b8951b94c", "target": "7402f55e19e1e609f014"}, {"source": "0568c594c55b8951b94c", "target": "36014350348825ded276"}, {"source": "7bb89480b28f189fafd3", "target": "12cf7435eb7ccdbd8d02"}, {"source": "7bb89480b28f189fafd3", "target": "ab8b613ca00de4f31b05"}, {"source": "ab8b613ca00de4f31b05", "target": "12cf7435eb7ccdbd8d02"}, {"source": "12cf7435eb7ccdbd8d02", "target": "1256572"}, {"source": "1028a3ee9ab4642199db", "target": "b8a8fcb885b6840bfde5"}, {"source": "1028a3ee9ab4642199db", "target": "9406077edb5a01d13d02"}, {"source": "9406077edb5a01d13d02", "target": "b8a8fcb885b6840bfde5"}, {"source": "bbc662a5bf51665cce8b", "target": "b8a8fcb885b6840bfde5"}, {"source": "bf8dbe207e9aa4c74b21", "target": "b8a8fcb885b6840bfde5"}, {"source": "bf8dbe207e9aa4c74b21", "target": "090f603aa3ef1c5fcf0b"}, {"source": "090f603aa3ef1c5fcf0b", "target": "b8a8fcb885b6840bfde5"}, {"source": "8d48064f03c22ae1a539", "target": "b8a8fcb885b6840bfde5"}, {"source": "8d48064f03c22ae1a539", "target": "abdff74d4f8ebf9b3350"}, {"source": "8d48064f03c22ae1a539", "target": "c16d4bf5e677045e9b94"}, {"source": "c16d4bf5e677045e9b94", "target": "b8a8fcb885b6840bfde5"}, {"source": "c16d4bf5e677045e9b94", "target": "abdff74d4f8ebf9b3350"}, {"source": "b7e78429ce54abc2154f", "target": "3fa3c1a523c8f3c2cfb9"}, {"source": "de3ee03f25aa9f134ecf", "target": "3918369"}, {"source": "6c6dd78bb6502e14b2f2", "target": "b8a8fcb885b6840bfde5"}, {"source": "da3f27dd8be82859b45e14898aa7fce0", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "da3f27dd8be82859b45e14898aa7fce0", "target": "19c88beca509ce5195643099f5740838"}, {"source": "da3f27dd8be82859b45e14898aa7fce0", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "da3f27dd8be82859b45e14898aa7fce0", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "19c88beca509ce5195643099f5740838", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "42f1c37f325999025db3", "target": "b8a8fcb885b6840bfde5"}, {"source": "b29a3572b3e3f19e7a22b18ef9134920", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "b29a3572b3e3f19e7a22b18ef9134920", "target": "19c88beca509ce5195643099f5740838"}, {"source": "b29a3572b3e3f19e7a22b18ef9134920", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "b29a3572b3e3f19e7a22b18ef9134920", "target": "a2746196ad4c3a2d46e416d27c52d2cf"}, {"source": "6d57db7d251fbf26360cb88a7f5a7296", "target": "b8a8fcb885b6840bfde5"}, {"source": "6d57db7d251fbf26360cb88a7f5a7296", "target": "42f1c37f325999025db3"}, {"source": "6d57db7d251fbf26360cb88a7f5a7296", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "6d57db7d251fbf26360cb88a7f5a7296", "target": "2e1f2a94cd49a429cda6d5a3d77e5271"}, {"source": "6d57db7d251fbf26360cb88a7f5a7296", "target": "33881b1947efe610b0e743580af093f8"}, {"source": "6d57db7d251fbf26360cb88a7f5a7296", "target": "3f6b48f9c5d72fb14fafc28ed3129a9f"}, {"source": "33881b1947efe610b0e743580af093f8", "target": "b8a8fcb885b6840bfde5"}, {"source": "33881b1947efe610b0e743580af093f8", "target": "42f1c37f325999025db3"}, {"source": "33881b1947efe610b0e743580af093f8", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "33881b1947efe610b0e743580af093f8", "target": "2e1f2a94cd49a429cda6d5a3d77e5271"}, {"source": "af2040b064e5d2e2e589", "target": "1389927"}, {"source": "af2040b064e5d2e2e589", "target": "9327a6642682b87430ec"}, {"source": "c649af7f141a700dca817b97386147f9", "target": "8241b9338930f081275090c4d421b4bc"}, {"source": "8232e0e796a6dbddc33caffe4929d985", "target": "3887235"}, {"source": "8232e0e796a6dbddc33caffe4929d985", "target": "5da8fc981efc46180000cc749b99465d"}, {"source": "5da8fc981efc46180000cc749b99465d", "target": "3887235"}, {"source": "d4a745960538e937dcde13f61e9893f1", "target": "91c5e84bed1ba6b1541deaa5d5f4f258"}, {"source": "6a0b4488bc60ea61eb887bf4d1f9b2b5", "target": "91c5e84bed1ba6b1541deaa5d5f4f258"}, {"source": "91c5e84bed1ba6b1541deaa5d5f4f258", "target": "1129492"}, {"source": "91c5e84bed1ba6b1541deaa5d5f4f258", "target": "151f6344ffd02105a67a"}, {"source": "6fd7a74709e6d24bf327439c62a3f1c1", "target": "7ac04d6c0797b583c5c79bea0dcb2a3f"}, {"source": "b408e03229958635c43b", "target": "29c2c89bcdf66c2553c2"}, {"source": "b408e03229958635c43b", "target": "4c0c9844596a2d00938a"}, {"source": "b408e03229958635c43b", "target": "54181ad2cb45d7e41ac8"}, {"source": "9a92db9794c21d576087", "target": "4055889"}, {"source": "887677acf92b55b3accd739e77c60fe4", "target": "b8a8fcb885b6840bfde5"}, {"source": "887677acf92b55b3accd739e77c60fe4", "target": "9406077edb5a01d13d02"}, {"source": "1593f53c0c8346d067491f39255d0b84", "target": "8709363"}, {"source": "dcfa9c27db140183bd87f3fc90efaf10", "target": "4248146"}, {"source": "dcfa9c27db140183bd87f3fc90efaf10", "target": "7c0bd51dab6b9665a315fef06c436f27"}, {"source": "7c0bd51dab6b9665a315fef06c436f27", "target": "4248145"}, {"source": "814c00f234349de899ef0d95649974e9", "target": "b4aa88691528c0f0b1fa"}, {"source": "814c00f234349de899ef0d95649974e9", "target": "e98e438c05eba9b8882412ca79af1c6f"}, {"source": "cb9da9f0caa674dc908708336645f0dd", "target": "4ce2ee825811f1c32125"}, {"source": "e98e438c05eba9b8882412ca79af1c6f", "target": "b4aa88691528c0f0b1fa"}, {"source": "73a90dd4229f8941b7f79df8b2c8505d", "target": "1bc8659b52b35b8a320f3fefb7275ef5"}, {"source": "73a90dd4229f8941b7f79df8b2c8505d", "target": "6276c0db5b24cf6bfea02b9dd72c378d"}, {"source": "28821b58b36a81d36a6a814ecdbdc037", "target": "d6b7f134335ddc4d12a3e143f81304d8"}, {"source": "eaab03204fa1acac43ea", "target": "b8a8fcb885b6840bfde5"}, {"source": "eaab03204fa1acac43ea", "target": "00c36fbe045e5d0290fc"}, {"source": "6f97aa94357cae4611c54a80c11f6128", "target": "735c8063f00c773ef1dea78f62a321fa"}, {"source": "6f97aa94357cae4611c54a80c11f6128", "target": "779f2db53e1e365e98d4"}, {"source": "735c8063f00c773ef1dea78f62a321fa", "target": "b8e76dd7003538975b3e81e86ac6dd1e"}, {"source": "b8e76dd7003538975b3e81e86ac6dd1e", "target": "3019563"}, {"source": "b8e76dd7003538975b3e81e86ac6dd1e", "target": "974c9def890f8ac0172611921fb51b8a"}, {"source": "b8e76dd7003538975b3e81e86ac6dd1e", "target": "2c736a99c2f57bfac7e4"}, {"source": "974c9def890f8ac0172611921fb51b8a", "target": "3a68b0c81991e2e94b19"}, {"source": "e14c09b73d241d89536cd81b35771215", "target": "8622315"}, {"source": "e14c09b73d241d89536cd81b35771215", "target": "a05b3ac17407e5d1d9cc989f882125af"}, {"source": "c6d3072074b1a9f717cf66068f34d8b6", "target": "4063318"}, {"source": "5485fa28d3fdd422930b53ab330f96e3", "target": "e16ad218bc241dfd2d6e"}, {"source": "681bfd06a86a6d5e237c", "target": "b8a8fcb885b6840bfde5"}, {"source": "681bfd06a86a6d5e237c", "target": "00c36fbe045e5d0290fc"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "b8a8fcb885b6840bfde5"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "42f1c37f325999025db3"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "6222544da1551c11d55b505edf4b88fa"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "dcd52a1daf6b00e5f0afa9b32e2ca043"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "f8bacc54b016d6ea014e54d81f2b7e48"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "4d7dfded54d1e2162265862ffb178f4a"}, {"source": "77fe31d25ee5c4d03dfe0e0a4cc6e1ba", "target": "9d49a219f72d8958313eb7fd9f236936"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "b8a8fcb885b6840bfde5"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "7b750aac02c274ce7be1a2ec65681b6b"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "e594934db447063ce886b35c2e3ea0c3"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "63b313292ecb948800fb1bebaef690b3"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "73d2088a4754f9a3b46db81c3a12ca42"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "752ff3769123dd6f733dd1a0fd139698"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "8e82406ccf2abef3600b5c63db1830ca"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "0bc494cbfc5dab3fbea588a6a7cc0cdb"}, {"source": "e844a36eff424d262f3de5d9baa16b91", "target": "e08d1b622815450a47a52deb8644533d"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "b8a8fcb885b6840bfde5"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "7b750aac02c274ce7be1a2ec65681b6b"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "e594934db447063ce886b35c2e3ea0c3"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "63b313292ecb948800fb1bebaef690b3"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "73d2088a4754f9a3b46db81c3a12ca42"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "752ff3769123dd6f733dd1a0fd139698"}, {"source": "0bc494cbfc5dab3fbea588a6a7cc0cdb", "target": "8e82406ccf2abef3600b5c63db1830ca"}, {"source": "752ff3769123dd6f733dd1a0fd139698", "target": "b8a8fcb885b6840bfde5"}, {"source": "752ff3769123dd6f733dd1a0fd139698", "target": "7b750aac02c274ce7be1a2ec65681b6b"}, {"source": "752ff3769123dd6f733dd1a0fd139698", "target": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"source": "752ff3769123dd6f733dd1a0fd139698", "target": "e594934db447063ce886b35c2e3ea0c3"}, {"source": "752ff3769123dd6f733dd1a0fd139698", "target": "63b313292ecb948800fb1bebaef690b3"}, {"source": "752ff3769123dd6f733dd1a0fd139698", "target": "73d2088a4754f9a3b46db81c3a12ca42"}, {"source": "ea33bdf3e6d636ecd1bb9f1febd0125e", "target": "b8a8fcb885b6840bfde5"}, {"source": "ea33bdf3e6d636ecd1bb9f1febd0125e", "target": "7b750aac02c274ce7be1a2ec65681b6b"}, {"source": "ea33bdf3e6d636ecd1bb9f1febd0125e", "target": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"source": "ea33bdf3e6d636ecd1bb9f1febd0125e", "target": "e594934db447063ce886b35c2e3ea0c3"}, {"source": "ea33bdf3e6d636ecd1bb9f1febd0125e", "target": "63b313292ecb948800fb1bebaef690b3"}, {"source": "63b313292ecb948800fb1bebaef690b3", "target": "b8a8fcb885b6840bfde5"}, {"source": "63b313292ecb948800fb1bebaef690b3", "target": "7b750aac02c274ce7be1a2ec65681b6b"}, {"source": "63b313292ecb948800fb1bebaef690b3", "target": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"source": "63b313292ecb948800fb1bebaef690b3", "target": "e594934db447063ce886b35c2e3ea0c3"}, {"source": "e594934db447063ce886b35c2e3ea0c3", "target": "b8a8fcb885b6840bfde5"}, {"source": "e594934db447063ce886b35c2e3ea0c3", "target": "7b750aac02c274ce7be1a2ec65681b6b"}, {"source": "e594934db447063ce886b35c2e3ea0c3", "target": "adf2f396ed6f7a06bcee6e03a6f2a375"}, {"source": "7b750aac02c274ce7be1a2ec65681b6b", "target": "b8a8fcb885b6840bfde5"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "34dc0e1aaabac24684ec622da2add7e9"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "54b835498f0288d2affc5c2d021864d1"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "a1169104fd3d0b78a2b60a2f099e0391"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "2338f36048aaa1372f386daa32bf624c"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "dde8f503ef6ea72edc8f3212896497d7"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "7c4f11418ebb53a997531c718e760f7c"}, {"source": "0271e40675307d3aede5cecdad5715e0", "target": "deda5442d64c34186b537f11751004aa"}, {"source": "dde8f503ef6ea72edc8f3212896497d7", "target": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"source": "dde8f503ef6ea72edc8f3212896497d7", "target": "34dc0e1aaabac24684ec622da2add7e9"}, {"source": "dde8f503ef6ea72edc8f3212896497d7", "target": "54b835498f0288d2affc5c2d021864d1"}, {"source": "dde8f503ef6ea72edc8f3212896497d7", "target": "a1169104fd3d0b78a2b60a2f099e0391"}, {"source": "dde8f503ef6ea72edc8f3212896497d7", "target": "2338f36048aaa1372f386daa32bf624c"}, {"source": "2338f36048aaa1372f386daa32bf624c", "target": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"source": "2338f36048aaa1372f386daa32bf624c", "target": "34dc0e1aaabac24684ec622da2add7e9"}, {"source": "2338f36048aaa1372f386daa32bf624c", "target": "54b835498f0288d2affc5c2d021864d1"}, {"source": "2338f36048aaa1372f386daa32bf624c", "target": "a1169104fd3d0b78a2b60a2f099e0391"}, {"source": "a1169104fd3d0b78a2b60a2f099e0391", "target": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"source": "a1169104fd3d0b78a2b60a2f099e0391", "target": "34dc0e1aaabac24684ec622da2add7e9"}, {"source": "a1169104fd3d0b78a2b60a2f099e0391", "target": "54b835498f0288d2affc5c2d021864d1"}, {"source": "54b835498f0288d2affc5c2d021864d1", "target": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"source": "54b835498f0288d2affc5c2d021864d1", "target": "34dc0e1aaabac24684ec622da2add7e9"}, {"source": "34dc0e1aaabac24684ec622da2add7e9", "target": "f65bdcfc2f39d3717709357e2f8fd03e"}, {"source": "5606736552160acb3351", "target": "9078690"}, {"source": "676bfe2b6e67e61e0656", "target": "18cb7a77b2d9de597b86"}, {"source": "676bfe2b6e67e61e0656", "target": "dc1ce756527c072885dc"}, {"source": "302096884d5fbc1817062492605b50dd", "target": "373e03ed01dfcf97d985"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "19c88beca509ce5195643099f5740838"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "b895d79139c315b4a14c7c4f63a9b00e", "target": "ba1beb933b849d986453f374c9cfd206"}, {"source": "ba1beb933b849d986453f374c9cfd206", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "ba1beb933b849d986453f374c9cfd206", "target": "19c88beca509ce5195643099f5740838"}, {"source": "ba1beb933b849d986453f374c9cfd206", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "ba1beb933b849d986453f374c9cfd206", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "ba1beb933b849d986453f374c9cfd206", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "ba1beb933b849d986453f374c9cfd206", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "19c88beca509ce5195643099f5740838"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "f48e7435ff4bb34180e744b0786c57de"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "b88b41124a4e1a398f8443041c41e24f"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "7c0354016ccc2f7974d84714dc8e8d4a"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "0d1bfe27165765ed03f8f175ce8680f1"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "627517c6d7a1b1384bce3f5402fbb9c4"}, {"source": "25f855bb1f47666b42962a82216dfd3e", "target": "ed2735e3952c35c92852410c21d15f46"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "19c88beca509ce5195643099f5740838"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "f48e7435ff4bb34180e744b0786c57de"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "b88b41124a4e1a398f8443041c41e24f"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "7c0354016ccc2f7974d84714dc8e8d4a"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "0d1bfe27165765ed03f8f175ce8680f1"}, {"source": "ed2735e3952c35c92852410c21d15f46", "target": "627517c6d7a1b1384bce3f5402fbb9c4"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "19c88beca509ce5195643099f5740838"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "f48e7435ff4bb34180e744b0786c57de"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "b88b41124a4e1a398f8443041c41e24f"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "7c0354016ccc2f7974d84714dc8e8d4a"}, {"source": "627517c6d7a1b1384bce3f5402fbb9c4", "target": "0d1bfe27165765ed03f8f175ce8680f1"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "19c88beca509ce5195643099f5740838"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "f48e7435ff4bb34180e744b0786c57de"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "b88b41124a4e1a398f8443041c41e24f"}, {"source": "0d1bfe27165765ed03f8f175ce8680f1", "target": "7c0354016ccc2f7974d84714dc8e8d4a"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "19c88beca509ce5195643099f5740838"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "f48e7435ff4bb34180e744b0786c57de"}, {"source": "7c0354016ccc2f7974d84714dc8e8d4a", "target": "b88b41124a4e1a398f8443041c41e24f"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "19c88beca509ce5195643099f5740838"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "b88b41124a4e1a398f8443041c41e24f", "target": "f48e7435ff4bb34180e744b0786c57de"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "19c88beca509ce5195643099f5740838"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "f48e7435ff4bb34180e744b0786c57de", "target": "ede8ef37272abcb60013f885b6736d63"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "19c88beca509ce5195643099f5740838"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "ede8ef37272abcb60013f885b6736d63", "target": "b28c34585d071b6937fdf74b9a85bca4"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "19c88beca509ce5195643099f5740838"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "b28c34585d071b6937fdf74b9a85bca4", "target": "f8eaa285348a91b74ff2bed44d4af44f"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "19c88beca509ce5195643099f5740838"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "f8eaa285348a91b74ff2bed44d4af44f", "target": "4bfacbc47612de727bf99a32d632abc3"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "19c88beca509ce5195643099f5740838"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "4bfacbc47612de727bf99a32d632abc3", "target": "db4dcb20e68ee366eb4c253399a3ad5d"}, {"source": "db4dcb20e68ee366eb4c253399a3ad5d", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "db4dcb20e68ee366eb4c253399a3ad5d", "target": "19c88beca509ce5195643099f5740838"}, {"source": "db4dcb20e68ee366eb4c253399a3ad5d", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "db4dcb20e68ee366eb4c253399a3ad5d", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "db4dcb20e68ee366eb4c253399a3ad5d", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "db4dcb20e68ee366eb4c253399a3ad5d", "target": "32d4c5badc4534da66b827122d6a1831"}, {"source": "32d4c5badc4534da66b827122d6a1831", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "32d4c5badc4534da66b827122d6a1831", "target": "19c88beca509ce5195643099f5740838"}, {"source": "32d4c5badc4534da66b827122d6a1831", "target": "9409231fdbdc710dcbb46cff643f86c9"}, {"source": "32d4c5badc4534da66b827122d6a1831", "target": "c185b3758e7fd011f9a32a3c0b806839"}, {"source": "32d4c5badc4534da66b827122d6a1831", "target": "da3f27dd8be82859b45e14898aa7fce0"}, {"source": "9409231fdbdc710dcbb46cff643f86c9", "target": "f2befd433e182193027dfa1d1b7a65d4"}, {"source": "9409231fdbdc710dcbb46cff643f86c9", "target": "19c88beca509ce5195643099f5740838"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "b8a8fcb885b6840bfde5"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "42f1c37f325999025db3"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "2e1f2a94cd49a429cda6d5a3d77e5271"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "33881b1947efe610b0e743580af093f8"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "3f6b48f9c5d72fb14fafc28ed3129a9f"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "6d57db7d251fbf26360cb88a7f5a7296"}, {"source": "7574590639a3fd654d87f02db11bddb1", "target": "751159e6a2290ab4417e5deac63adec1"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "b8a8fcb885b6840bfde5"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "42f1c37f325999025db3"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "2e1f2a94cd49a429cda6d5a3d77e5271"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "33881b1947efe610b0e743580af093f8"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "3f6b48f9c5d72fb14fafc28ed3129a9f"}, {"source": "751159e6a2290ab4417e5deac63adec1", "target": "6d57db7d251fbf26360cb88a7f5a7296"}, {"source": "cc6cd97c95a80f35c824cc24a5bf0f6d", "target": "b8a8fcb885b6840bfde5"}, {"source": "cc6cd97c95a80f35c824cc24a5bf0f6d", "target": "42f1c37f325999025db3"}, {"source": "4d17fa15a7a5084e217992f985fba484", "target": "4342045"}, {"source": "4d17fa15a7a5084e217992f985fba484", "target": "5181105"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "7d41f75647bdc4fef8736442db42bad0"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "afc1b005be34f64b495b259263733b35"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "33505001b3f4ff96c29fcb15425b561b"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "90056c4e88c140345ac8a52b8999cf1c"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "0762f972ce5785f6efd4b975f2a95ff5"}, {"source": "d390147f53cbcf8094b6aefea8791fc8", "target": "5f080cc5f6d000b6540bded03b3dc133"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "7d41f75647bdc4fef8736442db42bad0"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "afc1b005be34f64b495b259263733b35"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "33505001b3f4ff96c29fcb15425b561b"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "90056c4e88c140345ac8a52b8999cf1c"}, {"source": "5f080cc5f6d000b6540bded03b3dc133", "target": "0762f972ce5785f6efd4b975f2a95ff5"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "7d41f75647bdc4fef8736442db42bad0"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "afc1b005be34f64b495b259263733b35"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "33505001b3f4ff96c29fcb15425b561b"}, {"source": "0762f972ce5785f6efd4b975f2a95ff5", "target": "90056c4e88c140345ac8a52b8999cf1c"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "7d41f75647bdc4fef8736442db42bad0"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "afc1b005be34f64b495b259263733b35"}, {"source": "90056c4e88c140345ac8a52b8999cf1c", "target": "33505001b3f4ff96c29fcb15425b561b"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "afc1b005be34f64b495b259263733b35", "target": "7d41f75647bdc4fef8736442db42bad0"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "7d41f75647bdc4fef8736442db42bad0", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "0bbec668d5ba6765cc908eafd6c556fc", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "0bbec668d5ba6765cc908eafd6c556fc", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "0bbec668d5ba6765cc908eafd6c556fc", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "0bbec668d5ba6765cc908eafd6c556fc", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "0bbec668d5ba6765cc908eafd6c556fc", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "0bbec668d5ba6765cc908eafd6c556fc", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "9be28947eccefb1ac05598b8fbc53d27", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "9be28947eccefb1ac05598b8fbc53d27", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "9be28947eccefb1ac05598b8fbc53d27", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "9be28947eccefb1ac05598b8fbc53d27", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "9be28947eccefb1ac05598b8fbc53d27", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "59d18946ec237fe864d70b811a62eddd", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "59d18946ec237fe864d70b811a62eddd", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "59d18946ec237fe864d70b811a62eddd", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "59d18946ec237fe864d70b811a62eddd", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "1431967216e048fcb4890445e1741c7c", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "1431967216e048fcb4890445e1741c7c", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "1431967216e048fcb4890445e1741c7c", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "b4f37349887a4c178ef93ed855153605", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "b4f37349887a4c178ef93ed855153605", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "7f8df0bcd7fba7e531e6", "target": "58abc7c39f8bcd04de56"}, {"source": "0020bebb012760de0d54", "target": "14fcf6f266e877cb1c23"}, {"source": "24699e2002b4cea23615", "target": "58f360e13a4397bf3dad"}, {"source": "ab293c5472d02639ca4f", "target": "fe38f5960d1276833555"}, {"source": "bd880dbc71d2307e2a5b", "target": "8ff54b5cc0640152c563"}, {"source": "94e7aebb7eb975291d9b", "target": "95685c2bcf9407e687d6"}, {"source": "f8bacc54b016d6ea014e54d81f2b7e48", "target": "b8a8fcb885b6840bfde5"}, {"source": "f8bacc54b016d6ea014e54d81f2b7e48", "target": "42f1c37f325999025db3"}, {"source": "f8bacc54b016d6ea014e54d81f2b7e48", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "f8bacc54b016d6ea014e54d81f2b7e48", "target": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"source": "f8bacc54b016d6ea014e54d81f2b7e48", "target": "6222544da1551c11d55b505edf4b88fa"}, {"source": "f8bacc54b016d6ea014e54d81f2b7e48", "target": "dcd52a1daf6b00e5f0afa9b32e2ca043"}, {"source": "a675d439b9ed06a856bb", "target": "b8a8fcb885b6840bfde5"}, {"source": "a675d439b9ed06a856bb", "target": "1d8085294b6094e617e9"}, {"source": "a675d439b9ed06a856bb", "target": "a3cd99de2763a9a696d1"}, {"source": "a675d439b9ed06a856bb", "target": "e949444235af20478188"}, {"source": "a675d439b9ed06a856bb", "target": "d1b6461efdf225f53d0f"}, {"source": "a675d439b9ed06a856bb", "target": "0dfef7aa92676a47b201"}, {"source": "a675d439b9ed06a856bb", "target": "b16dad1cea69952f5b72"}, {"source": "b16dad1cea69952f5b72", "target": "b8a8fcb885b6840bfde5"}, {"source": "b16dad1cea69952f5b72", "target": "1d8085294b6094e617e9"}, {"source": "b16dad1cea69952f5b72", "target": "a3cd99de2763a9a696d1"}, {"source": "b16dad1cea69952f5b72", "target": "e949444235af20478188"}, {"source": "b16dad1cea69952f5b72", "target": "d1b6461efdf225f53d0f"}, {"source": "b16dad1cea69952f5b72", "target": "0dfef7aa92676a47b201"}, {"source": "0dfef7aa92676a47b201", "target": "b8a8fcb885b6840bfde5"}, {"source": "0dfef7aa92676a47b201", "target": "1d8085294b6094e617e9"}, {"source": "0dfef7aa92676a47b201", "target": "a3cd99de2763a9a696d1"}, {"source": "0dfef7aa92676a47b201", "target": "e949444235af20478188"}, {"source": "0dfef7aa92676a47b201", "target": "d1b6461efdf225f53d0f"}, {"source": "d1b6461efdf225f53d0f", "target": "b8a8fcb885b6840bfde5"}, {"source": "d1b6461efdf225f53d0f", "target": "1d8085294b6094e617e9"}, {"source": "d1b6461efdf225f53d0f", "target": "a3cd99de2763a9a696d1"}, {"source": "d1b6461efdf225f53d0f", "target": "e949444235af20478188"}, {"source": "e949444235af20478188", "target": "b8a8fcb885b6840bfde5"}, {"source": "e949444235af20478188", "target": "1d8085294b6094e617e9"}, {"source": "e949444235af20478188", "target": "a3cd99de2763a9a696d1"}, {"source": "ba54603cfa4b6d7222c0", "target": "3b3ae1013968c0a1613b"}, {"source": "ba54603cfa4b6d7222c0", "target": "41c811c603360c27d0ae"}, {"source": "ba54603cfa4b6d7222c0", "target": "87cf840dd72fef25ac24"}, {"source": "ba54603cfa4b6d7222c0", "target": "bde995eccb68a6294dd9"}, {"source": "bde995eccb68a6294dd9", "target": "3b3ae1013968c0a1613b"}, {"source": "bde995eccb68a6294dd9", "target": "41c811c603360c27d0ae"}, {"source": "bde995eccb68a6294dd9", "target": "87cf840dd72fef25ac24"}, {"source": "87cf840dd72fef25ac24", "target": "3b3ae1013968c0a1613b"}, {"source": "87cf840dd72fef25ac24", "target": "41c811c603360c27d0ae"}, {"source": "41c811c603360c27d0ae", "target": "3b3ae1013968c0a1613b"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "b8a8fcb885b6840bfde5"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "42f1c37f325999025db3"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "6222544da1551c11d55b505edf4b88fa"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "dcd52a1daf6b00e5f0afa9b32e2ca043"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "f8bacc54b016d6ea014e54d81f2b7e48"}, {"source": "9d49a219f72d8958313eb7fd9f236936", "target": "4d7dfded54d1e2162265862ffb178f4a"}, {"source": "dcd52a1daf6b00e5f0afa9b32e2ca043", "target": "b8a8fcb885b6840bfde5"}, {"source": "dcd52a1daf6b00e5f0afa9b32e2ca043", "target": "42f1c37f325999025db3"}, {"source": "dcd52a1daf6b00e5f0afa9b32e2ca043", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "dcd52a1daf6b00e5f0afa9b32e2ca043", "target": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"source": "dcd52a1daf6b00e5f0afa9b32e2ca043", "target": "6222544da1551c11d55b505edf4b88fa"}, {"source": "6222544da1551c11d55b505edf4b88fa", "target": "b8a8fcb885b6840bfde5"}, {"source": "6222544da1551c11d55b505edf4b88fa", "target": "42f1c37f325999025db3"}, {"source": "6222544da1551c11d55b505edf4b88fa", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "6222544da1551c11d55b505edf4b88fa", "target": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"source": "9ffb265094d8dffb8627", "target": "b8a8fcb885b6840bfde5"}, {"source": "9ffb265094d8dffb8627", "target": "7fedb282aca4df15b0d4"}, {"source": "9ffb265094d8dffb8627", "target": "ddd41979fd8e96c976f6"}, {"source": "9ffb265094d8dffb8627", "target": "c7cf338b400b90b04f95"}, {"source": "7fedb282aca4df15b0d4", "target": "b8a8fcb885b6840bfde5"}, {"source": "5246b8a643393f0753a11b98129a3237", "target": "8a173cfcb9171627c7f1"}, {"source": "5246b8a643393f0753a11b98129a3237", "target": "e330141d1279c6115a3a"}, {"source": "428036f0f948a5c2b95f8a4b91931ee6", "target": "7090426"}, {"source": "428036f0f948a5c2b95f8a4b91931ee6", "target": "4063423"}, {"source": "f6a30cdf04d252876b44", "target": "10699411"}, {"source": "f6a30cdf04d252876b44", "target": "5703573bd695c66b6e99"}, {"source": "8e09d21c2a15d7234b6d", "target": "542f350f5af22fba72b5"}, {"source": "8e09d21c2a15d7234b6d", "target": "9c72a94ca5a0d04c68e4"}, {"source": "8e09d21c2a15d7234b6d", "target": "2814080d24a44c357781"}, {"source": "8e09d21c2a15d7234b6d", "target": "b8f13d84df4fbbaa98c0"}, {"source": "b8f13d84df4fbbaa98c0", "target": "542f350f5af22fba72b5"}, {"source": "b8f13d84df4fbbaa98c0", "target": "9c72a94ca5a0d04c68e4"}, {"source": "b8f13d84df4fbbaa98c0", "target": "2814080d24a44c357781"}, {"source": "2814080d24a44c357781", "target": "542f350f5af22fba72b5"}, {"source": "2814080d24a44c357781", "target": "9c72a94ca5a0d04c68e4"}, {"source": "a6690ca67800a2abafcd71ef4725f33f", "target": "6f5b472cabe8f85583989a40eef0303c"}, {"source": "a6690ca67800a2abafcd71ef4725f33f", "target": "f9dacd23eece9d23669c"}, {"source": "5cd07f2cb4ad202a9facfbd5d2bc842e", "target": "62cf60e116ae821c06602793d265eaf6"}, {"source": "5cd07f2cb4ad202a9facfbd5d2bc842e", "target": "a43dbd5690ccd5ac4c6cc392415140e7"}, {"source": "6f5b472cabe8f85583989a40eef0303c", "target": "f9dacd23eece9d23669c"}, {"source": "62cf60e116ae821c06602793d265eaf6", "target": "a43dbd5690ccd5ac4c6cc392415140e7"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "50f0b4412292163bf4b50d06253994ae"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "f8f62045f69c65e3514cfe20f33fb9f1"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "b4f37349887a4c178ef93ed855153605"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "1431967216e048fcb4890445e1741c7c"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "59d18946ec237fe864d70b811a62eddd"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "9be28947eccefb1ac05598b8fbc53d27"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "0bbec668d5ba6765cc908eafd6c556fc"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "7d41f75647bdc4fef8736442db42bad0"}, {"source": "33505001b3f4ff96c29fcb15425b561b", "target": "afc1b005be34f64b495b259263733b35"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "b8a8fcb885b6840bfde5"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "42f1c37f325999025db3"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "cc6cd97c95a80f35c824cc24a5bf0f6d"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "2c6fa34a28b157e78a22fc93800f0bc7"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "6222544da1551c11d55b505edf4b88fa"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "dcd52a1daf6b00e5f0afa9b32e2ca043"}, {"source": "4d7dfded54d1e2162265862ffb178f4a", "target": "f8bacc54b016d6ea014e54d81f2b7e48"}, {"source": "794e8d9eb8c153a1778cc487ff4465ff", "target": "b8a8fcb885b6840bfde5"}, {"source": "794e8d9eb8c153a1778cc487ff4465ff", "target": "1d3dcaefadd4c8f33318fe017cf730d6"}, {"source": "794e8d9eb8c153a1778cc487ff4465ff", "target": "d9330fbe7bbf1a68e3d634602eb4d7a2"}, {"source": "794e8d9eb8c153a1778cc487ff4465ff", "target": "f405977b9bf51851a6b86476992c6ce9"}, {"source": "d9330fbe7bbf1a68e3d634602eb4d7a2", "target": "b8a8fcb885b6840bfde5"}, {"source": "d9330fbe7bbf1a68e3d634602eb4d7a2", "target": "1d3dcaefadd4c8f33318fe017cf730d6"}, {"source": "9327a6642682b87430ec", "target": "1389927"}, {"source": "ca8347ecae78391b8cc9", "target": "1209499"}, {"source": "9e82d298e917564240ad98c881d6c75b", "target": "53fe9d373ca3b3e20632f69c5169a59d"}, {"source": "f405977b9bf51851a6b86476992c6ce9", "target": "b8a8fcb885b6840bfde5"}, {"source": "f405977b9bf51851a6b86476992c6ce9", "target": "1d3dcaefadd4c8f33318fe017cf730d6"}, {"source": "f405977b9bf51851a6b86476992c6ce9", "target": "d9330fbe7bbf1a68e3d634602eb4d7a2"}, {"source": "1d3dcaefadd4c8f33318fe017cf730d6", "target": "b8a8fcb885b6840bfde5"}, {"source": "9adf74fb08394082c393d8290c00e843", "target": "b8a8fcb885b6840bfde5"}, {"source": "5085fcdd086cb5174867", "target": "b8a8fcb885b6840bfde5"}, {"source": "5085fcdd086cb5174867", "target": "4742802e9cf761912270"}, {"source": "5085fcdd086cb5174867", "target": "9747494f9437c0567eed"}, {"source": "a5df54f0c34518424b00", "target": "b8a8fcb885b6840bfde5"}, {"source": "9747494f9437c0567eed", "target": "b8a8fcb885b6840bfde5"}, {"source": "9747494f9437c0567eed", "target": "4742802e9cf761912270"}, {"source": "9ea31a49cc0af1fdeed189619f651f5b", "target": "92841c212b11c534713782544cfdf10f"}, {"source": "9ea31a49cc0af1fdeed189619f651f5b", "target": "8b7d381614a093195eec2636cab9ef2f"}, {"source": "8b7d381614a093195eec2636cab9ef2f", "target": "92841c212b11c534713782544cfdf10f"}, {"source": "ac5b726c53ec02618de7795429c6f833", "target": "4063663"}, {"source": "1061eef4ce7545bdd52537dcef952e7e", "target": "c6d3072074b1a9f717cf66068f34d8b6"}, {"source": "1061eef4ce7545bdd52537dcef952e7e", "target": "5577023"}, {"source": "cc5dcff17db32e4e910f", "target": "c98b726ab1cc4f4b1477"}, {"source": "cc5dcff17db32e4e910f", "target": "606b23b77ecf3991d3b0"}, {"source": "cc5dcff17db32e4e910f", "target": "4090846"}, {"source": "c98b726ab1cc4f4b1477", "target": "606b23b77ecf3991d3b0"}, {"source": "c98b726ab1cc4f4b1477", "target": "4090846"}, {"source": "606b23b77ecf3991d3b0", "target": "4090846"}, {"source": "f0f27bb61ef20a6b2e17d7231e2294c6", "target": "1021953"}, {"source": "5e1fbf27aa88a0f96680634bfd4079b3", "target": "7880033"}, {"source": "4d76ddafa3682bbcae37", "target": "f494728af00496ea1aa4"}, {"source": "1554f5cc59f7b4b3403d", "target": "17a54b0b2520fb6dcae3"}]}