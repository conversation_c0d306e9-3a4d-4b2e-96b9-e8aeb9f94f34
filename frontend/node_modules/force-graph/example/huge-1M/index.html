<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
  <!--<script src="../../dist/force-graph.js"></script>-->
</head>

<body>
  <div id="graph"></div>

  <script>
    window.devicePixelRatio = 1; // use standard resolution in retina displays

    // Random tree
    const N = 500000;
    const gData = {
      nodes: [...Array(N).keys()].map(i => ({ id: i })),
      links: [...Array(N).keys()]
        .filter(id => id)
        .map(id => ({
          source: id,
          target: Math.round(Math.random() * (id-1))
        }))
    };

    const Graph = new ForceGraph(document.getElementById('graph'))
        .linkColor(() => 'rgba(0,0,0,0.04)')
        .enablePointerInteraction(false)
        .d3AlphaDecay(0)
        .d3VelocityDecay(0.08)
        .warmupTicks(20)
        .cooldownTicks(0)
        .zoom(0.01)
        .graphData(gData);
  </script>
</body>