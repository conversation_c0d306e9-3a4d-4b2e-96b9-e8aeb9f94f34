<head>
  <style> body { margin: 0; } </style>

  <script src="//cdn.jsdelivr.net/npm/force-graph"></script>
  <!--<script src="../../dist/force-graph.js"></script>-->
</head>

<body>
  <div id="graph"></div>

  <script type="module">
    import { csvParse } from 'https://esm.sh/d3-dsv';
    import { forceCollide } from 'https://esm.sh/d3-force';
    import { GUI } from 'https://esm.sh/dat.gui';

    // controls
    const controls = { 'DAG Orientation': 'td'};
    const gui = new GUI();
    gui.add(controls, 'DAG Orientation', ['td', 'bu', 'lr', 'rl', 'radialout', 'radialin', null])
      .onChange(orientation => graph && graph.dagMode(orientation));

    // graph config
    const NODE_REL_SIZE = 1;
    const graph = new ForceGraph(document.getElementById('graph'))
      .dagMode('td')
      .dagLevelDistance(300)
      .backgroundColor('#101020')
      .linkColor(() => 'rgba(255,255,255,0.2)')
      .nodeRelSize(NODE_REL_SIZE)
      .nodeId('path')
      .nodeVal(node => 100 / (node.level + 1))
      .nodeLabel('path')
      .nodeAutoColorBy('module')
      .linkDirectionalParticles(2)
      .linkDirectionalParticleWidth(2)
      .d3Force('collision', forceCollide(node => Math.sqrt(100 / (node.level + 1)) * NODE_REL_SIZE))
      .d3VelocityDecay(0.3);

    fetch('../datasets/d3-dependencies.csv')
      .then(r => r.text())
      .then(csvParse)
      .then(data => {
        const nodes = [], links = [];
        data.forEach(({ size, path }) => {
          const levels = path.split('/'),
            level = levels.length - 1,
            module = level > 0 ? levels[1] : null,
            leaf = levels.pop(),
            parent = levels.join('/');

          const node = {
            path,
            leaf,
            module,
            size: +size || 20,
            level
          };

          nodes.push(node);

          if (parent) {
            links.push({source: parent, target: path, targetNode: node});
          }
        });

        graph.graphData({ nodes, links });
      });
  </script>
</body>