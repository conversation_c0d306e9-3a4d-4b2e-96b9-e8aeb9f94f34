// Version 1.50.1 force-graph - https://github.com/vasturiano/force-graph
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t="undefined"!=typeof globalThis?globalThis:t||self).ForceGraph=n()}(this,function(){"use strict";function n(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function e(t,n,e){if(i())return Reflect.construct.apply(null,arguments);var r=[null];return r.push.apply(r,n),new(t.bind.apply(t,r))}function r(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function i(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(i=function(){return!!t})()}function o(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),e.push.apply(e,r)}return e}function a(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?o(Object(e),!0).forEach(function(n){r(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):o(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function u(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||c(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){return function(t){if(Array.isArray(t))return n(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||c(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(t,e){if(t){if("string"==typeof t)return n(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(t,e):void 0}}!function(t,n){void 0===n&&(n={});var e=n.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===e&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".force-graph-container canvas {\n  display: block;\n  user-select: none;\n  outline: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.force-graph-container .clickable {\n  cursor: pointer;\n}\n\n.force-graph-container .grabbable {\n  cursor: move;\n  cursor: grab;\n  cursor: -moz-grab;\n  cursor: -webkit-grab;\n}\n\n.force-graph-container .grabbable:active {\n  cursor: grabbing;\n  cursor: -moz-grabbing;\n  cursor: -webkit-grabbing;\n}\n");var h="http://www.w3.org/1999/xhtml",f={svg:"http://www.w3.org/2000/svg",xhtml:h,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function p(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),f.hasOwnProperty(n)?{space:f[n],local:t}:t}function d(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===h&&n.documentElement.namespaceURI===h?n.createElement(t):n.createElementNS(e,t)}}function g(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function y(t){var n=p(t);return(n.local?g:d)(n)}function _(){}function v(t){return null==t?_:function(){return this.querySelector(t)}}function m(){return[]}function x(t){return null==t?m:function(){return this.querySelectorAll(t)}}function b(t){return function(){return function(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}(t.apply(this,arguments))}}function w(t){return function(){return this.matches(t)}}function k(t){return function(n){return n.matches(t)}}var M=Array.prototype.find;function A(){return this.firstElementChild}var z=Array.prototype.filter;function S(){return Array.from(this.children)}function C(t){return new Array(t.length)}function E(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function P(t,n,e,r,i,o){for(var a,u=0,s=n.length,l=o.length;u<l;++u)(a=n[u])?(a.__data__=o[u],r[u]=a):e[u]=new E(t,o[u]);for(;u<s;++u)(a=n[u])&&(i[u]=a)}function O(t,n,e,r,i,o,a){var u,s,l,c=new Map,h=n.length,f=o.length,p=new Array(h);for(u=0;u<h;++u)(s=n[u])&&(p[u]=l=a.call(s,s.__data__,u,n)+"",c.has(l)?i[u]=s:c.set(l,s));for(u=0;u<f;++u)l=a.call(t,o[u],u,o)+"",(s=c.get(l))?(r[u]=s,s.__data__=o[u],c.delete(l)):e[u]=new E(t,o[u]);for(u=0;u<h;++u)(s=n[u])&&c.get(p[u])===s&&(i[u]=s)}function N(t){return t.__data__}function j(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function T(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function R(t){return function(){this.removeAttribute(t)}}function D(t){return function(){this.removeAttributeNS(t.space,t.local)}}function I(t,n){return function(){this.setAttribute(t,n)}}function U(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function F(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function L(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}function q(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function $(t){return function(){this.style.removeProperty(t)}}function B(t,n,e){return function(){this.style.setProperty(t,n,e)}}function H(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function V(t,n){return t.style.getPropertyValue(n)||q(t).getComputedStyle(t,null).getPropertyValue(n)}function X(t){return function(){delete this[t]}}function G(t,n){return function(){this[t]=n}}function Y(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function W(t){return t.trim().split(/^|\s+/)}function Z(t){return t.classList||new Q(t)}function Q(t){this._node=t,this._names=W(t.getAttribute("class")||"")}function K(t,n){for(var e=Z(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function J(t,n){for(var e=Z(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function tt(t){return function(){K(this,t)}}function nt(t){return function(){J(this,t)}}function et(t,n){return function(){(n.apply(this,arguments)?K:J)(this,t)}}function rt(){this.textContent=""}function it(t){return function(){this.textContent=t}}function ot(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}function at(){this.innerHTML=""}function ut(t){return function(){this.innerHTML=t}}function st(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}function lt(){this.nextSibling&&this.parentNode.appendChild(this)}function ct(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ht(){return null}function ft(){var t=this.parentNode;t&&t.removeChild(this)}function pt(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function dt(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function gt(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function yt(t,n,e){return function(){var r,i=this.__on,o=function(t){return function(n){t.call(this,n,this.__data__)}}(n);if(i)for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),void(r.value=n);this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function _t(t,n,e){var r=q(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}function vt(t,n){return function(){return _t(this,t,n)}}function mt(t,n){return function(){return _t(this,t,n.apply(this,arguments))}}E.prototype={constructor:E,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},Q.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var xt=[null];function bt(t,n){this._groups=t,this._parents=n}function wt(){return new bt([[document.documentElement]],xt)}function kt(t){return"string"==typeof t?new bt([[document.querySelector(t)]],[document.documentElement]):new bt([[t]],xt)}function Mt(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}bt.prototype=wt.prototype={constructor:bt,select:function(t){"function"!=typeof t&&(t=v(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a,u=n[i],s=u.length,l=r[i]=new Array(s),c=0;c<s;++c)(o=u[c])&&(a=t.call(o,o.__data__,c,u))&&("__data__"in o&&(a.__data__=o.__data__),l[c]=a);return new bt(r,this._parents)},selectAll:function(t){t="function"==typeof t?b(t):x(t);for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var a,u=n[o],s=u.length,l=0;l<s;++l)(a=u[l])&&(r.push(t.call(a,a.__data__,l,u)),i.push(a));return new bt(r,i)},selectChild:function(t){return this.select(null==t?A:function(t){return function(){return M.call(this.children,t)}}("function"==typeof t?t:k(t)))},selectChildren:function(t){return this.selectAll(null==t?S:function(t){return function(){return z.call(this.children,t)}}("function"==typeof t?t:k(t)))},filter:function(t){"function"!=typeof t&&(t=w(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new bt(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,N);var e=n?O:P,r=this._parents,i=this._groups;"function"!=typeof t&&(t=function(t){return function(){return t}}(t));for(var o=i.length,a=new Array(o),u=new Array(o),s=new Array(o),l=0;l<o;++l){var c=r[l],h=i[l],f=h.length,p=j(t.call(c,c&&c.__data__,l,r)),d=p.length,g=u[l]=new Array(d),y=a[l]=new Array(d);e(c,h,g,y,s[l]=new Array(f),p,n);for(var _,v,m=0,x=0;m<d;++m)if(_=g[m]){for(m>=x&&(x=m+1);!(v=y[x])&&++x<d;);_._next=v||null}}return(a=new bt(a,r))._enter=u,a._exit=s,a},enter:function(){return new bt(this._enter||this._groups.map(C),this._parents)},exit:function(){return new bt(this._exit||this._groups.map(C),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,a=Math.min(i,o),u=new Array(i),s=0;s<a;++s)for(var l,c=e[s],h=r[s],f=c.length,p=u[s]=new Array(f),d=0;d<f;++d)(l=c[d]||h[d])&&(p[d]=l);for(;s<i;++s)u[s]=e[s];return new bt(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=T);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var a,u=e[o],s=u.length,l=i[o]=new Array(s),c=0;c<s;++c)(a=u[c])&&(l[c]=a);l.sort(n)}return new bt(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(const n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,n){var e=p(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?D:R:"function"==typeof n?e.local?L:F:e.local?U:I)(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?$:"function"==typeof n?H:B)(t,n,null==e?"":e)):V(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?X:"function"==typeof n?Y:G)(t,n)):this.node()[t]},classed:function(t,n){var e=W(t+"");if(arguments.length<2){for(var r=Z(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?et:n?tt:nt)(e,n))},text:function(t){return arguments.length?this.each(null==t?rt:("function"==typeof t?ot:it)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?at:("function"==typeof t?st:ut)(t)):this.node().innerHTML},raise:function(){return this.each(lt)},lower:function(){return this.each(ct)},append:function(t){var n="function"==typeof t?t:y(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:y(t),r=null==n?ht:"function"==typeof n?n:v(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(ft)},clone:function(t){return this.select(t?dt:pt)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=function(t){return t.trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}})}(t+""),a=o.length;if(!(arguments.length<2)){for(u=n?yt:gt,r=0;r<a;++r)this.each(u(o[r],n,e));return this}var u=this.node().__on;if(u)for(var s,l=0,c=u.length;l<c;++l)for(r=0,s=u[l];r<a;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value},dispatch:function(t,n){return this.each(("function"==typeof n?mt:vt)(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};var At={value:()=>{}};function zt(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new St(r)}function St(t){this._=t}function Ct(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}function Et(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=At,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}St.prototype=zt.prototype={constructor:St,on:function(t,n){var e,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}})),a=-1,u=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++a<u;)if(e=(t=o[a]).type)i[e]=Et(i[e],t.name,n);else if(null==n)for(e in i)i[e]=Et(i[e],t.name,null);return this}for(;++a<u;)if((e=(t=o[a]).type)&&(e=Ct(i[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new St(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};const Pt={passive:!1},Ot={capture:!0,passive:!1};function Nt(t){t.stopImmediatePropagation()}function jt(t){t.preventDefault(),t.stopImmediatePropagation()}function Tt(t){var n=t.document.documentElement,e=kt(t).on("dragstart.drag",jt,Ot);"onselectstart"in n?e.on("selectstart.drag",jt,Ot):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function Rt(t,n){var e=t.document.documentElement,r=kt(t).on("dragstart.drag",null);n&&(r.on("click.drag",jt,Ot),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}var Dt=t=>()=>t;function It(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:a,y:u,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function Ut(t){return!t.ctrlKey&&!t.button}function Ft(){return this.parentNode}function Lt(t,n){return null==n?{x:t.x,y:t.y}:n}function qt(){return navigator.maxTouchPoints||"ontouchstart"in this}function $t(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function Bt(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function Ht(){}It.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var Vt=.7,Xt=1/Vt,Gt="\\s*([+-]?\\d+)\\s*",Yt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Wt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Zt=/^#([0-9a-f]{3,8})$/,Qt=new RegExp(`^rgb\\(${Gt},${Gt},${Gt}\\)$`),Kt=new RegExp(`^rgb\\(${Wt},${Wt},${Wt}\\)$`),Jt=new RegExp(`^rgba\\(${Gt},${Gt},${Gt},${Yt}\\)$`),tn=new RegExp(`^rgba\\(${Wt},${Wt},${Wt},${Yt}\\)$`),nn=new RegExp(`^hsl\\(${Yt},${Wt},${Wt}\\)$`),en=new RegExp(`^hsla\\(${Yt},${Wt},${Wt},${Yt}\\)$`),rn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function on(){return this.rgb().formatHex()}function an(){return this.rgb().formatRgb()}function un(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=Zt.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?sn(n):3===e?new hn(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?ln(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?ln(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=Qt.exec(t))?new hn(n[1],n[2],n[3],1):(n=Kt.exec(t))?new hn(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=Jt.exec(t))?ln(n[1],n[2],n[3],n[4]):(n=tn.exec(t))?ln(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=nn.exec(t))?_n(n[1],n[2]/100,n[3]/100,1):(n=en.exec(t))?_n(n[1],n[2]/100,n[3]/100,n[4]):rn.hasOwnProperty(t)?sn(rn[t]):"transparent"===t?new hn(NaN,NaN,NaN,0):null}function sn(t){return new hn(t>>16&255,t>>8&255,255&t,1)}function ln(t,n,e,r){return r<=0&&(t=n=e=NaN),new hn(t,n,e,r)}function cn(t,n,e,r){return 1===arguments.length?function(t){return t instanceof Ht||(t=un(t)),t?new hn((t=t.rgb()).r,t.g,t.b,t.opacity):new hn}(t):new hn(t,n,e,null==r?1:r)}function hn(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function fn(){return`#${yn(this.r)}${yn(this.g)}${yn(this.b)}`}function pn(){const t=dn(this.opacity);return`${1===t?"rgb(":"rgba("}${gn(this.r)}, ${gn(this.g)}, ${gn(this.b)}${1===t?")":`, ${t})`}`}function dn(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function gn(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function yn(t){return((t=gn(t))<16?"0":"")+t.toString(16)}function _n(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new mn(t,n,e,r)}function vn(t){if(t instanceof mn)return new mn(t.h,t.s,t.l,t.opacity);if(t instanceof Ht||(t=un(t)),!t)return new mn;if(t instanceof mn)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),a=NaN,u=o-i,s=(o+i)/2;return u?(a=n===o?(e-r)/u+6*(e<r):e===o?(r-n)/u+2:(n-e)/u+4,u/=s<.5?o+i:2-o-i,a*=60):u=s>0&&s<1?0:a,new mn(a,u,s,t.opacity)}function mn(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function xn(t){return(t=(t||0)%360)<0?t+360:t}function bn(t){return Math.max(0,Math.min(1,t||0))}function wn(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}$t(Ht,un,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:on,formatHex:on,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return vn(this).formatHsl()},formatRgb:an,toString:an}),$t(hn,cn,Bt(Ht,{brighter(t){return t=null==t?Xt:Math.pow(Xt,t),new hn(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Vt:Math.pow(Vt,t),new hn(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new hn(gn(this.r),gn(this.g),gn(this.b),dn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:fn,formatHex:fn,formatHex8:function(){return`#${yn(this.r)}${yn(this.g)}${yn(this.b)}${yn(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:pn,toString:pn})),$t(mn,function(t,n,e,r){return 1===arguments.length?vn(t):new mn(t,n,e,null==r?1:r)},Bt(Ht,{brighter(t){return t=null==t?Xt:Math.pow(Xt,t),new mn(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Vt:Math.pow(Vt,t),new mn(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new hn(wn(t>=240?t-240:t+120,i,r),wn(t,i,r),wn(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new mn(xn(this.h),bn(this.s),bn(this.l),dn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=dn(this.opacity);return`${1===t?"hsl(":"hsla("}${xn(this.h)}, ${100*bn(this.s)}%, ${100*bn(this.l)}%${1===t?")":`, ${t})`}`}}));var kn=t=>()=>t;function Mn(t){return 1===(t=+t)?An:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):kn(isNaN(n)?e:n)}}function An(t,n){var e=n-t;return e?function(t,n){return function(e){return t+e*n}}(t,e):kn(isNaN(t)?n:t)}var zn=function t(n){var e=Mn(n);function r(t,n){var r=e((t=cn(t)).r,(n=cn(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),a=An(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=a(n),t+""}}return r.gamma=t,r}(1);function Sn(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}var Cn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,En=new RegExp(Cn.source,"g");function Pn(t,n){var e,r,i,o=Cn.lastIndex=En.lastIndex=0,a=-1,u=[],s=[];for(t+="",n+="";(e=Cn.exec(t))&&(r=En.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),u[a]?u[a]+=i:u[++a]=i),(e=e[0])===(r=r[0])?u[a]?u[a]+=r:u[++a]=r:(u[++a]=null,s.push({i:a,x:Sn(e,r)})),o=En.lastIndex;return o<n.length&&(i=n.slice(o),u[a]?u[a]+=i:u[++a]=i),u.length<2?s[0]?function(t){return function(n){return t(n)+""}}(s[0].x):function(t){return function(){return t}}(n):(n=s.length,function(t){for(var e,r=0;r<n;++r)u[(e=s[r]).i]=e.x(t);return u.join("")})}var On,Nn=180/Math.PI,jn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Tn(t,n,e,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+n*n))&&(t/=a,n/=a),(s=t*e+n*r)&&(e-=t*s,r-=n*s),(u=Math.sqrt(e*e+r*r))&&(e/=u,r/=u,s/=u),t*r<n*e&&(t=-t,n=-n,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*Nn,skewX:Math.atan(s)*Nn,scaleX:a,scaleY:u}}function Rn(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u=[],s=[];return o=t(o),a=t(a),function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,n,null,e);u.push({i:s-4,x:Sn(t,i)},{i:s-2,x:Sn(r,o)})}else(i||o)&&a.push("translate("+i+n+o+e)}(o.translateX,o.translateY,a.translateX,a.translateY,u,s),function(t,n,e,o){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),o.push({i:e.push(i(e)+"rotate(",null,r)-2,x:Sn(t,n)})):n&&e.push(i(e)+"rotate("+n+r)}(o.rotate,a.rotate,u,s),function(t,n,e,o){t!==n?o.push({i:e.push(i(e)+"skewX(",null,r)-2,x:Sn(t,n)}):n&&e.push(i(e)+"skewX("+n+r)}(o.skewX,a.skewX,u,s),function(t,n,e,r,o,a){if(t!==e||n!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:Sn(t,e)},{i:u-2,x:Sn(n,r)})}else 1===e&&1===r||o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,u,s),o=a=null,function(t){for(var n,e=-1,r=s.length;++e<r;)u[(n=s[e]).i]=n.x(t);return u.join("")}}}var Dn=Rn(function(t){const n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?jn:Tn(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),In=Rn(function(t){return null==t?jn:(On||(On=document.createElementNS("http://www.w3.org/2000/svg","g")),On.setAttribute("transform",t),(t=On.transform.baseVal.consolidate())?Tn((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):jn)},", ",")",")");function Un(t){return((t=Math.exp(t))+1/t)/2}var Fn,Ln,qn=function t(n,e,r){function i(t,i){var o,a,u=t[0],s=t[1],l=t[2],c=i[0],h=i[1],f=i[2],p=c-u,d=h-s,g=p*p+d*d;if(g<1e-12)a=Math.log(f/l)/n,o=function(t){return[u+t*p,s+t*d,l*Math.exp(n*t*a)]};else{var y=Math.sqrt(g),_=(f*f-l*l+r*g)/(2*l*e*y),v=(f*f-l*l-r*g)/(2*f*e*y),m=Math.log(Math.sqrt(_*_+1)-_),x=Math.log(Math.sqrt(v*v+1)-v);a=(x-m)/n,o=function(t){var r=t*a,i=Un(m),o=l/(e*y)*(i*function(t){return((t=Math.exp(2*t))-1)/(t+1)}(n*r+m)-function(t){return((t=Math.exp(t))-1/t)/2}(m));return[u+o*p,s+o*d,l*i/Un(n*r+m)]}}return o.duration=1e3*a*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4),$n=0,Bn=0,Hn=0,Vn=0,Xn=0,Gn=0,Yn="object"==typeof performance&&performance.now?performance:Date,Wn="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Zn(){return Xn||(Wn(Qn),Xn=Yn.now()+Gn)}function Qn(){Xn=0}function Kn(){this._call=this._time=this._next=null}function Jn(t,n,e){var r=new Kn;return r.restart(t,n,e),r}function te(){Xn=(Vn=Yn.now())+Gn,$n=Bn=0;try{!function(){Zn(),++$n;for(var t,n=Fn;n;)(t=Xn-n._time)>=0&&n._call.call(void 0,t),n=n._next;--$n}()}finally{$n=0,function(){var t,n,e=Fn,r=1/0;for(;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:Fn=n);Ln=t,ee(r)}(),Xn=0}}function ne(){var t=Yn.now(),n=t-Vn;n>1e3&&(Gn-=n,Vn=t)}function ee(t){$n||(Bn&&(Bn=clearTimeout(Bn)),t-Xn>24?(t<1/0&&(Bn=setTimeout(te,t-Yn.now()-Gn)),Hn&&(Hn=clearInterval(Hn))):(Hn||(Vn=Yn.now(),Hn=setInterval(ne,1e3)),$n=1,Wn(te)))}function re(t,n,e){var r=new Kn;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}Kn.prototype=Jn.prototype={constructor:Kn,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?Zn():+e)+(null==n?0:+n),this._next||Ln===this||(Ln?Ln._next=this:Fn=this,Ln=this),this._call=t,this._time=e,ee()},stop:function(){this._call&&(this._call=null,this._time=1/0,ee())}};var ie=zt("start","end","cancel","interrupt"),oe=[];function ae(t,n,e,r,i,o){var a=t.__transition;if(a){if(e in a)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(t){e.state=1,e.timer.restart(a,e.delay,e.time),e.delay<=t&&a(t-e.delay)}function a(o){var l,c,h,f;if(1!==e.state)return s();for(l in i)if((f=i[l]).name===e.name){if(3===f.state)return re(a);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",t,t.__data__,f.index,f.group),delete i[l]):+l<n&&(f.state=6,f.timer.stop(),f.on.call("cancel",t,t.__data__,f.index,f.group),delete i[l])}if(re(function(){3===e.state&&(e.state=4,e.timer.restart(u,e.delay,e.time),u(o))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(e.state=3,r=new Array(h=e.tween.length),l=0,c=-1;l<h;++l)(f=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++c]=f);r.length=c+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(s),e.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),s())}function s(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=Jn(o,0,e.time)}(t,e,{name:n,index:r,group:i,on:ie,tween:oe,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function ue(t,n){var e=le(t,n);if(e.state>0)throw new Error("too late; already scheduled");return e}function se(t,n){var e=le(t,n);if(e.state>3)throw new Error("too late; already running");return e}function le(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function ce(t,n){var e,r,i,o=t.__transition,a=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name===n?(r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]):a=!1;a&&delete t.__transition}}function he(t,n){var e,r;return function(){var i=se(this,t),o=i.tween;if(o!==e)for(var a=0,u=(r=e=o).length;a<u;++a)if(r[a].name===n){(r=r.slice()).splice(a,1);break}i.tween=r}}function fe(t,n,e){var r,i;if("function"!=typeof e)throw new Error;return function(){var o=se(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=u;break}s===l&&i.push(u)}o.tween=i}}function pe(t,n,e){var r=t._id;return t.each(function(){var t=se(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return le(t,r).value[n]}}function de(t,n){var e;return("number"==typeof n?Sn:n instanceof un?zn:(e=un(n))?(n=e,zn):Pn)(t,n)}function ge(t){return function(){this.removeAttribute(t)}}function ye(t){return function(){this.removeAttributeNS(t.space,t.local)}}function _e(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=n(r=a,e)}}function ve(t,n,e){var r,i,o=e+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=n(r=a,e)}}function me(t,n,e){var r,i,o;return function(){var a,u,s=e(this);if(null!=s)return(a=this.getAttribute(t))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s));this.removeAttribute(t)}}function xe(t,n,e){var r,i,o;return function(){var a,u,s=e(this);if(null!=s)return(a=this.getAttributeNS(t.space,t.local))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=n(r=a,s));this.removeAttributeNS(t.space,t.local)}}function be(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}(t,i)),e}return i._value=n,i}function we(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}(t,i)),e}return i._value=n,i}function ke(t,n){return function(){ue(this,t).delay=+n.apply(this,arguments)}}function Me(t,n){return n=+n,function(){ue(this,t).delay=n}}function Ae(t,n){return function(){se(this,t).duration=+n.apply(this,arguments)}}function ze(t,n){return n=+n,function(){se(this,t).duration=n}}var Se=wt.prototype.constructor;function Ce(t){return function(){this.style.removeProperty(t)}}var Ee=0;function Pe(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function Oe(){return++Ee}var Ne=wt.prototype;Pe.prototype={constructor:Pe,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=v(t));for(var r=this._groups,i=r.length,o=new Array(i),a=0;a<i;++a)for(var u,s,l=r[a],c=l.length,h=o[a]=new Array(c),f=0;f<c;++f)(u=l[f])&&(s=t.call(u,u.__data__,f,l))&&("__data__"in u&&(s.__data__=u.__data__),h[f]=s,ae(h[f],n,e,f,h,le(u,e)));return new Pe(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=x(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var s,l=r[u],c=l.length,h=0;h<c;++h)if(s=l[h]){for(var f,p=t.call(s,s.__data__,h,l),d=le(s,e),g=0,y=p.length;g<y;++g)(f=p[g])&&ae(f,n,e,g,p,d);o.push(p),a.push(s)}return new Pe(o,a,n,e)},selectChild:Ne.selectChild,selectChildren:Ne.selectChildren,filter:function(t){"function"!=typeof t&&(t=w(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,a=n[i],u=a.length,s=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&s.push(o);return new Pe(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),a=new Array(r),u=0;u<o;++u)for(var s,l=n[u],c=e[u],h=l.length,f=a[u]=new Array(h),p=0;p<h;++p)(s=l[p]||c[p])&&(f[p]=s);for(;u<r;++u)a[u]=n[u];return new Pe(a,this._parents,this._name,this._id)},selection:function(){return new Se(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=Oe(),r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)if(a=u[l]){var c=le(a,n);ae(a,t,e,l,u,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new Pe(r,this._parents,t,e)},call:Ne.call,nodes:Ne.nodes,node:Ne.node,size:Ne.size,empty:Ne.empty,each:Ne.each,on:function(t,n){var e=this._id;return arguments.length<2?le(this.node(),e).on.on(t):this.each(function(t,n,e){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})}(n)?ue:se;return function(){var a=o(this,t),u=a.on;u!==r&&(i=(r=u).copy()).on(n,e),a.on=i}}(e,t,n))},attr:function(t,n){var e=p(t),r="transform"===e?In:de;return this.attrTween(t,"function"==typeof n?(e.local?xe:me)(e,r,pe(this,"attr."+t,n)):null==n?(e.local?ye:ge)(e):(e.local?ve:_e)(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var r=p(t);return this.tween(e,(r.local?be:we)(r,n))},style:function(t,n,e){var r="transform"==(t+="")?Dn:de;return null==n?this.styleTween(t,function(t,n){var e,r,i;return function(){var o=V(this,t),a=(this.style.removeProperty(t),V(this,t));return o===a?null:o===e&&a===r?i:i=n(e=o,r=a)}}(t,r)).on("end.style."+t,Ce(t)):"function"==typeof n?this.styleTween(t,function(t,n,e){var r,i,o;return function(){var a=V(this,t),u=e(this),s=u+"";return null==u&&(this.style.removeProperty(t),s=u=V(this,t)),a===s?null:a===r&&s===i?o:(i=s,o=n(r=a,u))}}(t,r,pe(this,"style."+t,n))).each(function(t,n){var e,r,i,o,a="style."+n,u="end."+a;return function(){var s=se(this,t),l=s.on,c=null==s.value[a]?o||(o=Ce(n)):void 0;l===e&&i===c||(r=(e=l).copy()).on(u,i=c),s.on=r}}(this._id,t)):this.styleTween(t,function(t,n,e){var r,i,o=e+"";return function(){var a=V(this,t);return a===o?null:a===r?i:i=n(r=a,e)}}(t,r,n),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}(t,o,e)),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(pe(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){return function(n){this.textContent=t.call(this,n)}}(r)),n}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}(this._id))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=le(this.node(),e).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?he:fe)(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?ke:Me)(n,t)):le(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?Ae:ze)(n,t)):le(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw new Error;return function(){se(this,t).ease=n}}(n,t)):le(this.node(),n).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,n){return function(){var e=n.apply(this,arguments);if("function"!=typeof e)throw new Error;se(this,t).ease=e}}(this._id,t))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,a){var u={value:a},s={value:function(){0===--i&&o()}};e.each(function(){var e=se(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(u),n._.interrupt.push(u),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:Ne[Symbol.iterator]};var je={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function Te(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}wt.prototype.interrupt=function(t){return this.each(function(){ce(this,t)})},wt.prototype.transition=function(t){var n,e;t instanceof Pe?(n=t._id,t=t._name):(n=Oe(),(e=je).time=Zn(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,l=0;l<s;++l)(a=u[l])&&ae(a,t,n,l,u,e||Te(a,n));return new Pe(r,this._parents,t,n)};var Re=t=>()=>t;function De(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function Ie(t,n,e){this.k=t,this.x=n,this.y=e}Ie.prototype={constructor:Ie,scale:function(t){return 1===t?this:new Ie(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new Ie(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Ue=new Ie(1,0,0);function Fe(t){for(;!t.__zoom;)if(!(t=t.parentNode))return Ue;return t.__zoom}function Le(t){t.stopImmediatePropagation()}function qe(t){t.preventDefault(),t.stopImmediatePropagation()}function $e(t){return!(t.ctrlKey&&"wheel"!==t.type||t.button)}function Be(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function He(){return this.__zoom||Ue}function Ve(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function Xe(){return navigator.maxTouchPoints||"ontouchstart"in this}function Ge(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],a=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function Ye(){var t,n,e,r=$e,i=Be,o=Ge,a=Ve,u=Xe,s=[0,1/0],l=[[-1/0,-1/0],[1/0,1/0]],c=250,h=qn,f=zt("start","zoom","end"),p=0,d=10;function g(t){t.property("__zoom",He).on("wheel.zoom",w,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",M).filter(u).on("touchstart.zoom",A).on("touchmove.zoom",z).on("touchend.zoom touchcancel.zoom",S).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(t,n){return(n=Math.max(s[0],Math.min(s[1],n)))===t.k?t:new Ie(n,t.x,t.y)}function _(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new Ie(t.k,r,i)}function v(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function m(t,n,e,r){t.on("start.zoom",function(){x(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){x(this,arguments).event(r).end()}).tween("zoom",function(){var t=this,o=arguments,a=x(t,o).event(r),u=i.apply(t,o),s=null==e?v(u):"function"==typeof e?e.apply(t,o):e,l=Math.max(u[1][0]-u[0][0],u[1][1]-u[0][1]),c=t.__zoom,f="function"==typeof n?n.apply(t,o):n,p=h(c.invert(s).concat(l/c.k),f.invert(s).concat(l/f.k));return function(t){if(1===t)t=f;else{var n=p(t),e=l/n[2];t=new Ie(e,s[0]-n[0]*e,s[1]-n[1]*e)}a.zoom(null,t)}})}function x(t,n,e){return!e&&t.__zooming||new b(t,n)}function b(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function w(t,...n){if(r.apply(this,arguments)){var e=x(this,n).event(t),i=this.__zoom,u=Math.max(s[0],Math.min(s[1],i.k*Math.pow(2,a.apply(this,arguments)))),c=Mt(t);if(e.wheel)e.mouse[0][0]===c[0]&&e.mouse[0][1]===c[1]||(e.mouse[1]=i.invert(e.mouse[0]=c)),clearTimeout(e.wheel);else{if(i.k===u)return;e.mouse=[c,i.invert(c)],ce(this),e.start()}qe(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",o(_(y(i,u),e.mouse[0],e.mouse[1]),e.extent,l))}}function k(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,a=x(this,n,!0).event(t),u=kt(t.view).on("mousemove.zoom",function(t){if(qe(t),!a.moved){var n=t.clientX-c,e=t.clientY-h;a.moved=n*n+e*e>p}a.event(t).zoom("mouse",o(_(a.that.__zoom,a.mouse[0]=Mt(t,i),a.mouse[1]),a.extent,l))},!0).on("mouseup.zoom",function(t){u.on("mousemove.zoom mouseup.zoom",null),Rt(t.view,a.moved),qe(t),a.event(t).end()},!0),s=Mt(t,i),c=t.clientX,h=t.clientY;Tt(t.view),Le(t),a.mouse=[s,this.__zoom.invert(s)],ce(this),a.start()}}function M(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,a=Mt(t.changedTouches?t.changedTouches[0]:t,this),u=e.invert(a),s=e.k*(t.shiftKey?.5:2),h=o(_(y(e,s),a,u),i.apply(this,n),l);qe(t),c>0?kt(this).transition().duration(c).call(m,h,a,t):kt(this).call(g.transform,h,a,t)}}function A(e,...i){if(r.apply(this,arguments)){var o,a,u,s,l=e.touches,c=l.length,h=x(this,i,e.changedTouches.length===c).event(e);for(Le(e),a=0;a<c;++a)s=[s=Mt(u=l[a],this),this.__zoom.invert(s),u.identifier],h.touch0?h.touch1||h.touch0[2]===s[2]||(h.touch1=s,h.taps=0):(h.touch0=s,o=!0,h.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(h.taps<2&&(n=s[0],t=setTimeout(function(){t=null},500)),ce(this),h.start())}}function z(t,...n){if(this.__zooming){var e,r,i,a,u=x(this,n).event(t),s=t.changedTouches,c=s.length;for(qe(t),e=0;e<c;++e)i=Mt(r=s[e],this),u.touch0&&u.touch0[2]===r.identifier?u.touch0[0]=i:u.touch1&&u.touch1[2]===r.identifier&&(u.touch1[0]=i);if(r=u.that.__zoom,u.touch1){var h=u.touch0[0],f=u.touch0[1],p=u.touch1[0],d=u.touch1[1],g=(g=p[0]-h[0])*g+(g=p[1]-h[1])*g,v=(v=d[0]-f[0])*v+(v=d[1]-f[1])*v;r=y(r,Math.sqrt(g/v)),i=[(h[0]+p[0])/2,(h[1]+p[1])/2],a=[(f[0]+d[0])/2,(f[1]+d[1])/2]}else{if(!u.touch0)return;i=u.touch0[0],a=u.touch0[1]}u.zoom("touch",o(_(r,i,a),u.extent,l))}}function S(t,...r){if(this.__zooming){var i,o,a=x(this,r).event(t),u=t.changedTouches,s=u.length;for(Le(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<s;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=Mt(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<d)){var l=kt(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return g.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",He),t!==i?m(t,n,e,r):i.interrupt().each(function(){x(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},g.scaleBy=function(t,n,e,r){g.scaleTo(t,function(){return this.__zoom.k*("function"==typeof n?n.apply(this,arguments):n)},e,r)},g.scaleTo=function(t,n,e,r){g.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,a=null==e?v(t):"function"==typeof e?e.apply(this,arguments):e,u=r.invert(a),s="function"==typeof n?n.apply(this,arguments):n;return o(_(y(r,s),a,u),t,l)},e,r)},g.translateBy=function(t,n,e,r){g.transform(t,function(){return o(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),l)},null,r)},g.translateTo=function(t,n,e,r,a){g.transform(t,function(){var t=i.apply(this,arguments),a=this.__zoom,u=null==r?v(t):"function"==typeof r?r.apply(this,arguments):r;return o(Ue.translate(u[0],u[1]).scale(a.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,l)},r,a)},b.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1===++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0===--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=kt(this.that).datum();f.call(t,this.that,new De(t,{sourceEvent:this.sourceEvent,target:g,transform:this.that.__zoom,dispatch:f}),n)}},g.wheelDelta=function(t){return arguments.length?(a="function"==typeof t?t:Re(+t),g):a},g.filter=function(t){return arguments.length?(r="function"==typeof t?t:Re(!!t),g):r},g.touchable=function(t){return arguments.length?(u="function"==typeof t?t:Re(!!t),g):u},g.extent=function(t){return arguments.length?(i="function"==typeof t?t:Re([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),g):i},g.scaleExtent=function(t){return arguments.length?(s[0]=+t[0],s[1]=+t[1],g):[s[0],s[1]]},g.translateExtent=function(t){return arguments.length?(l[0][0]=+t[0][0],l[1][0]=+t[1][0],l[0][1]=+t[0][1],l[1][1]=+t[1][1],g):[[l[0][0],l[0][1]],[l[1][0],l[1][1]]]},g.constrain=function(t){return arguments.length?(o=t,g):o},g.duration=function(t){return arguments.length?(c=+t,g):c},g.interpolate=function(t){return arguments.length?(h=t,g):h},g.on=function(){var t=f.on.apply(f,arguments);return t===f?g:t},g.clickDistance=function(t){return arguments.length?(p=(t=+t)*t,g):Math.sqrt(p)},g.tapDistance=function(t){return arguments.length?(d=+t,g):d},g}Fe.prototype=Ie.prototype;class We extends Map{constructor(t,n=Qe){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),null!=t)for(const[n,e]of t)this.set(n,e)}get(t){return super.get(Ze(this,t))}has(t){return super.has(Ze(this,t))}set(t,n){return super.set(function({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):(t.set(r,e),e)}(this,t),n)}delete(t){return super.delete(function({_intern:t,_key:n},e){const r=n(e);t.has(r)&&(e=t.get(r),t.delete(r));return e}(this,t))}}function Ze({_intern:t,_key:n},e){const r=n(e);return t.has(r)?t.get(r):e}function Qe(t){return null!==t&&"object"==typeof t?t.valueOf():t}function Ke(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e<n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e<i||void 0===e&&i>=i)&&(e=i)}return e}function Je(t,n){let e;if(void 0===n)for(const n of t)null!=n&&(e>n||void 0===e&&n>=n)&&(e=n);else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(e>i||void 0===e&&i>=i)&&(e=i)}return e}var tr="object"==typeof global&&global&&global.Object===Object&&global,nr="object"==typeof self&&self&&self.Object===Object&&self,er=tr||nr||Function("return this")(),rr=er.Symbol,ir=Object.prototype,or=ir.hasOwnProperty,ar=ir.toString,ur=rr?rr.toStringTag:void 0;var sr=Object.prototype.toString;var lr=rr?rr.toStringTag:void 0;function cr(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":lr&&lr in Object(t)?function(t){var n=or.call(t,ur),e=t[ur];try{t[ur]=void 0;var r=!0}catch(t){}var i=ar.call(t);return r&&(n?t[ur]=e:delete t[ur]),i}(t):function(t){return sr.call(t)}(t)}var hr=/\s/;var fr=/^\s+/;function pr(t){return t?t.slice(0,function(t){for(var n=t.length;n--&&hr.test(t.charAt(n)););return n}(t)+1).replace(fr,""):t}function dr(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)}var gr=/^[-+]0x[0-9a-f]+$/i,yr=/^0b[01]+$/i,_r=/^0o[0-7]+$/i,vr=parseInt;function mr(t){if("number"==typeof t)return t;if(function(t){return"symbol"==typeof t||function(t){return null!=t&&"object"==typeof t}(t)&&"[object Symbol]"==cr(t)}(t))return NaN;if(dr(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=dr(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=pr(t);var e=yr.test(t);return e||_r.test(t)?vr(t.slice(2),e?2:8):gr.test(t)?NaN:+t}var xr=function(){return er.Date.now()},br=Math.max,wr=Math.min;function kr(t,n,e){var r,i,o,a,u,s,l=0,c=!1,h=!1,f=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function p(n){var e=r,o=i;return r=i=void 0,l=n,a=t.apply(o,e)}function d(t){var e=t-s;return void 0===s||e>=n||e<0||h&&t-l>=o}function g(){var t=xr();if(d(t))return y(t);u=setTimeout(g,function(t){var e=n-(t-s);return h?wr(e,o-(t-l)):e}(t))}function y(t){return u=void 0,f&&r?p(t):(r=i=void 0,a)}function _(){var t=xr(),e=d(t);if(r=arguments,i=this,s=t,e){if(void 0===u)return function(t){return l=t,u=setTimeout(g,n),c?p(t):a}(s);if(h)return clearTimeout(u),u=setTimeout(g,n),p(s)}return void 0===u&&(u=setTimeout(g,n)),a}return n=mr(n)||0,dr(e)&&(c=!!e.leading,o=(h="maxWait"in e)?br(mr(e.maxWait)||0,n):o,f="trailing"in e?!!e.trailing:f),_.cancel=function(){void 0!==u&&clearTimeout(u),l=0,r=s=i=u=void 0},_.flush=function(){return void 0===u?a:y(xr())},_}var Mr=Object.freeze({Linear:Object.freeze({None:function(t){return t},In:function(t){return t},Out:function(t){return t},InOut:function(t){return t}}),Quadratic:Object.freeze({In:function(t){return t*t},Out:function(t){return t*(2-t)},InOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}}),Cubic:Object.freeze({In:function(t){return t*t*t},Out:function(t){return--t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}}),Quartic:Object.freeze({In:function(t){return t*t*t*t},Out:function(t){return 1- --t*t*t*t},InOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}}),Quintic:Object.freeze({In:function(t){return t*t*t*t*t},Out:function(t){return--t*t*t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}}),Sinusoidal:Object.freeze({In:function(t){return 1-Math.sin((1-t)*Math.PI/2)},Out:function(t){return Math.sin(t*Math.PI/2)},InOut:function(t){return.5*(1-Math.sin(Math.PI*(.5-t)))}}),Exponential:Object.freeze({In:function(t){return 0===t?0:Math.pow(1024,t-1)},Out:function(t){return 1===t?1:1-Math.pow(2,-10*t)},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))}}),Circular:Object.freeze({In:function(t){return 1-Math.sqrt(1-t*t)},Out:function(t){return Math.sqrt(1- --t*t)},InOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}}),Elastic:Object.freeze({In:function(t){return 0===t?0:1===t?1:-Math.pow(2,10*(t-1))*Math.sin(5*(t-1.1)*Math.PI)},Out:function(t){return 0===t?0:1===t?1:Math.pow(2,-10*t)*Math.sin(5*(t-.1)*Math.PI)+1},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?-.5*Math.pow(2,10*(t-1))*Math.sin(5*(t-1.1)*Math.PI):.5*Math.pow(2,-10*(t-1))*Math.sin(5*(t-1.1)*Math.PI)+1}}),Back:Object.freeze({In:function(t){var n=1.70158;return 1===t?1:t*t*((n+1)*t-n)},Out:function(t){var n=1.70158;return 0===t?0:--t*t*((n+1)*t+n)+1},InOut:function(t){var n=2.5949095;return(t*=2)<1?t*t*((n+1)*t-n)*.5:.5*((t-=2)*t*((n+1)*t+n)+2)}}),Bounce:Object.freeze({In:function(t){return 1-Mr.Bounce.Out(1-t)},Out:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},InOut:function(t){return t<.5?.5*Mr.Bounce.In(2*t):.5*Mr.Bounce.Out(2*t-1)+.5}}),generatePow:function(t){return void 0===t&&(t=4),t=(t=t<Number.EPSILON?Number.EPSILON:t)>1e4?1e4:t,{In:function(n){return Math.pow(n,t)},Out:function(n){return 1-Math.pow(1-n,t)},InOut:function(n){return n<.5?Math.pow(2*n,t)/2:(1-Math.pow(2-2*n,t))/2+.5}}}}),Ar=function(){return performance.now()},zr=function(){function t(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._tweens={},this._tweensAddedDuringUpdate={},this.add.apply(this,t)}return t.prototype.getAll=function(){var t=this;return Object.keys(this._tweens).map(function(n){return t._tweens[n]})},t.prototype.removeAll=function(){this._tweens={}},t.prototype.add=function(){for(var t,n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];for(var r=0,i=n;r<i.length;r++){var o=i[r];null===(t=o._group)||void 0===t||t.remove(o),o._group=this,this._tweens[o.getId()]=o,this._tweensAddedDuringUpdate[o.getId()]=o}},t.prototype.remove=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var e=0,r=t;e<r.length;e++){var i=r[e];i._group=void 0,delete this._tweens[i.getId()],delete this._tweensAddedDuringUpdate[i.getId()]}},t.prototype.allStopped=function(){return this.getAll().every(function(t){return!t.isPlaying()})},t.prototype.update=function(t,n){void 0===t&&(t=Ar()),void 0===n&&(n=!0);var e=Object.keys(this._tweens);if(0!==e.length)for(;e.length>0;){this._tweensAddedDuringUpdate={};for(var r=0;r<e.length;r++){var i=this._tweens[e[r]],o=!n;i&&!1===i.update(t,o)&&!n&&this.remove(i)}e=Object.keys(this._tweensAddedDuringUpdate)}},t}(),Sr={Linear:function(t,n){var e=t.length-1,r=e*n,i=Math.floor(r),o=Sr.Utils.Linear;return n<0?o(t[0],t[1],r):n>1?o(t[e],t[e-1],e-r):o(t[i],t[i+1>e?e:i+1],r-i)},Utils:{Linear:function(t,n,e){return(n-t)*e+t}}},Cr=function(){function t(){}return t.nextId=function(){return t._nextId++},t._nextId=0,t}(),Er=new zr,Pr=function(){function t(t,n){this._isPaused=!1,this._pauseStart=0,this._valuesStart={},this._valuesEnd={},this._valuesStartRepeat={},this._duration=1e3,this._isDynamic=!1,this._initialRepeat=0,this._repeat=0,this._yoyo=!1,this._isPlaying=!1,this._reversed=!1,this._delayTime=0,this._startTime=0,this._easingFunction=Mr.Linear.None,this._interpolationFunction=Sr.Linear,this._chainedTweens=[],this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._id=Cr.nextId(),this._isChainStopped=!1,this._propertiesAreSetUp=!1,this._goToEnd=!1,this._object=t,"object"==typeof n?(this._group=n,n.add(this)):!0===n&&(this._group=Er,Er.add(this))}return t.prototype.getId=function(){return this._id},t.prototype.isPlaying=function(){return this._isPlaying},t.prototype.isPaused=function(){return this._isPaused},t.prototype.getDuration=function(){return this._duration},t.prototype.to=function(t,n){if(void 0===n&&(n=1e3),this._isPlaying)throw new Error("Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.");return this._valuesEnd=t,this._propertiesAreSetUp=!1,this._duration=n<0?0:n,this},t.prototype.duration=function(t){return void 0===t&&(t=1e3),this._duration=t<0?0:t,this},t.prototype.dynamic=function(t){return void 0===t&&(t=!1),this._isDynamic=t,this},t.prototype.start=function(t,n){if(void 0===t&&(t=Ar()),void 0===n&&(n=!1),this._isPlaying)return this;if(this._repeat=this._initialRepeat,this._reversed)for(var e in this._reversed=!1,this._valuesStartRepeat)this._swapEndStartRepeatValues(e),this._valuesStart[e]=this._valuesStartRepeat[e];if(this._isPlaying=!0,this._isPaused=!1,this._onStartCallbackFired=!1,this._onEveryStartCallbackFired=!1,this._isChainStopped=!1,this._startTime=t,this._startTime+=this._delayTime,!this._propertiesAreSetUp||n){if(this._propertiesAreSetUp=!0,!this._isDynamic){var r={};for(var i in this._valuesEnd)r[i]=this._valuesEnd[i];this._valuesEnd=r}this._setupProperties(this._object,this._valuesStart,this._valuesEnd,this._valuesStartRepeat,n)}return this},t.prototype.startFromCurrentValues=function(t){return this.start(t,!0)},t.prototype._setupProperties=function(t,n,e,r,i){for(var o in e){var a=t[o],u=Array.isArray(a),s=u?"array":typeof a,l=!u&&Array.isArray(e[o]);if("undefined"!==s&&"function"!==s){if(l){if(0===(y=e[o]).length)continue;for(var c=[a],h=0,f=y.length;h<f;h+=1){var p=this._handleRelativeValue(a,y[h]);if(isNaN(p)){l=!1,console.warn("Found invalid interpolation list. Skipping.");break}c.push(p)}l&&(e[o]=c)}if("object"!==s&&!u||!a||l)(void 0===n[o]||i)&&(n[o]=a),u||(n[o]*=1),r[o]=l?e[o].slice().reverse():n[o]||0;else{n[o]=u?[]:{};var d=a;for(var g in d)n[o][g]=d[g];r[o]=u?[]:{};var y=e[o];if(!this._isDynamic){var _={};for(var g in y)_[g]=y[g];e[o]=y=_}this._setupProperties(d,n[o],y,r[o],i)}}}},t.prototype.stop=function(){return this._isChainStopped||(this._isChainStopped=!0,this.stopChainedTweens()),this._isPlaying?(this._isPlaying=!1,this._isPaused=!1,this._onStopCallback&&this._onStopCallback(this._object),this):this},t.prototype.end=function(){return this._goToEnd=!0,this.update(this._startTime+this._duration),this},t.prototype.pause=function(t){return void 0===t&&(t=Ar()),this._isPaused||!this._isPlaying||(this._isPaused=!0,this._pauseStart=t),this},t.prototype.resume=function(t){return void 0===t&&(t=Ar()),this._isPaused&&this._isPlaying?(this._isPaused=!1,this._startTime+=t-this._pauseStart,this._pauseStart=0,this):this},t.prototype.stopChainedTweens=function(){for(var t=0,n=this._chainedTweens.length;t<n;t++)this._chainedTweens[t].stop();return this},t.prototype.group=function(t){return t?(t.add(this),this):(console.warn("tween.group() without args has been removed, use group.add(tween) instead."),this)},t.prototype.remove=function(){var t;return null===(t=this._group)||void 0===t||t.remove(this),this},t.prototype.delay=function(t){return void 0===t&&(t=0),this._delayTime=t,this},t.prototype.repeat=function(t){return void 0===t&&(t=0),this._initialRepeat=t,this._repeat=t,this},t.prototype.repeatDelay=function(t){return this._repeatDelayTime=t,this},t.prototype.yoyo=function(t){return void 0===t&&(t=!1),this._yoyo=t,this},t.prototype.easing=function(t){return void 0===t&&(t=Mr.Linear.None),this._easingFunction=t,this},t.prototype.interpolation=function(t){return void 0===t&&(t=Sr.Linear),this._interpolationFunction=t,this},t.prototype.chain=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return this._chainedTweens=t,this},t.prototype.onStart=function(t){return this._onStartCallback=t,this},t.prototype.onEveryStart=function(t){return this._onEveryStartCallback=t,this},t.prototype.onUpdate=function(t){return this._onUpdateCallback=t,this},t.prototype.onRepeat=function(t){return this._onRepeatCallback=t,this},t.prototype.onComplete=function(t){return this._onCompleteCallback=t,this},t.prototype.onStop=function(t){return this._onStopCallback=t,this},t.prototype.update=function(n,e){var r,i,o=this;if(void 0===n&&(n=Ar()),void 0===e&&(e=t.autoStartOnUpdate),this._isPaused)return!0;if(!this._goToEnd&&!this._isPlaying){if(!e)return!1;this.start(n,!0)}if(this._goToEnd=!1,n<this._startTime)return!0;!1===this._onStartCallbackFired&&(this._onStartCallback&&this._onStartCallback(this._object),this._onStartCallbackFired=!0),!1===this._onEveryStartCallbackFired&&(this._onEveryStartCallback&&this._onEveryStartCallback(this._object),this._onEveryStartCallbackFired=!0);var a=n-this._startTime,u=this._duration+(null!==(r=this._repeatDelayTime)&&void 0!==r?r:this._delayTime),s=this._duration+this._repeat*u,l=function(){if(0===o._duration)return 1;if(a>s)return 1;var t=Math.trunc(a/u),n=a-t*u,e=Math.min(n/o._duration,1);return 0===e&&a===o._duration?1:e}(),c=this._easingFunction(l);if(this._updateProperties(this._object,this._valuesStart,this._valuesEnd,c),this._onUpdateCallback&&this._onUpdateCallback(this._object,l),0===this._duration||a>=this._duration){if(this._repeat>0){var h=Math.min(Math.trunc((a-this._duration)/u)+1,this._repeat);for(i in isFinite(this._repeat)&&(this._repeat-=h),this._valuesStartRepeat)this._yoyo||"string"!=typeof this._valuesEnd[i]||(this._valuesStartRepeat[i]=this._valuesStartRepeat[i]+parseFloat(this._valuesEnd[i])),this._yoyo&&this._swapEndStartRepeatValues(i),this._valuesStart[i]=this._valuesStartRepeat[i];return this._yoyo&&(this._reversed=!this._reversed),this._startTime+=u*h,this._onRepeatCallback&&this._onRepeatCallback(this._object),this._onEveryStartCallbackFired=!1,!0}this._onCompleteCallback&&this._onCompleteCallback(this._object);for(var f=0,p=this._chainedTweens.length;f<p;f++)this._chainedTweens[f].start(this._startTime+this._duration,!1);return this._isPlaying=!1,!1}return!0},t.prototype._updateProperties=function(t,n,e,r){for(var i in e)if(void 0!==n[i]){var o=n[i]||0,a=e[i],u=Array.isArray(t[i]),s=Array.isArray(a);!u&&s?t[i]=this._interpolationFunction(a,r):"object"==typeof a&&a?this._updateProperties(t[i],o,a,r):"number"==typeof(a=this._handleRelativeValue(o,a))&&(t[i]=o+(a-o)*r)}},t.prototype._handleRelativeValue=function(t,n){return"string"!=typeof n?n:"+"===n.charAt(0)||"-"===n.charAt(0)?t+parseFloat(n):parseFloat(n)},t.prototype._swapEndStartRepeatValues=function(t){var n=this._valuesStartRepeat[t],e=this._valuesEnd[t];this._valuesStartRepeat[t]="string"==typeof e?this._valuesStartRepeat[t]+parseFloat(e):this._valuesEnd[t],this._valuesEnd[t]=n},t.autoStartOnUpdate=!1,t}();Cr.nextId;var Or=Er;function Nr(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function jr(t,n,e){return Object.defineProperty(t,"prototype",{writable:!1}),t}function Tr(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||function(t,n){if(t){if("string"==typeof t)return Nr(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Nr(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}Or.getAll.bind(Or),Or.removeAll.bind(Or),Or.add.bind(Or),Or.remove.bind(Or),Or.update.bind(Or);var Rr=jr(function t(n,e){var r=e.default,i=void 0===r?null:r,o=e.triggerUpdate,a=void 0===o||o,u=e.onChange,s=void 0===u?function(t,n){}:u;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),this.name=n,this.defaultVal=i,this.triggerUpdate=a,this.onChange=s});function Dr(t){var n=t.stateInit,e=void 0===n?function(){return{}}:n,r=t.props,i=void 0===r?{}:r,o=t.methods,a=void 0===o?{}:o,u=t.aliases,s=void 0===u?{}:u,l=t.init,c=void 0===l?function(){}:l,h=t.update,f=void 0===h?function(){}:h,p=Object.keys(i).map(function(t){return new Rr(t,i[t])});return function t(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=!!(this instanceof t?this.constructor:void 0),u=o?r.shift():void 0,l=r[0],h=void 0===l?{}:l,d=Object.assign({},e instanceof Function?e(h):e,{initialised:!1}),g={};function y(t){return _(t,h),v(),y}var _=function(t,n){c.call(y,t,d,n),d.initialised=!0},v=kr(function(){d.initialised&&(f.call(y,d,g),g={})},1);return p.forEach(function(t){y[t.name]=function(t){var n=t.name,e=t.triggerUpdate,r=void 0!==e&&e,i=t.onChange,o=void 0===i?function(t,n){}:i,a=t.defaultVal,u=void 0===a?null:a;return function(t){var e=d[n];if(!arguments.length)return e;var i=void 0===t?u:t;return d[n]=i,o.call(y,i,d,e),!g.hasOwnProperty(n)&&(g[n]=e),r&&v(),y}}(t)}),Object.keys(a).forEach(function(t){y[t]=function(){for(var n,e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return(n=a[t]).call.apply(n,[y,d].concat(r))}}),Object.entries(s).forEach(function(t){var n=Tr(t,2),e=n[0],r=n[1];return y[e]=y[r]}),y.resetProps=function(){return p.forEach(function(t){y[t.name](t.defaultVal)}),y},y.resetProps(),d._rerender=v,o&&u&&y(u),y}}var Ir=function(t){return"function"==typeof t?t:"string"==typeof t?function(n){return n[t]}:function(n){return t}};function Ur(t){return Ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ur(t)}var Fr=/^\s+/,Lr=/\s+$/;function qr(t,n){if(n=n||{},(t=t||"")instanceof qr)return t;if(!(this instanceof qr))return new qr(t,n);var e=function(t){var n={r:0,g:0,b:0},e=1,r=null,i=null,o=null,a=!1,u=!1;"string"==typeof t&&(t=function(t){t=t.replace(Fr,"").replace(Lr,"").toLowerCase();var n,e=!1;if(ii[t])t=ii[t],e=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(n=_i.rgb.exec(t))return{r:n[1],g:n[2],b:n[3]};if(n=_i.rgba.exec(t))return{r:n[1],g:n[2],b:n[3],a:n[4]};if(n=_i.hsl.exec(t))return{h:n[1],s:n[2],l:n[3]};if(n=_i.hsla.exec(t))return{h:n[1],s:n[2],l:n[3],a:n[4]};if(n=_i.hsv.exec(t))return{h:n[1],s:n[2],v:n[3]};if(n=_i.hsva.exec(t))return{h:n[1],s:n[2],v:n[3],a:n[4]};if(n=_i.hex8.exec(t))return{r:li(n[1]),g:li(n[2]),b:li(n[3]),a:pi(n[4]),format:e?"name":"hex8"};if(n=_i.hex6.exec(t))return{r:li(n[1]),g:li(n[2]),b:li(n[3]),format:e?"name":"hex"};if(n=_i.hex4.exec(t))return{r:li(n[1]+""+n[1]),g:li(n[2]+""+n[2]),b:li(n[3]+""+n[3]),a:pi(n[4]+""+n[4]),format:e?"name":"hex8"};if(n=_i.hex3.exec(t))return{r:li(n[1]+""+n[1]),g:li(n[2]+""+n[2]),b:li(n[3]+""+n[3]),format:e?"name":"hex"};return!1}(t));"object"==Ur(t)&&(vi(t.r)&&vi(t.g)&&vi(t.b)?(n=function(t,n,e){return{r:255*ui(t,255),g:255*ui(n,255),b:255*ui(e,255)}}(t.r,t.g,t.b),a=!0,u="%"===String(t.r).substr(-1)?"prgb":"rgb"):vi(t.h)&&vi(t.s)&&vi(t.v)?(r=hi(t.s),i=hi(t.v),n=function(t,n,e){t=6*ui(t,360),n=ui(n,100),e=ui(e,100);var r=Math.floor(t),i=t-r,o=e*(1-n),a=e*(1-i*n),u=e*(1-(1-i)*n),s=r%6,l=[e,a,o,o,u,e][s],c=[u,e,e,a,o,o][s],h=[o,o,u,e,e,a][s];return{r:255*l,g:255*c,b:255*h}}(t.h,r,i),a=!0,u="hsv"):vi(t.h)&&vi(t.s)&&vi(t.l)&&(r=hi(t.s),o=hi(t.l),n=function(t,n,e){var r,i,o;function a(t,n,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(n-t)*e:e<.5?n:e<2/3?t+(n-t)*(2/3-e)*6:t}if(t=ui(t,360),n=ui(n,100),e=ui(e,100),0===n)r=i=o=e;else{var u=e<.5?e*(1+n):e+n-e*n,s=2*e-u;r=a(s,u,t+1/3),i=a(s,u,t),o=a(s,u,t-1/3)}return{r:255*r,g:255*i,b:255*o}}(t.h,r,o),a=!0,u="hsl"),t.hasOwnProperty("a")&&(e=t.a));return e=ai(e),{ok:a,format:t.format||u,r:Math.min(255,Math.max(n.r,0)),g:Math.min(255,Math.max(n.g,0)),b:Math.min(255,Math.max(n.b,0)),a:e}}(t);this._originalInput=t,this._r=e.r,this._g=e.g,this._b=e.b,this._a=e.a,this._roundA=Math.round(100*this._a)/100,this._format=n.format||e.format,this._gradientType=n.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=e.ok}function $r(t,n,e){t=ui(t,255),n=ui(n,255),e=ui(e,255);var r,i,o=Math.max(t,n,e),a=Math.min(t,n,e),u=(o+a)/2;if(o==a)r=i=0;else{var s=o-a;switch(i=u>.5?s/(2-o-a):s/(o+a),o){case t:r=(n-e)/s+(n<e?6:0);break;case n:r=(e-t)/s+2;break;case e:r=(t-n)/s+4}r/=6}return{h:r,s:i,l:u}}function Br(t,n,e){t=ui(t,255),n=ui(n,255),e=ui(e,255);var r,i,o=Math.max(t,n,e),a=Math.min(t,n,e),u=o,s=o-a;if(i=0===o?0:s/o,o==a)r=0;else{switch(o){case t:r=(n-e)/s+(n<e?6:0);break;case n:r=(e-t)/s+2;break;case e:r=(t-n)/s+4}r/=6}return{h:r,s:i,v:u}}function Hr(t,n,e,r){var i=[ci(Math.round(t).toString(16)),ci(Math.round(n).toString(16)),ci(Math.round(e).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function Vr(t,n,e,r){return[ci(fi(r)),ci(Math.round(t).toString(16)),ci(Math.round(n).toString(16)),ci(Math.round(e).toString(16))].join("")}function Xr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.s-=n/100,e.s=si(e.s),qr(e)}function Gr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.s+=n/100,e.s=si(e.s),qr(e)}function Yr(t){return qr(t).desaturate(100)}function Wr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.l+=n/100,e.l=si(e.l),qr(e)}function Zr(t,n){n=0===n?0:n||10;var e=qr(t).toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-n/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-n/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-n/100*255))),qr(e)}function Qr(t,n){n=0===n?0:n||10;var e=qr(t).toHsl();return e.l-=n/100,e.l=si(e.l),qr(e)}function Kr(t,n){var e=qr(t).toHsl(),r=(e.h+n)%360;return e.h=r<0?360+r:r,qr(e)}function Jr(t){var n=qr(t).toHsl();return n.h=(n.h+180)%360,qr(n)}function ti(t,n){if(isNaN(n)||n<=0)throw new Error("Argument to polyad must be a positive number");for(var e=qr(t).toHsl(),r=[qr(t)],i=360/n,o=1;o<n;o++)r.push(qr({h:(e.h+o*i)%360,s:e.s,l:e.l}));return r}function ni(t){var n=qr(t).toHsl(),e=n.h;return[qr(t),qr({h:(e+72)%360,s:n.s,l:n.l}),qr({h:(e+216)%360,s:n.s,l:n.l})]}function ei(t,n,e){n=n||6,e=e||30;var r=qr(t).toHsl(),i=360/e,o=[qr(t)];for(r.h=(r.h-(i*n>>1)+720)%360;--n;)r.h=(r.h+i)%360,o.push(qr(r));return o}function ri(t,n){n=n||6;for(var e=qr(t).toHsv(),r=e.h,i=e.s,o=e.v,a=[],u=1/n;n--;)a.push(qr({h:r,s:i,v:o})),o=(o+u)%1;return a}qr.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,n,e,r=this.toRgb();return t=r.r/255,n=r.g/255,e=r.b/255,.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))+.0722*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))},setAlpha:function(t){return this._a=ai(t),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var t=Br(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=Br(this._r,this._g,this._b),n=Math.round(360*t.h),e=Math.round(100*t.s),r=Math.round(100*t.v);return 1==this._a?"hsv("+n+", "+e+"%, "+r+"%)":"hsva("+n+", "+e+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=$r(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=$r(this._r,this._g,this._b),n=Math.round(360*t.h),e=Math.round(100*t.s),r=Math.round(100*t.l);return 1==this._a?"hsl("+n+", "+e+"%, "+r+"%)":"hsla("+n+", "+e+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return Hr(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,n,e,r,i){var o=[ci(Math.round(t).toString(16)),ci(Math.round(n).toString(16)),ci(Math.round(e).toString(16)),ci(fi(r))];if(i&&o[0].charAt(0)==o[0].charAt(1)&&o[1].charAt(0)==o[1].charAt(1)&&o[2].charAt(0)==o[2].charAt(1)&&o[3].charAt(0)==o[3].charAt(1))return o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0);return o.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(100*ui(this._r,255))+"%",g:Math.round(100*ui(this._g,255))+"%",b:Math.round(100*ui(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+Math.round(100*ui(this._r,255))+"%, "+Math.round(100*ui(this._g,255))+"%, "+Math.round(100*ui(this._b,255))+"%)":"rgba("+Math.round(100*ui(this._r,255))+"%, "+Math.round(100*ui(this._g,255))+"%, "+Math.round(100*ui(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(oi[Hr(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var n="#"+Vr(this._r,this._g,this._b,this._a),e=n,r=this._gradientType?"GradientType = 1, ":"";if(t){var i=qr(t);e="#"+Vr(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+n+",endColorstr="+e+")"},toString:function(t){var n=!!t;t=t||this._format;var e=!1,r=this._a<1&&this._a>=0;return n||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(e=this.toRgbString()),"prgb"===t&&(e=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(e=this.toHexString()),"hex3"===t&&(e=this.toHexString(!0)),"hex4"===t&&(e=this.toHex8String(!0)),"hex8"===t&&(e=this.toHex8String()),"name"===t&&(e=this.toName()),"hsl"===t&&(e=this.toHslString()),"hsv"===t&&(e=this.toHsvString()),e||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return qr(this.toString())},_applyModification:function(t,n){var e=t.apply(null,[this].concat([].slice.call(n)));return this._r=e._r,this._g=e._g,this._b=e._b,this.setAlpha(e._a),this},lighten:function(){return this._applyModification(Wr,arguments)},brighten:function(){return this._applyModification(Zr,arguments)},darken:function(){return this._applyModification(Qr,arguments)},desaturate:function(){return this._applyModification(Xr,arguments)},saturate:function(){return this._applyModification(Gr,arguments)},greyscale:function(){return this._applyModification(Yr,arguments)},spin:function(){return this._applyModification(Kr,arguments)},_applyCombination:function(t,n){return t.apply(null,[this].concat([].slice.call(n)))},analogous:function(){return this._applyCombination(ei,arguments)},complement:function(){return this._applyCombination(Jr,arguments)},monochromatic:function(){return this._applyCombination(ri,arguments)},splitcomplement:function(){return this._applyCombination(ni,arguments)},triad:function(){return this._applyCombination(ti,[3])},tetrad:function(){return this._applyCombination(ti,[4])}},qr.fromRatio=function(t,n){if("object"==Ur(t)){var e={};for(var r in t)t.hasOwnProperty(r)&&(e[r]="a"===r?t[r]:hi(t[r]));t=e}return qr(t,n)},qr.equals=function(t,n){return!(!t||!n)&&qr(t).toRgbString()==qr(n).toRgbString()},qr.random=function(){return qr.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})},qr.mix=function(t,n,e){e=0===e?0:e||50;var r=qr(t).toRgb(),i=qr(n).toRgb(),o=e/100;return qr({r:(i.r-r.r)*o+r.r,g:(i.g-r.g)*o+r.g,b:(i.b-r.b)*o+r.b,a:(i.a-r.a)*o+r.a})},
// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)
// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)
qr.readability=function(t,n){var e=qr(t),r=qr(n);return(Math.max(e.getLuminance(),r.getLuminance())+.05)/(Math.min(e.getLuminance(),r.getLuminance())+.05)},qr.isReadable=function(t,n,e){var r,i,o=qr.readability(t,n);switch(i=!1,(r=function(t){var n,e;n=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),e=(t.size||"small").toLowerCase(),"AA"!==n&&"AAA"!==n&&(n="AA");"small"!==e&&"large"!==e&&(e="small");return{level:n,size:e}}(e)).level+r.size){case"AAsmall":case"AAAlarge":i=o>=4.5;break;case"AAlarge":i=o>=3;break;case"AAAsmall":i=o>=7}return i},qr.mostReadable=function(t,n,e){var r,i,o,a,u=null,s=0;i=(e=e||{}).includeFallbackColors,o=e.level,a=e.size;for(var l=0;l<n.length;l++)(r=qr.readability(t,n[l]))>s&&(s=r,u=qr(n[l]));return qr.isReadable(t,u,{level:o,size:a})||!i?u:(e.includeFallbackColors=!1,qr.mostReadable(t,["#fff","#000"],e))};var ii=qr.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},oi=qr.hexNames=function(t){var n={};for(var e in t)t.hasOwnProperty(e)&&(n[t[e]]=e);return n}(ii);function ai(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function ui(t,n){(function(t){return"string"==typeof t&&-1!=t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var e=function(t){return"string"==typeof t&&-1!=t.indexOf("%")}(t);return t=Math.min(n,Math.max(0,parseFloat(t))),e&&(t=parseInt(t*n,10)/100),Math.abs(t-n)<1e-6?1:t%n/parseFloat(n)}function si(t){return Math.min(1,Math.max(0,t))}function li(t){return parseInt(t,16)}function ci(t){return 1==t.length?"0"+t:""+t}function hi(t){return t<=1&&(t=100*t+"%"),t}function fi(t){return Math.round(255*parseFloat(t)).toString(16)}function pi(t){return li(t)/255}var di,gi,yi,_i=(gi="[\\s|\\(]+("+(di="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+di+")[,|\\s]+("+di+")\\s*\\)?",yi="[\\s|\\(]+("+di+")[,|\\s]+("+di+")[,|\\s]+("+di+")[,|\\s]+("+di+")\\s*\\)?",{CSS_UNIT:new RegExp(di),rgb:new RegExp("rgb"+gi),rgba:new RegExp("rgba"+yi),hsl:new RegExp("hsl"+gi),hsla:new RegExp("hsla"+yi),hsv:new RegExp("hsv"+gi),hsva:new RegExp("hsva"+yi),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function vi(t){return!!_i.CSS_UNIT.exec(t)}function mi(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function xi(t,n,e){if("function"==typeof t?t===n:t.has(n))return arguments.length<3?n:e;throw new TypeError("Private element is not present on this object")}function bi(t,n){return t.get(xi(t,n))}function wi(t,n,e){(function(t,n){if(n.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")})(t,n),n.set(t,e)}function ki(t,n,e){return t.set(xi(t,n),e),e}function Mi(t,n,e){return n&&function(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,zi(r.key),r)}}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ai(t){return function(t){if(Array.isArray(t))return mi(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(t){if("string"==typeof t)return mi(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?mi(t,n):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zi(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof n?n:n+""}var Si,Ci,Ei,Pi,Oi,Ni,ji,Ti,Ri,Di,Ii,Ui,Fi=function(t,n,e){return(t<<16)+(n<<8)+e},Li=function(t,n){return 123*t%Math.pow(2,n)},qi=new WeakMap,$i=new WeakMap,Bi=function(){return Mi(function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;!function(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}(this,t),wi(this,qi,void 0),wi(this,$i,void 0),ki($i,this,n),this.reset()},[{key:"reset",value:function(){ki(qi,this,["__reserved for background__"])}},{key:"register",value:function(t){if(bi(qi,this).length>=Math.pow(2,24-bi($i,this)))return null;var n,e=bi(qi,this).length,r=Li(e,bi($i,this)),i=(n=e+(r<<24-bi($i,this)),"#".concat(Math.min(n,Math.pow(2,24)).toString(16).padStart(6,"0")));return bi(qi,this).push(t),i}},{key:"lookup",value:function(t){if(!t)return null;var n="string"==typeof t?function(t){var n=qr(t).toRgb(),e=n.r,r=n.g,i=n.b;return Fi(e,r,i)}(t):Fi.apply(void 0,Ai(t));if(!n)return null;var e=n&Math.pow(2,24-bi($i,this))-1,r=n>>24-bi($i,this)&Math.pow(2,bi($i,this))-1;return Li(e,bi($i,this))!==r||e>=bi(qi,this).length?null:bi(qi,this)[e]}}])}(),Hi={},Vi=[],Xi=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Gi=Array.isArray;function Yi(t,n){for(var e in n)t[e]=n[e];return t}function Wi(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function Zi(t,n,e,r,i){var o={type:t,props:n,key:e,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==i?++Ei:i,__i:-1,__u:0};return null==i&&null!=Ci.vnode&&Ci.vnode(o),o}function Qi(t){return t.children}function Ki(t,n){this.props=t,this.context=n}function Ji(t,n){if(null==n)return t.__?Ji(t.__,t.__i+1):null;for(var e;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e)return e.__e;return"function"==typeof t.type?Ji(t):null}function to(t){var n,e;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(e=t.__k[n])&&null!=e.__e){t.__e=t.__c.base=e.__e;break}return to(t)}}function no(t){(!t.__d&&(t.__d=!0)&&Oi.push(t)&&!eo.__r++||Ni!=Ci.debounceRendering)&&((Ni=Ci.debounceRendering)||ji)(eo)}function eo(){for(var t,n,e,r,i,o,a,u=1;Oi.length;)Oi.length>u&&Oi.sort(Ti),t=Oi.shift(),u=Oi.length,t.__d&&(e=void 0,i=(r=(n=t).__v).__e,o=[],a=[],n.__P&&((e=Yi({},r)).__v=r.__v+1,Ci.vnode&&Ci.vnode(e),co(n.__P,e,r,n.__n,n.__P.namespaceURI,32&r.__u?[i]:null,o,null==i?Ji(r):i,!!(32&r.__u),a),e.__v=r.__v,e.__.__k[e.__i]=e,ho(o,e,a),e.__e!=i&&to(e)));eo.__r=0}function ro(t,n,e,r,i,o,a,u,s,l,c){var h,f,p,d,g,y,_=r&&r.__k||Vi,v=n.length;for(s=io(e,n,_,s,v),h=0;h<v;h++)null!=(p=e.__k[h])&&(f=-1==p.__i?Hi:_[p.__i]||Hi,p.__i=h,y=co(t,p,f,i,o,a,u,s,l,c),d=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&go(f.ref,null,p),c.push(p.ref,p.__c||d,p)),null==g&&null!=d&&(g=d),4&p.__u||f.__k===p.__k?s=oo(p,s,t):"function"==typeof p.type&&void 0!==y?s=y:d&&(s=d.nextSibling),p.__u&=-7);return e.__e=g,s}function io(t,n,e,r,i){var o,a,u,s,l,c=e.length,h=c,f=0;for(t.__k=new Array(i),o=0;o<i;o++)null!=(a=n[o])&&"boolean"!=typeof a&&"function"!=typeof a?(s=o+f,(a=t.__k[o]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?Zi(null,a,null,null,null):Gi(a)?Zi(Qi,{children:a},null,null,null):null==a.constructor&&a.__b>0?Zi(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=t,a.__b=t.__b+1,u=null,-1!=(l=a.__i=ao(a,e,s,h))&&(h--,(u=e[l])&&(u.__u|=2)),null==u||null==u.__v?(-1==l&&(i>c?f--:i<c&&f++),"function"!=typeof a.type&&(a.__u|=4)):l!=s&&(l==s-1?f--:l==s+1?f++:(l>s?f--:f++,a.__u|=4))):t.__k[o]=null;if(h)for(o=0;o<c;o++)null!=(u=e[o])&&!(2&u.__u)&&(u.__e==r&&(r=Ji(u)),yo(u,u));return r}function oo(t,n,e){var r,i;if("function"==typeof t.type){for(r=t.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=t,n=oo(r[i],n,e));return n}t.__e!=n&&(n&&t.type&&!e.contains(n)&&(n=Ji(t)),e.insertBefore(t.__e,n||null),n=t.__e);do{n=n&&n.nextSibling}while(null!=n&&8==n.nodeType);return n}function ao(t,n,e,r){var i,o,a=t.key,u=t.type,s=n[e];if(null===s&&null==t.key||s&&a==s.key&&u==s.type&&!(2&s.__u))return e;if(r>(null==s||2&s.__u?0:1))for(i=e-1,o=e+1;i>=0||o<n.length;){if(i>=0){if((s=n[i])&&!(2&s.__u)&&a==s.key&&u==s.type)return i;i--}if(o<n.length){if((s=n[o])&&!(2&s.__u)&&a==s.key&&u==s.type)return o;o++}}return-1}function uo(t,n,e){"-"==n[0]?t.setProperty(n,null==e?"":e):t[n]=null==e?"":"number"!=typeof e||Xi.test(n)?e:e+"px"}function so(t,n,e,r,i){var o,a;t:if("style"==n)if("string"==typeof e)t.style.cssText=e;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(n in r)e&&n in e||uo(t.style,n,"");if(e)for(n in e)r&&e[n]==r[n]||uo(t.style,n,e[n])}else if("o"==n[0]&&"n"==n[1])o=n!=(n=n.replace(Ri,"$1")),a=n.toLowerCase(),n=a in t||"onFocusOut"==n||"onFocusIn"==n?a.slice(2):n.slice(2),t.l||(t.l={}),t.l[n+o]=e,e?r?e.u=r.u:(e.u=Di,t.addEventListener(n,o?Ui:Ii,o)):t.removeEventListener(n,o?Ui:Ii,o);else{if("http://www.w3.org/2000/svg"==i)n=n.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=n&&"height"!=n&&"href"!=n&&"list"!=n&&"form"!=n&&"tabIndex"!=n&&"download"!=n&&"rowSpan"!=n&&"colSpan"!=n&&"role"!=n&&"popover"!=n&&n in t)try{t[n]=null==e?"":e;break t}catch(t){}"function"==typeof e||(null==e||!1===e&&"-"!=n[4]?t.removeAttribute(n):t.setAttribute(n,"popover"==n&&1==e?"":e))}}function lo(t){return function(n){if(this.l){var e=this.l[n.type+t];if(null==n.t)n.t=Di++;else if(n.t<e.u)return;return e(Ci.event?Ci.event(n):n)}}}function co(t,n,e,r,i,o,a,u,s,l){var c,h,f,p,d,g,y,_,v,m,x,b,w,k,M,A,z,S=n.type;if(null!=n.constructor)return null;128&e.__u&&(s=!!(32&e.__u),o=[u=n.__e=e.__e]),(c=Ci.__b)&&c(n);t:if("function"==typeof S)try{if(_=n.props,v="prototype"in S&&S.prototype.render,m=(c=S.contextType)&&r[c.__c],x=c?m?m.props.value:c.__:r,e.__c?y=(h=n.__c=e.__c).__=h.__E:(v?n.__c=h=new S(_,x):(n.__c=h=new Ki(_,x),h.constructor=S,h.render=_o),m&&m.sub(h),h.props=_,h.state||(h.state={}),h.context=x,h.__n=r,f=h.__d=!0,h.__h=[],h._sb=[]),v&&null==h.__s&&(h.__s=h.state),v&&null!=S.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=Yi({},h.__s)),Yi(h.__s,S.getDerivedStateFromProps(_,h.__s))),p=h.props,d=h.state,h.__v=n,f)v&&null==S.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),v&&null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(v&&null==S.getDerivedStateFromProps&&_!==p&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(_,x),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(_,h.__s,x)||n.__v==e.__v){for(n.__v!=e.__v&&(h.props=_,h.state=h.__s,h.__d=!1),n.__e=e.__e,n.__k=e.__k,n.__k.some(function(t){t&&(t.__=n)}),b=0;b<h._sb.length;b++)h.__h.push(h._sb[b]);h._sb=[],h.__h.length&&a.push(h);break t}null!=h.componentWillUpdate&&h.componentWillUpdate(_,h.__s,x),v&&null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(p,d,g)})}if(h.context=x,h.props=_,h.__P=t,h.__e=!1,w=Ci.__r,k=0,v){for(h.state=h.__s,h.__d=!1,w&&w(n),c=h.render(h.props,h.state,h.context),M=0;M<h._sb.length;M++)h.__h.push(h._sb[M]);h._sb=[]}else do{h.__d=!1,w&&w(n),c=h.render(h.props,h.state,h.context),h.state=h.__s}while(h.__d&&++k<25);h.state=h.__s,null!=h.getChildContext&&(r=Yi(Yi({},r),h.getChildContext())),v&&!f&&null!=h.getSnapshotBeforeUpdate&&(g=h.getSnapshotBeforeUpdate(p,d)),A=c,null!=c&&c.type===Qi&&null==c.key&&(A=fo(c.props.children)),u=ro(t,Gi(A)?A:[A],n,e,r,i,o,a,u,s,l),h.base=n.__e,n.__u&=-161,h.__h.length&&a.push(h),y&&(h.__E=h.__=null)}catch(t){if(n.__v=null,s||null!=o)if(t.then){for(n.__u|=s?160:128;u&&8==u.nodeType&&u.nextSibling;)u=u.nextSibling;o[o.indexOf(u)]=null,n.__e=u}else for(z=o.length;z--;)Wi(o[z]);else n.__e=e.__e,n.__k=e.__k;Ci.__e(t,n,e)}else null==o&&n.__v==e.__v?(n.__k=e.__k,n.__e=e.__e):u=n.__e=po(e.__e,n,e,r,i,o,a,s,l);return(c=Ci.diffed)&&c(n),128&n.__u?void 0:u}function ho(t,n,e){for(var r=0;r<e.length;r++)go(e[r],e[++r],e[++r]);Ci.__c&&Ci.__c(n,t),t.some(function(n){try{t=n.__h,n.__h=[],t.some(function(t){t.call(n)})}catch(t){Ci.__e(t,n.__v)}})}function fo(t){return"object"!=typeof t||null==t||t.__b&&t.__b>0?t:Gi(t)?t.map(fo):Yi({},t)}function po(t,n,e,r,i,o,a,u,s){var l,c,h,f,p,d,g,y=e.props,_=n.props,v=n.type;if("svg"==v?i="http://www.w3.org/2000/svg":"math"==v?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=o)for(l=0;l<o.length;l++)if((p=o[l])&&"setAttribute"in p==!!v&&(v?p.localName==v:3==p.nodeType)){t=p,o[l]=null;break}if(null==t){if(null==v)return document.createTextNode(_);t=document.createElementNS(i,v,_.is&&_),u&&(Ci.__m&&Ci.__m(n,o),u=!1),o=null}if(null==v)y===_||u&&t.data==_||(t.data=_);else{if(o=o&&Si.call(t.childNodes),y=e.props||Hi,!u&&null!=o)for(y={},l=0;l<t.attributes.length;l++)y[(p=t.attributes[l]).name]=p.value;for(l in y)if(p=y[l],"children"==l);else if("dangerouslySetInnerHTML"==l)h=p;else if(!(l in _)){if("value"==l&&"defaultValue"in _||"checked"==l&&"defaultChecked"in _)continue;so(t,l,null,p,i)}for(l in _)p=_[l],"children"==l?f=p:"dangerouslySetInnerHTML"==l?c=p:"value"==l?d=p:"checked"==l?g=p:u&&"function"!=typeof p||y[l]===p||so(t,l,p,y[l],i);if(c)u||h&&(c.__html==h.__html||c.__html==t.innerHTML)||(t.innerHTML=c.__html),n.__k=[];else if(h&&(t.innerHTML=""),ro("template"==n.type?t.content:t,Gi(f)?f:[f],n,e,r,"foreignObject"==v?"http://www.w3.org/1999/xhtml":i,o,a,o?o[0]:e.__k&&Ji(e,0),u,s),null!=o)for(l=o.length;l--;)Wi(o[l]);u||(l="value","progress"==v&&null==d?t.removeAttribute("value"):null!=d&&(d!==t[l]||"progress"==v&&!d||"option"==v&&d!=y[l])&&so(t,l,d,y[l],i),l="checked",null!=g&&g!=t[l]&&so(t,l,g,y[l],i))}return t}function go(t,n,e){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==n||(t.__u=t(n))}else t.current=n}catch(t){Ci.__e(t,e)}}function yo(t,n,e){var r,i;if(Ci.unmount&&Ci.unmount(t),(r=t.ref)&&(r.current&&r.current!=t.__e||go(r,null,n)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){Ci.__e(t,n)}r.base=r.__P=null}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&yo(r[i],n,e||"function"!=typeof t.type);e||Wi(t.__e),t.__c=t.__=t.__e=void 0}function _o(t,n,e){return this.constructor(t,e)}function vo(t,n,e){var r,i,o;n==document&&(n=document.documentElement),Ci.__&&Ci.__(t,n),r=!1?null:n.__k,i=[],o=[],co(n,t=n.__k=function(t,n,e){var r,i,o,a={};for(o in n)"key"==o?r=n[o]:"ref"==o?i=n[o]:a[o]=n[o];if(arguments.length>2&&(a.children=arguments.length>3?Si.call(arguments,2):e),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===a[o]&&(a[o]=t.defaultProps[o]);return Zi(t,a,r,i,null)}(Qi,null,[t]),r||Hi,Hi,n.namespaceURI,r?null:n.firstChild?Si.call(n.childNodes):null,i,r?r.__e:n.firstChild,false,o),ho(i,t,o)}function mo(t,n,e){var r,i,o,a,u=Yi({},t.props);for(o in t.type&&t.type.defaultProps&&(a=t.type.defaultProps),n)"key"==o?r=n[o]:"ref"==o?i=n[o]:u[o]=void 0===n[o]&&null!=a?a[o]:n[o];return arguments.length>2&&(u.children=arguments.length>3?Si.call(arguments,2):e),Zi(t.type,u,r||t.key,i||t.ref,null)}function xo(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function bo(t,n,e){return(n=function(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function wo(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable})),e.push.apply(e,r)}return e}function ko(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||function(t,n){if(t){if("string"==typeof t)return xo(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?xo(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mo(t){return Mo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mo(t)}Si=Vi.slice,Ci={__e:function(t,n,e,r){for(var i,o,a;n=n.__;)if((i=n.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(t)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),a=i.__d),a)return i.__E=i}catch(n){t=n}throw t}},Ei=0,Pi=function(t){return null!=t&&null==t.constructor},Ki.prototype.setState=function(t,n){var e;e=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=Yi({},this.state),"function"==typeof t&&(t=t(Yi({},e),this.props)),t&&Yi(e,t),null!=t&&this.__v&&(n&&this._sb.push(n),no(this))},Ki.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),no(this))},Ki.prototype.render=Qi,Oi=[],ji="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Ti=function(t,n){return t.__v.__b-n.__v.__b},eo.__r=0,Ri=/(PointerCapture)$|Capture$/i,Di=0,Ii=lo(!1),Ui=lo(!0);var Ao=function(t){if("object"!==Mo(t))return t;var n,e=mo(t);e.props&&(e.props=function(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?wo(Object(e),!0).forEach(function(n){bo(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):wo(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}({},e.props),null!=e&&null!==(n=e.props)&&void 0!==n&&n.children&&(e.props.children=Array.isArray(e.props.children)?e.props.children.map(Ao):Ao(e.props.children)));return e};!function(t,n){void 0===n&&(n={});var e=n.insertAt;if("undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===e&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}(".float-tooltip-kap {\n  position: absolute;\n  width: max-content; /* prevent shrinking near right edge */\n  max-width: max(50%, 150px);\n  padding: 3px 5px;\n  border-radius: 3px;\n  font: 12px sans-serif;\n  color: #eee;\n  background: rgba(0,0,0,0.6);\n  pointer-events: none;\n}\n");var zo=Dr({props:{content:{default:!1},offsetX:{triggerUpdate:!1},offsetY:{triggerUpdate:!1}},init:function(t,n){var e=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).style,r=void 0===e?{}:e,i=kt(!!t&&"object"===Mo(t)&&!!t.node&&"function"==typeof t.node?t.node():t);"static"===i.style("position")&&i.style("position","relative"),n.tooltipEl=i.append("div").attr("class","float-tooltip-kap"),Object.entries(r).forEach(function(t){var e=ko(t,2),r=e[0],i=e[1];return n.tooltipEl.style(r,i)}),n.tooltipEl.style("left","-10000px").style("display","none");var o="tooltip-".concat(Math.round(1e12*Math.random()));n.mouseInside=!1,i.on("mousemove.".concat(o),function(t){n.mouseInside=!0;var e=Mt(t),r=i.node(),o=r.offsetWidth,a=r.offsetHeight,u=[null===n.offsetX||void 0===n.offsetX?"-".concat(e[0]/o*100,"%"):"number"==typeof n.offsetX?"calc(-50% + ".concat(n.offsetX,"px)"):n.offsetX,null===n.offsetY||void 0===n.offsetY?a>130&&a-e[1]<100?"calc(-100% - 6px)":"21px":"number"==typeof n.offsetY?n.offsetY<0?"calc(-100% - ".concat(Math.abs(n.offsetY),"px)"):"".concat(n.offsetY,"px"):n.offsetY];n.tooltipEl.style("left",e[0]+"px").style("top",e[1]+"px").style("transform","translate(".concat(u.join(","),")")),n.content&&n.tooltipEl.style("display","inline")}),i.on("mouseover.".concat(o),function(){n.mouseInside=!0,n.content&&n.tooltipEl.style("display","inline")}),i.on("mouseout.".concat(o),function(){n.mouseInside=!1,n.tooltipEl.style("display","none")})},update:function(t){var n,e;t.tooltipEl.style("display",t.content&&t.mouseInside?"inline":"none"),t.content?t.content instanceof HTMLElement?(t.tooltipEl.text(""),t.tooltipEl.append(function(){return t.content})):"string"==typeof t.content?t.tooltipEl.html(t.content):!function(t){return Pi(mo(t))}(t.content)?(t.tooltipEl.style("display","none"),console.warn("Tooltip content is invalid, skipping.",t.content,t.content.toString())):(t.tooltipEl.text(""),n=t.content,delete(e=t.tooltipEl.node()).__k,vo(Ao(n),e)):t.tooltipEl.text("")}});function So(t,n,e){var r,i=1;function o(){var o,a,u=r.length,s=0,l=0,c=0;for(o=0;o<u;++o)s+=(a=r[o]).x||0,l+=a.y||0,c+=a.z||0;for(s=(s/u-t)*i,l=(l/u-n)*i,c=(c/u-e)*i,o=0;o<u;++o)a=r[o],s&&(a.x-=s),l&&(a.y-=l),c&&(a.z-=c)}return null==t&&(t=0),null==n&&(n=0),null==e&&(e=0),o.initialize=function(t){r=t},o.x=function(n){return arguments.length?(t=+n,o):t},o.y=function(t){return arguments.length?(n=+t,o):n},o.z=function(t){return arguments.length?(e=+t,o):e},o.strength=function(t){return arguments.length?(i=+t,o):i},o}function Co(t,n,e){if(isNaN(n))return t;var r,i,o,a,u,s,l=t._root,c={data:e},h=t._x0,f=t._x1;if(!l)return t._root=c,t;for(;l.length;)if((a=n>=(i=(h+f)/2))?h=i:f=i,r=l,!(l=l[u=+a]))return r[u]=c,t;if(n===(o=+t._x.call(null,l.data)))return c.next=l,r?r[u]=c:t._root=c,t;do{r=r?r[u]=new Array(2):t._root=new Array(2),(a=n>=(i=(h+f)/2))?h=i:f=i}while((u=+a)===(s=+(o>=i)));return r[s]=l,r[u]=c,t}function Eo(t,n,e){this.node=t,this.x0=n,this.x1=e}function Po(t){return t[0]}function Oo(t,n){var e=new No(null==n?Po:n,NaN,NaN);return null==t?e:e.addAll(t)}function No(t,n,e){this._x=t,this._x0=n,this._x1=e,this._root=void 0}function jo(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var To=Oo.prototype=No.prototype;function Ro(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,a,u,s,l,c,h,f,p=t._root,d={data:r},g=t._x0,y=t._y0,_=t._x1,v=t._y1;if(!p)return t._root=d,t;for(;p.length;)if((l=n>=(o=(g+_)/2))?g=o:_=o,(c=e>=(a=(y+v)/2))?y=a:v=a,i=p,!(p=p[h=c<<1|l]))return i[h]=d,t;if(u=+t._x.call(null,p.data),s=+t._y.call(null,p.data),n===u&&e===s)return d.next=p,i?i[h]=d:t._root=d,t;do{i=i?i[h]=new Array(4):t._root=new Array(4),(l=n>=(o=(g+_)/2))?g=o:_=o,(c=e>=(a=(y+v)/2))?y=a:v=a}while((h=c<<1|l)==(f=(s>=a)<<1|u>=o));return i[f]=p,i[h]=d,t}function Do(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function Io(t){return t[0]}function Uo(t){return t[1]}function Fo(t,n,e){var r=new Lo(null==n?Io:n,null==e?Uo:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function Lo(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function qo(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}To.copy=function(){var t,n,e=new No(this._x,this._x0,this._x1),r=this._root;if(!r)return e;if(!r.length)return e._root=jo(r),e;for(t=[{source:r,target:e._root=new Array(2)}];r=t.pop();)for(var i=0;i<2;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(2)}):r.target[i]=jo(n));return e},To.add=function(t){const n=+this._x.call(null,t);return Co(this.cover(n),n,t)},To.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n);let r=1/0,i=-1/0;for(let o,a=0;a<n;++a)isNaN(o=+this._x.call(null,t[a]))||(e[a]=o,o<r&&(r=o),o>i&&(i=o));if(r>i)return this;this.cover(r).cover(i);for(let r=0;r<n;++r)Co(this,e[r],t[r]);return this},To.cover=function(t){if(isNaN(t=+t))return this;var n=this._x0,e=this._x1;if(isNaN(n))e=(n=Math.floor(t))+1;else{for(var r,i,o=e-n||1,a=this._root;n>t||t>=e;)switch(i=+(t<n),(r=new Array(2))[i]=a,a=r,o*=2,i){case 0:e=n+o;break;case 1:n=e-o}this._root&&this._root.length&&(this._root=a)}return this._x0=n,this._x1=e,this},To.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},To.extent=function(t){return arguments.length?this.cover(+t[0][0]).cover(+t[1][0]):isNaN(this._x0)?void 0:[[this._x0],[this._x1]]},To.find=function(t,n){var e,r,i,o,a,u=this._x0,s=this._x1,l=[],c=this._root;for(c&&l.push(new Eo(c,u,s)),null==n?n=1/0:(u=t-n,s=t+n);o=l.pop();)if(!(!(c=o.node)||(r=o.x0)>s||(i=o.x1)<u))if(c.length){var h=(r+i)/2;l.push(new Eo(c[1],h,i),new Eo(c[0],r,h)),(a=+(t>=h))&&(o=l[l.length-1],l[l.length-1]=l[l.length-1-a],l[l.length-1-a]=o)}else{var f=Math.abs(t-+this._x.call(null,c.data));f<n&&(n=f,u=t-f,s=t+f,e=c.data)}return e},To.remove=function(t){if(isNaN(o=+this._x.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c=this._root,h=this._x0,f=this._x1;if(!c)return this;if(c.length)for(;;){if((u=o>=(a=(h+f)/2))?h=a:f=a,n=c,!(c=c[s=+u]))return this;if(!c.length)break;n[s+1&1]&&(e=n,l=s)}for(;c.data!==t;)if(r=c,!(c=c.next))return this;return(i=c.next)&&delete c.next,r?(i?r.next=i:delete r.next,this):n?(i?n[s]=i:delete n[s],(c=n[0]||n[1])&&c===(n[1]||n[0])&&!c.length&&(e?e[l]=c:this._root=c),this):(this._root=i,this)},To.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},To.root=function(){return this._root},To.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},To.visit=function(t){var n,e,r,i,o=[],a=this._root;for(a&&o.push(new Eo(a,this._x0,this._x1));n=o.pop();)if(!t(a=n.node,r=n.x0,i=n.x1)&&a.length){var u=(r+i)/2;(e=a[1])&&o.push(new Eo(e,u,i)),(e=a[0])&&o.push(new Eo(e,r,u))}return this},To.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Eo(this._root,this._x0,this._x1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.x1,s=(a+u)/2;(o=i[0])&&e.push(new Eo(o,a,s)),(o=i[1])&&e.push(new Eo(o,s,u))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.x1);return this},To.x=function(t){return arguments.length?(this._x=t,this):this._x};var $o=Fo.prototype=Lo.prototype;function Bo(t,n,e,r,i){if(isNaN(n)||isNaN(e)||isNaN(r))return t;var o,a,u,s,l,c,h,f,p,d,g,y,_=t._root,v={data:i},m=t._x0,x=t._y0,b=t._z0,w=t._x1,k=t._y1,M=t._z1;if(!_)return t._root=v,t;for(;_.length;)if((f=n>=(a=(m+w)/2))?m=a:w=a,(p=e>=(u=(x+k)/2))?x=u:k=u,(d=r>=(s=(b+M)/2))?b=s:M=s,o=_,!(_=_[g=d<<2|p<<1|f]))return o[g]=v,t;if(l=+t._x.call(null,_.data),c=+t._y.call(null,_.data),h=+t._z.call(null,_.data),n===l&&e===c&&r===h)return v.next=_,o?o[g]=v:t._root=v,t;do{o=o?o[g]=new Array(8):t._root=new Array(8),(f=n>=(a=(m+w)/2))?m=a:w=a,(p=e>=(u=(x+k)/2))?x=u:k=u,(d=r>=(s=(b+M)/2))?b=s:M=s}while((g=d<<2|p<<1|f)==(y=(h>=s)<<2|(c>=u)<<1|l>=a));return o[y]=_,o[g]=v,t}function Ho(t,n,e,r,i,o,a){this.node=t,this.x0=n,this.y0=e,this.z0=r,this.x1=i,this.y1=o,this.z1=a}$o.copy=function(){var t,n,e=new Lo(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=qo(r),e;for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=qo(n));return e},$o.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t);return Ro(this.cover(n,e),n,e,t)},$o.addAll=function(t){var n,e,r,i,o=t.length,a=new Array(o),u=new Array(o),s=1/0,l=1/0,c=-1/0,h=-1/0;for(e=0;e<o;++e)isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n))||(a[e]=r,u[e]=i,r<s&&(s=r),r>c&&(c=r),i<l&&(l=i),i>h&&(h=i));if(s>c||l>h)return this;for(this.cover(s,l).cover(c,h),e=0;e<o;++e)Ro(this,a[e],u[e],t[e]);return this},$o.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var a,u,s=i-e||1,l=this._root;e>t||t>=i||r>n||n>=o;)switch(u=(n<r)<<1|t<e,(a=new Array(4))[u]=l,l=a,s*=2,u){case 0:i=e+s,o=r+s;break;case 1:e=i-s,o=r+s;break;case 2:i=e+s,r=o-s;break;case 3:e=i-s,r=o-s}this._root&&this._root.length&&(this._root=l)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},$o.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},$o.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},$o.find=function(t,n,e){var r,i,o,a,u,s,l,c=this._x0,h=this._y0,f=this._x1,p=this._y1,d=[],g=this._root;for(g&&d.push(new Do(g,c,h,f,p)),null==e?e=1/0:(c=t-e,h=n-e,f=t+e,p=n+e,e*=e);s=d.pop();)if(!(!(g=s.node)||(i=s.x0)>f||(o=s.y0)>p||(a=s.x1)<c||(u=s.y1)<h))if(g.length){var y=(i+a)/2,_=(o+u)/2;d.push(new Do(g[3],y,_,a,u),new Do(g[2],i,_,y,u),new Do(g[1],y,o,a,_),new Do(g[0],i,o,y,_)),(l=(n>=_)<<1|t>=y)&&(s=d[d.length-1],d[d.length-1]=d[d.length-1-l],d[d.length-1-l]=s)}else{var v=t-+this._x.call(null,g.data),m=n-+this._y.call(null,g.data),x=v*v+m*m;if(x<e){var b=Math.sqrt(e=x);c=t-b,h=n-b,f=t+b,p=n+b,r=g.data}}return r},$o.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c,h,f,p=this._root,d=this._x0,g=this._y0,y=this._x1,_=this._y1;if(!p)return this;if(p.length)for(;;){if((l=o>=(u=(d+y)/2))?d=u:y=u,(c=a>=(s=(g+_)/2))?g=s:_=s,n=p,!(p=p[h=c<<1|l]))return this;if(!p.length)break;(n[h+1&3]||n[h+2&3]||n[h+3&3])&&(e=n,f=h)}for(;p.data!==t;)if(r=p,!(p=p.next))return this;return(i=p.next)&&delete p.next,r?(i?r.next=i:delete r.next,this):n?(i?n[h]=i:delete n[h],(p=n[0]||n[1]||n[2]||n[3])&&p===(n[3]||n[2]||n[1]||n[0])&&!p.length&&(e?e[f]=p:this._root=p),this):(this._root=i,this)},$o.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},$o.root=function(){return this._root},$o.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},$o.visit=function(t){var n,e,r,i,o,a,u=[],s=this._root;for(s&&u.push(new Do(s,this._x0,this._y0,this._x1,this._y1));n=u.pop();)if(!t(s=n.node,r=n.x0,i=n.y0,o=n.x1,a=n.y1)&&s.length){var l=(r+o)/2,c=(i+a)/2;(e=s[3])&&u.push(new Do(e,l,c,o,a)),(e=s[2])&&u.push(new Do(e,r,c,l,a)),(e=s[1])&&u.push(new Do(e,l,i,o,c)),(e=s[0])&&u.push(new Do(e,r,i,l,c))}return this},$o.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Do(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,s=n.x1,l=n.y1,c=(a+s)/2,h=(u+l)/2;(o=i[0])&&e.push(new Do(o,a,u,c,h)),(o=i[1])&&e.push(new Do(o,c,u,s,h)),(o=i[2])&&e.push(new Do(o,a,h,c,l)),(o=i[3])&&e.push(new Do(o,c,h,s,l))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},$o.x=function(t){return arguments.length?(this._x=t,this):this._x},$o.y=function(t){return arguments.length?(this._y=t,this):this._y};const Vo=(t,n,e,r,i,o)=>Math.sqrt((t-r)**2+(n-i)**2+(e-o)**2);function Xo(t){return t[0]}function Go(t){return t[1]}function Yo(t){return t[2]}function Wo(t,n,e,r){var i=new Zo(null==n?Xo:n,null==e?Go:e,null==r?Yo:r,NaN,NaN,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function Zo(t,n,e,r,i,o,a,u,s){this._x=t,this._y=n,this._z=e,this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=u,this._z1=s,this._root=void 0}function Qo(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var Ko=Wo.prototype=Zo.prototype;function Jo(t){return function(){return t}}function ta(t){return 1e-6*(t()-.5)}function na(t){return t.index}function ea(t,n){var e=t.get(n);if(!e)throw new Error("node not found: "+n);return e}function ra(t){var n,e,r,i,o,a,u,s=na,l=function(t){return 1/Math.min(o[t.source.index],o[t.target.index])},c=Jo(30),h=1;function f(r){for(var o=0,s=t.length;o<h;++o)for(var l,c,f,p,d,g=0,y=0,_=0,v=0;g<s;++g)c=(l=t[g]).source,y=(f=l.target).x+f.vx-c.x-c.vx||ta(u),i>1&&(_=f.y+f.vy-c.y-c.vy||ta(u)),i>2&&(v=f.z+f.vz-c.z-c.vz||ta(u)),y*=p=((p=Math.sqrt(y*y+_*_+v*v))-e[g])/p*r*n[g],_*=p,v*=p,f.vx-=y*(d=a[g]),i>1&&(f.vy-=_*d),i>2&&(f.vz-=v*d),c.vx+=y*(d=1-d),i>1&&(c.vy+=_*d),i>2&&(c.vz+=v*d)}function p(){if(r){var i,u,l=r.length,c=t.length,h=new Map(r.map((t,n)=>[s(t,n,r),t]));for(i=0,o=new Array(l);i<c;++i)(u=t[i]).index=i,"object"!=typeof u.source&&(u.source=ea(h,u.source)),"object"!=typeof u.target&&(u.target=ea(h,u.target)),o[u.source.index]=(o[u.source.index]||0)+1,o[u.target.index]=(o[u.target.index]||0)+1;for(i=0,a=new Array(c);i<c;++i)u=t[i],a[i]=o[u.source.index]/(o[u.source.index]+o[u.target.index]);n=new Array(c),d(),e=new Array(c),g()}}function d(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+l(t[e],e,t)}function g(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+c(t[n],n,t)}return null==t&&(t=[]),f.initialize=function(t,...n){r=t,u=n.find(t=>"function"==typeof t)||Math.random,i=n.find(t=>[1,2,3].includes(t))||2,p()},f.links=function(n){return arguments.length?(t=n,p(),f):t},f.id=function(t){return arguments.length?(s=t,f):s},f.iterations=function(t){return arguments.length?(h=+t,f):h},f.strength=function(t){return arguments.length?(l="function"==typeof t?t:Jo(+t),d(),f):l},f.distance=function(t){return arguments.length?(c="function"==typeof t?t:Jo(+t),g(),f):c},f}Ko.copy=function(){var t,n,e=new Zo(this._x,this._y,this._z,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1),r=this._root;if(!r)return e;if(!r.length)return e._root=Qo(r),e;for(t=[{source:r,target:e._root=new Array(8)}];r=t.pop();)for(var i=0;i<8;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(8)}):r.target[i]=Qo(n));return e},Ko.add=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t),r=+this._z.call(null,t);return Bo(this.cover(n,e,r),n,e,r,t)},Ko.addAll=function(t){Array.isArray(t)||(t=Array.from(t));const n=t.length,e=new Float64Array(n),r=new Float64Array(n),i=new Float64Array(n);let o=1/0,a=1/0,u=1/0,s=-1/0,l=-1/0,c=-1/0;for(let h,f,p,d,g=0;g<n;++g)isNaN(f=+this._x.call(null,h=t[g]))||isNaN(p=+this._y.call(null,h))||isNaN(d=+this._z.call(null,h))||(e[g]=f,r[g]=p,i[g]=d,f<o&&(o=f),f>s&&(s=f),p<a&&(a=p),p>l&&(l=p),d<u&&(u=d),d>c&&(c=d));if(o>s||a>l||u>c)return this;this.cover(o,a,u).cover(s,l,c);for(let o=0;o<n;++o)Bo(this,e[o],r[o],i[o],t[o]);return this},Ko.cover=function(t,n,e){if(isNaN(t=+t)||isNaN(n=+n)||isNaN(e=+e))return this;var r=this._x0,i=this._y0,o=this._z0,a=this._x1,u=this._y1,s=this._z1;if(isNaN(r))a=(r=Math.floor(t))+1,u=(i=Math.floor(n))+1,s=(o=Math.floor(e))+1;else{for(var l,c,h=a-r||1,f=this._root;r>t||t>=a||i>n||n>=u||o>e||e>=s;)switch(c=(e<o)<<2|(n<i)<<1|t<r,(l=new Array(8))[c]=f,f=l,h*=2,c){case 0:a=r+h,u=i+h,s=o+h;break;case 1:r=a-h,u=i+h,s=o+h;break;case 2:a=r+h,i=u-h,s=o+h;break;case 3:r=a-h,i=u-h,s=o+h;break;case 4:a=r+h,u=i+h,o=s-h;break;case 5:r=a-h,u=i+h,o=s-h;break;case 6:a=r+h,i=u-h,o=s-h;break;case 7:r=a-h,i=u-h,o=s-h}this._root&&this._root.length&&(this._root=f)}return this._x0=r,this._y0=i,this._z0=o,this._x1=a,this._y1=u,this._z1=s,this},Ko.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},Ko.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1],+t[0][2]).cover(+t[1][0],+t[1][1],+t[1][2]):isNaN(this._x0)?void 0:[[this._x0,this._y0,this._z0],[this._x1,this._y1,this._z1]]},Ko.find=function(t,n,e,r){var i,o,a,u,s,l,c,h,f,p=this._x0,d=this._y0,g=this._z0,y=this._x1,_=this._y1,v=this._z1,m=[],x=this._root;for(x&&m.push(new Ho(x,p,d,g,y,_,v)),null==r?r=1/0:(p=t-r,d=n-r,g=e-r,y=t+r,_=n+r,v=e+r,r*=r);h=m.pop();)if(!(!(x=h.node)||(o=h.x0)>y||(a=h.y0)>_||(u=h.z0)>v||(s=h.x1)<p||(l=h.y1)<d||(c=h.z1)<g))if(x.length){var b=(o+s)/2,w=(a+l)/2,k=(u+c)/2;m.push(new Ho(x[7],b,w,k,s,l,c),new Ho(x[6],o,w,k,b,l,c),new Ho(x[5],b,a,k,s,w,c),new Ho(x[4],o,a,k,b,w,c),new Ho(x[3],b,w,u,s,l,k),new Ho(x[2],o,w,u,b,l,k),new Ho(x[1],b,a,u,s,w,k),new Ho(x[0],o,a,u,b,w,k)),(f=(e>=k)<<2|(n>=w)<<1|t>=b)&&(h=m[m.length-1],m[m.length-1]=m[m.length-1-f],m[m.length-1-f]=h)}else{var M=t-+this._x.call(null,x.data),A=n-+this._y.call(null,x.data),z=e-+this._z.call(null,x.data),S=M*M+A*A+z*z;if(S<r){var C=Math.sqrt(r=S);p=t-C,d=n-C,g=e-C,y=t+C,_=n+C,v=e+C,i=x.data}}return i},Ko.findAllWithinRadius=function(t,n,e,r){const i=[],o=t-r,a=n-r,u=e-r,s=t+r,l=n+r,c=e+r;return this.visit((h,f,p,d,g,y,_)=>{if(!h.length)do{const o=h.data;Vo(t,n,e,this._x(o),this._y(o),this._z(o))<=r&&i.push(o)}while(h=h.next);return f>s||p>l||d>c||g<o||y<a||_<u}),i},Ko.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t))||isNaN(u=+this._z.call(null,t)))return this;var n,e,r,i,o,a,u,s,l,c,h,f,p,d,g,y=this._root,_=this._x0,v=this._y0,m=this._z0,x=this._x1,b=this._y1,w=this._z1;if(!y)return this;if(y.length)for(;;){if((h=o>=(s=(_+x)/2))?_=s:x=s,(f=a>=(l=(v+b)/2))?v=l:b=l,(p=u>=(c=(m+w)/2))?m=c:w=c,n=y,!(y=y[d=p<<2|f<<1|h]))return this;if(!y.length)break;(n[d+1&7]||n[d+2&7]||n[d+3&7]||n[d+4&7]||n[d+5&7]||n[d+6&7]||n[d+7&7])&&(e=n,g=d)}for(;y.data!==t;)if(r=y,!(y=y.next))return this;return(i=y.next)&&delete y.next,r?(i?r.next=i:delete r.next,this):n?(i?n[d]=i:delete n[d],(y=n[0]||n[1]||n[2]||n[3]||n[4]||n[5]||n[6]||n[7])&&y===(n[7]||n[6]||n[5]||n[4]||n[3]||n[2]||n[1]||n[0])&&!y.length&&(e?e[g]=y:this._root=y),this):(this._root=i,this)},Ko.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},Ko.root=function(){return this._root},Ko.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},Ko.visit=function(t){var n,e,r,i,o,a,u,s,l=[],c=this._root;for(c&&l.push(new Ho(c,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=l.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.z0,a=n.x1,u=n.y1,s=n.z1)&&c.length){var h=(r+a)/2,f=(i+u)/2,p=(o+s)/2;(e=c[7])&&l.push(new Ho(e,h,f,p,a,u,s)),(e=c[6])&&l.push(new Ho(e,r,f,p,h,u,s)),(e=c[5])&&l.push(new Ho(e,h,i,p,a,f,s)),(e=c[4])&&l.push(new Ho(e,r,i,p,h,f,s)),(e=c[3])&&l.push(new Ho(e,h,f,o,a,u,p)),(e=c[2])&&l.push(new Ho(e,r,f,o,h,u,p)),(e=c[1])&&l.push(new Ho(e,h,i,o,a,f,p)),(e=c[0])&&l.push(new Ho(e,r,i,o,h,f,p))}return this},Ko.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new Ho(this._root,this._x0,this._y0,this._z0,this._x1,this._y1,this._z1));n=e.pop();){var i=n.node;if(i.length){var o,a=n.x0,u=n.y0,s=n.z0,l=n.x1,c=n.y1,h=n.z1,f=(a+l)/2,p=(u+c)/2,d=(s+h)/2;(o=i[0])&&e.push(new Ho(o,a,u,s,f,p,d)),(o=i[1])&&e.push(new Ho(o,f,u,s,l,p,d)),(o=i[2])&&e.push(new Ho(o,a,p,s,f,c,d)),(o=i[3])&&e.push(new Ho(o,f,p,s,l,c,d)),(o=i[4])&&e.push(new Ho(o,a,u,d,f,p,h)),(o=i[5])&&e.push(new Ho(o,f,u,d,l,p,h)),(o=i[6])&&e.push(new Ho(o,a,p,d,f,c,h)),(o=i[7])&&e.push(new Ho(o,f,p,d,l,c,h))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.z0,n.x1,n.y1,n.z1);return this},Ko.x=function(t){return arguments.length?(this._x=t,this):this._x},Ko.y=function(t){return arguments.length?(this._y=t,this):this._y},Ko.z=function(t){return arguments.length?(this._z=t,this):this._z};const ia=4294967296;function oa(t){return t.x}function aa(t){return t.y}function ua(t){return t.z}var sa=Math.PI*(3-Math.sqrt(5)),la=20*Math.PI/(9+Math.sqrt(221));function ca(t,n){n=n||2;var e,r=Math.min(3,Math.max(1,Math.round(n))),i=1,o=.001,a=1-Math.pow(o,1/300),u=0,s=.6,l=new Map,c=Jn(p),h=zt("tick","end"),f=function(){let t=1;return()=>(t=(1664525*t+1013904223)%ia)/ia}();function p(){d(),h.call("tick",e),i<o&&(c.stop(),h.call("end",e))}function d(n){var o,c,h=t.length;void 0===n&&(n=1);for(var f=0;f<n;++f)for(i+=(u-i)*a,l.forEach(function(t){t(i)}),o=0;o<h;++o)null==(c=t[o]).fx?c.x+=c.vx*=s:(c.x=c.fx,c.vx=0),r>1&&(null==c.fy?c.y+=c.vy*=s:(c.y=c.fy,c.vy=0)),r>2&&(null==c.fz?c.z+=c.vz*=s:(c.z=c.fz,c.vz=0));return e}function g(){for(var n,e=0,i=t.length;e<i;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),null!=n.fz&&(n.z=n.fz),isNaN(n.x)||r>1&&isNaN(n.y)||r>2&&isNaN(n.z)){var o=10*(r>2?Math.cbrt(.5+e):r>1?Math.sqrt(.5+e):e),a=e*sa,u=e*la;1===r?n.x=o:2===r?(n.x=o*Math.cos(a),n.y=o*Math.sin(a)):(n.x=o*Math.sin(a)*Math.cos(u),n.y=o*Math.cos(a),n.z=o*Math.sin(a)*Math.sin(u))}(isNaN(n.vx)||r>1&&isNaN(n.vy)||r>2&&isNaN(n.vz))&&(n.vx=0,r>1&&(n.vy=0),r>2&&(n.vz=0))}}function y(n){return n.initialize&&n.initialize(t,f,r),n}return null==t&&(t=[]),g(),e={tick:d,restart:function(){return c.restart(p),e},stop:function(){return c.stop(),e},numDimensions:function(t){return arguments.length?(r=Math.min(3,Math.max(1,Math.round(t))),l.forEach(y),e):r},nodes:function(n){return arguments.length?(t=n,g(),l.forEach(y),e):t},alpha:function(t){return arguments.length?(i=+t,e):i},alphaMin:function(t){return arguments.length?(o=+t,e):o},alphaDecay:function(t){return arguments.length?(a=+t,e):+a},alphaTarget:function(t){return arguments.length?(u=+t,e):u},velocityDecay:function(t){return arguments.length?(s=1-t,e):1-s},randomSource:function(t){return arguments.length?(f=t,l.forEach(y),e):f},force:function(t,n){return arguments.length>1?(null==n?l.delete(t):l.set(t,y(n)),e):l.get(t)},find:function(){var n,e,i,o,a,u,s=Array.prototype.slice.call(arguments),l=s.shift()||0,c=(r>1?s.shift():null)||0,h=(r>2?s.shift():null)||0,f=s.shift()||1/0,p=0,d=t.length;for(f*=f,p=0;p<d;++p)(o=(n=l-(a=t[p]).x)*n+(e=c-(a.y||0))*e+(i=h-(a.z||0))*i)<f&&(u=a,f=o);return u},on:function(t,n){return arguments.length>1?(h.on(t,n),e):h.on(t)}}}function ha(){var t,n,e,r,i,o,a=Jo(-30),u=1,s=1/0,l=.81;function c(r){var o,a=t.length,u=(1===n?Oo(t,oa):2===n?Fo(t,oa,aa):3===n?Wo(t,oa,aa,ua):null).visitAfter(f);for(i=r,o=0;o<a;++o)e=t[o],u.visit(p)}function h(){if(t){var n,e,r=t.length;for(o=new Array(r),n=0;n<r;++n)e=t[n],o[e.index]=+a(e,n,t)}}function f(t){var e,r,i,a,u,s,l=0,c=0,h=t.length;if(h){for(i=a=u=s=0;s<h;++s)(e=t[s])&&(r=Math.abs(e.value))&&(l+=e.value,c+=r,i+=r*(e.x||0),a+=r*(e.y||0),u+=r*(e.z||0));l*=Math.sqrt(4/h),t.x=i/c,n>1&&(t.y=a/c),n>2&&(t.z=u/c)}else{(e=t).x=e.data.x,n>1&&(e.y=e.data.y),n>2&&(e.z=e.data.z);do{l+=o[e.data.index]}while(e=e.next)}t.value=l}function p(t,a,c,h,f){if(!t.value)return!0;var p=[c,h,f][n-1],d=t.x-e.x,g=n>1?t.y-e.y:0,y=n>2?t.z-e.z:0,_=p-a,v=d*d+g*g+y*y;if(_*_/l<v)return v<s&&(0===d&&(v+=(d=ta(r))*d),n>1&&0===g&&(v+=(g=ta(r))*g),n>2&&0===y&&(v+=(y=ta(r))*y),v<u&&(v=Math.sqrt(u*v)),e.vx+=d*t.value*i/v,n>1&&(e.vy+=g*t.value*i/v),n>2&&(e.vz+=y*t.value*i/v)),!0;if(!(t.length||v>=s)){(t.data!==e||t.next)&&(0===d&&(v+=(d=ta(r))*d),n>1&&0===g&&(v+=(g=ta(r))*g),n>2&&0===y&&(v+=(y=ta(r))*y),v<u&&(v=Math.sqrt(u*v)));do{t.data!==e&&(_=o[t.data.index]*i/v,e.vx+=d*_,n>1&&(e.vy+=g*_),n>2&&(e.vz+=y*_))}while(t=t.next)}}return c.initialize=function(e,...i){t=e,r=i.find(t=>"function"==typeof t)||Math.random,n=i.find(t=>[1,2,3].includes(t))||2,h()},c.strength=function(t){return arguments.length?(a="function"==typeof t?t:Jo(+t),h(),c):a},c.distanceMin=function(t){return arguments.length?(u=t*t,c):Math.sqrt(u)},c.distanceMax=function(t){return arguments.length?(s=t*t,c):Math.sqrt(s)},c.theta=function(t){return arguments.length?(l=t*t,c):Math.sqrt(l)},c}const{abs:fa,cos:pa,sin:da,acos:ga,atan2:ya,sqrt:_a,pow:va}=Math;function ma(t){return t<0?-va(-t,1/3):va(t,1/3)}const xa=Math.PI,ba=2*xa,wa=xa/2,ka=Number.MAX_SAFE_INTEGER||9007199254740991,Ma=Number.MIN_SAFE_INTEGER||-9007199254740991,Aa={x:0,y:0,z:0},za={Tvalues:[-.06405689286260563,.06405689286260563,-.1911188674736163,.1911188674736163,-.3150426796961634,.3150426796961634,-.4337935076260451,.4337935076260451,-.5454214713888396,.5454214713888396,-.6480936519369755,.6480936519369755,-.7401241915785544,.7401241915785544,-.820001985973903,.820001985973903,-.8864155270044011,.8864155270044011,-.9382745520027328,.9382745520027328,-.9747285559713095,.9747285559713095,-.9951872199970213,.9951872199970213],Cvalues:[.12793819534675216,.12793819534675216,.1258374563468283,.1258374563468283,.12167047292780339,.12167047292780339,.1155056680537256,.1155056680537256,.10744427011596563,.10744427011596563,.09761865210411388,.09761865210411388,.08619016153195327,.08619016153195327,.0733464814110803,.0733464814110803,.05929858491543678,.05929858491543678,.04427743881741981,.04427743881741981,.028531388628933663,.028531388628933663,.0123412297999872,.0123412297999872],arcfn:function(t,n){const e=n(t);let r=e.x*e.x+e.y*e.y;return void 0!==e.z&&(r+=e.z*e.z),_a(r)},compute:function(t,n,e){if(0===t)return n[0].t=0,n[0];const r=n.length-1;if(1===t)return n[r].t=1,n[r];const i=1-t;let o=n;if(0===r)return n[0].t=t,n[0];if(1===r){const n={x:i*o[0].x+t*o[1].x,y:i*o[0].y+t*o[1].y,t:t};return e&&(n.z=i*o[0].z+t*o[1].z),n}if(r<4){let n,a,u,s=i*i,l=t*t,c=0;2===r?(o=[o[0],o[1],o[2],Aa],n=s,a=i*t*2,u=l):3===r&&(n=s*i,a=s*t*3,u=i*l*3,c=t*l);const h={x:n*o[0].x+a*o[1].x+u*o[2].x+c*o[3].x,y:n*o[0].y+a*o[1].y+u*o[2].y+c*o[3].y,t:t};return e&&(h.z=n*o[0].z+a*o[1].z+u*o[2].z+c*o[3].z),h}const a=JSON.parse(JSON.stringify(n));for(;a.length>1;){for(let n=0;n<a.length-1;n++)a[n]={x:a[n].x+(a[n+1].x-a[n].x)*t,y:a[n].y+(a[n+1].y-a[n].y)*t},void 0!==a[n].z&&(a[n].z=a[n].z+(a[n+1].z-a[n].z)*t);a.splice(a.length-1,1)}return a[0].t=t,a[0]},computeWithRatios:function(t,n,e,r){const i=1-t,o=e,a=n;let u,s=o[0],l=o[1],c=o[2],h=o[3];return s*=i,l*=t,2===a.length?(u=s+l,{x:(s*a[0].x+l*a[1].x)/u,y:(s*a[0].y+l*a[1].y)/u,z:!!r&&(s*a[0].z+l*a[1].z)/u,t:t}):(s*=i,l*=2*i,c*=t*t,3===a.length?(u=s+l+c,{x:(s*a[0].x+l*a[1].x+c*a[2].x)/u,y:(s*a[0].y+l*a[1].y+c*a[2].y)/u,z:!!r&&(s*a[0].z+l*a[1].z+c*a[2].z)/u,t:t}):(s*=i,l*=1.5*i,c*=3*i,h*=t*t*t,4===a.length?(u=s+l+c+h,{x:(s*a[0].x+l*a[1].x+c*a[2].x+h*a[3].x)/u,y:(s*a[0].y+l*a[1].y+c*a[2].y+h*a[3].y)/u,z:!!r&&(s*a[0].z+l*a[1].z+c*a[2].z+h*a[3].z)/u,t:t}):void 0))},derive:function(t,n){const e=[];for(let r=t,i=r.length,o=i-1;i>1;i--,o--){const t=[];for(let e,i=0;i<o;i++)e={x:o*(r[i+1].x-r[i].x),y:o*(r[i+1].y-r[i].y)},n&&(e.z=o*(r[i+1].z-r[i].z)),t.push(e);e.push(t),r=t}return e},between:function(t,n,e){return n<=t&&t<=e||za.approximately(t,n)||za.approximately(t,e)},approximately:function(t,n,e){return fa(t-n)<=(e||1e-6)},length:function(t){const n=za.Tvalues.length;let e=0;for(let r,i=0;i<n;i++)r=.5*za.Tvalues[i]+.5,e+=za.Cvalues[i]*za.arcfn(r,t);return.5*e},map:function(t,n,e,r,i){return r+(i-r)*((t-n)/(e-n))},lerp:function(t,n,e){const r={x:n.x+t*(e.x-n.x),y:n.y+t*(e.y-n.y)};return void 0!==n.z&&void 0!==e.z&&(r.z=n.z+t*(e.z-n.z)),r},pointToString:function(t){let n=t.x+"/"+t.y;return void 0!==t.z&&(n+="/"+t.z),n},pointsToString:function(t){return"["+t.map(za.pointToString).join(", ")+"]"},copy:function(t){return JSON.parse(JSON.stringify(t))},angle:function(t,n,e){const r=n.x-t.x,i=n.y-t.y,o=e.x-t.x,a=e.y-t.y;return ya(r*a-i*o,r*o+i*a)},round:function(t,n){const e=""+t,r=e.indexOf(".");return parseFloat(e.substring(0,r+1+n))},dist:function(t,n){const e=t.x-n.x,r=t.y-n.y;return _a(e*e+r*r)},closest:function(t,n){let e,r,i=va(2,63);return t.forEach(function(t,o){r=za.dist(n,t),r<i&&(i=r,e=o)}),{mdist:i,mpos:e}},abcratio:function(t,n){if(2!==n&&3!==n)return!1;if(void 0===t)t=.5;else if(0===t||1===t)return t;const e=va(t,n)+va(1-t,n);return fa((e-1)/e)},projectionratio:function(t,n){if(2!==n&&3!==n)return!1;if(void 0===t)t=.5;else if(0===t||1===t)return t;const e=va(1-t,n);return e/(va(t,n)+e)},lli8:function(t,n,e,r,i,o,a,u){const s=(t-e)*(o-u)-(n-r)*(i-a);return 0!=s&&{x:((t*r-n*e)*(i-a)-(t-e)*(i*u-o*a))/s,y:((t*r-n*e)*(o-u)-(n-r)*(i*u-o*a))/s}},lli4:function(t,n,e,r){const i=t.x,o=t.y,a=n.x,u=n.y,s=e.x,l=e.y,c=r.x,h=r.y;return za.lli8(i,o,a,u,s,l,c,h)},lli:function(t,n){return za.lli4(t,t.c,n,n.c)},makeline:function(t,n){return new Da(t.x,t.y,(t.x+n.x)/2,(t.y+n.y)/2,n.x,n.y)},findbbox:function(t){let n=ka,e=ka,r=Ma,i=Ma;return t.forEach(function(t){const o=t.bbox();n>o.x.min&&(n=o.x.min),e>o.y.min&&(e=o.y.min),r<o.x.max&&(r=o.x.max),i<o.y.max&&(i=o.y.max)}),{x:{min:n,mid:(n+r)/2,max:r,size:r-n},y:{min:e,mid:(e+i)/2,max:i,size:i-e}}},shapeintersections:function(t,n,e,r,i){if(!za.bboxoverlap(n,r))return[];const o=[],a=[t.startcap,t.forward,t.back,t.endcap],u=[e.startcap,e.forward,e.back,e.endcap];return a.forEach(function(n){n.virtual||u.forEach(function(r){if(r.virtual)return;const a=n.intersects(r,i);a.length>0&&(a.c1=n,a.c2=r,a.s1=t,a.s2=e,o.push(a))})}),o},makeshape:function(t,n,e){const r=n.points.length,i=t.points.length,o=za.makeline(n.points[r-1],t.points[0]),a=za.makeline(t.points[i-1],n.points[0]),u={startcap:o,forward:t,back:n,endcap:a,bbox:za.findbbox([o,t,n,a]),intersections:function(t){return za.shapeintersections(u,u.bbox,t,t.bbox,e)}};return u},getminmax:function(t,n,e){if(!e)return{min:0,max:0};let r,i,o=ka,a=Ma;-1===e.indexOf(0)&&(e=[0].concat(e)),-1===e.indexOf(1)&&e.push(1);for(let u=0,s=e.length;u<s;u++)r=e[u],i=t.get(r),i[n]<o&&(o=i[n]),i[n]>a&&(a=i[n]);return{min:o,mid:(o+a)/2,max:a,size:a-o}},align:function(t,n){const e=n.p1.x,r=n.p1.y,i=-ya(n.p2.y-r,n.p2.x-e);return t.map(function(t){return{x:(t.x-e)*pa(i)-(t.y-r)*da(i),y:(t.x-e)*da(i)+(t.y-r)*pa(i)}})},roots:function(t,n){n=n||{p1:{x:0,y:0},p2:{x:1,y:0}};const e=t.length-1,r=za.align(t,n),i=function(t){return 0<=t&&t<=1};if(2===e){const t=r[0].y,n=r[1].y,e=r[2].y,o=t-2*n+e;if(0!==o){const r=-_a(n*n-t*e),a=-t+n;return[-(r+a)/o,-(-r+a)/o].filter(i)}return n!==e&&0===o?[(2*n-e)/(2*n-2*e)].filter(i):[]}const o=r[0].y,a=r[1].y,u=r[2].y;let s=3*a-o-3*u+r[3].y,l=3*o-6*a+3*u,c=-3*o+3*a,h=o;if(za.approximately(s,0)){if(za.approximately(l,0))return za.approximately(c,0)?[]:[-h/c].filter(i);const t=_a(c*c-4*l*h),n=2*l;return[(t-c)/n,(-c-t)/n].filter(i)}l/=s,c/=s,h/=s;const f=(3*c-l*l)/3,p=f/3,d=(2*l*l*l-9*l*c+27*h)/27,g=d/2,y=g*g+p*p*p;let _,v,m,x,b;if(y<0){const t=-f/3,n=_a(t*t*t),e=-d/(2*n),r=ga(e<-1?-1:e>1?1:e),o=2*ma(n);return m=o*pa(r/3)-l/3,x=o*pa((r+ba)/3)-l/3,b=o*pa((r+2*ba)/3)-l/3,[m,x,b].filter(i)}if(0===y)return _=g<0?ma(-g):-ma(g),m=2*_-l/3,x=-_-l/3,[m,x].filter(i);{const t=_a(y);return _=ma(-g+t),v=ma(g+t),[_-v-l/3].filter(i)}},droots:function(t){if(3===t.length){const n=t[0],e=t[1],r=t[2],i=n-2*e+r;if(0!==i){const t=-_a(e*e-n*r),o=-n+e;return[-(t+o)/i,-(-t+o)/i]}return e!==r&&0===i?[(2*e-r)/(2*(e-r))]:[]}if(2===t.length){const n=t[0],e=t[1];return n!==e?[n/(n-e)]:[]}return[]},curvature:function(t,n,e,r,i){let o,a,u,s,l=0,c=0;const h=za.compute(t,n),f=za.compute(t,e),p=h.x*h.x+h.y*h.y;if(r?(o=_a(va(h.y*f.z-f.y*h.z,2)+va(h.z*f.x-f.z*h.x,2)+va(h.x*f.y-f.x*h.y,2)),a=va(p+h.z*h.z,1.5)):(o=h.x*f.y-h.y*f.x,a=va(p,1.5)),0===o||0===a)return{k:0,r:0};if(l=o/a,c=a/o,!i){const i=za.curvature(t-.001,n,e,r,!0).k,o=za.curvature(t+.001,n,e,r,!0).k;s=(o-l+(l-i))/2,u=(fa(o-l)+fa(l-i))/2}return{k:l,r:c,dk:s,adk:u}},inflections:function(t){if(t.length<4)return[];const n=za.align(t,{p1:t[0],p2:t.slice(-1)[0]}),e=n[2].x*n[1].y,r=n[3].x*n[1].y,i=n[1].x*n[2].y,o=18*(-3*e+2*r+3*i-n[3].x*n[2].y),a=18*(3*e-r-3*i),u=18*(i-e);if(za.approximately(o,0)){if(!za.approximately(a,0)){let t=-u/a;if(0<=t&&t<=1)return[t]}return[]}const s=2*o;if(za.approximately(s,0))return[];const l=a*a-4*o*u;if(l<0)return[];const c=Math.sqrt(l);return[(c-a)/s,-(a+c)/s].filter(function(t){return 0<=t&&t<=1})},bboxoverlap:function(t,n){const e=["x","y"],r=e.length;for(let i,o,a,u,s=0;s<r;s++)if(i=e[s],o=t[i].mid,a=n[i].mid,u=(t[i].size+n[i].size)/2,fa(o-a)>=u)return!1;return!0},expandbox:function(t,n){n.x.min<t.x.min&&(t.x.min=n.x.min),n.y.min<t.y.min&&(t.y.min=n.y.min),n.z&&n.z.min<t.z.min&&(t.z.min=n.z.min),n.x.max>t.x.max&&(t.x.max=n.x.max),n.y.max>t.y.max&&(t.y.max=n.y.max),n.z&&n.z.max>t.z.max&&(t.z.max=n.z.max),t.x.mid=(t.x.min+t.x.max)/2,t.y.mid=(t.y.min+t.y.max)/2,t.z&&(t.z.mid=(t.z.min+t.z.max)/2),t.x.size=t.x.max-t.x.min,t.y.size=t.y.max-t.y.min,t.z&&(t.z.size=t.z.max-t.z.min)},pairiteration:function(t,n,e){const r=t.bbox(),i=n.bbox(),o=1e5,a=e||.5;if(r.x.size+r.y.size<a&&i.x.size+i.y.size<a)return[(o*(t._t1+t._t2)/2|0)/o+"/"+(o*(n._t1+n._t2)/2|0)/o];let u=t.split(.5),s=n.split(.5),l=[{left:u.left,right:s.left},{left:u.left,right:s.right},{left:u.right,right:s.right},{left:u.right,right:s.left}];l=l.filter(function(t){return za.bboxoverlap(t.left.bbox(),t.right.bbox())});let c=[];return 0===l.length||(l.forEach(function(t){c=c.concat(za.pairiteration(t.left,t.right,a))}),c=c.filter(function(t,n){return c.indexOf(t)===n})),c},getccenter:function(t,n,e){const r=n.x-t.x,i=n.y-t.y,o=e.x-n.x,a=e.y-n.y,u=r*pa(wa)-i*da(wa),s=r*da(wa)+i*pa(wa),l=o*pa(wa)-a*da(wa),c=o*da(wa)+a*pa(wa),h=(t.x+n.x)/2,f=(t.y+n.y)/2,p=(n.x+e.x)/2,d=(n.y+e.y)/2,g=h+u,y=f+s,_=p+l,v=d+c,m=za.lli8(h,f,g,y,p,d,_,v),x=za.dist(m,t);let b,w=ya(t.y-m.y,t.x-m.x),k=ya(n.y-m.y,n.x-m.x),M=ya(e.y-m.y,e.x-m.x);return w<M?((w>k||k>M)&&(w+=ba),w>M&&(b=M,M=w,w=b)):M<k&&k<w?(b=M,M=w,w=b):M+=ba,m.s=w,m.e=M,m.r=x,m},numberSort:function(t,n){return t-n}};class Sa{constructor(t){this.curves=[],this._3d=!1,t&&(this.curves=t,this._3d=this.curves[0]._3d)}valueOf(){return this.toString()}toString(){return"["+this.curves.map(function(t){return za.pointsToString(t.points)}).join(", ")+"]"}addCurve(t){this.curves.push(t),this._3d=this._3d||t._3d}length(){return this.curves.map(function(t){return t.length()}).reduce(function(t,n){return t+n})}curve(t){return this.curves[t]}bbox(){const t=this.curves;for(var n=t[0].bbox(),e=1;e<t.length;e++)za.expandbox(n,t[e].bbox());return n}offset(t){const n=[];return this.curves.forEach(function(e){n.push(...e.offset(t))}),new Sa(n)}}const{abs:Ca,min:Ea,max:Pa,cos:Oa,sin:Na,acos:ja,sqrt:Ta}=Math,Ra=Math.PI;class Da{constructor(t){let n=t&&t.forEach?t:Array.from(arguments).slice(),e=!1;if("object"==typeof n[0]){e=n.length;const t=[];n.forEach(function(n){["x","y","z"].forEach(function(e){void 0!==n[e]&&t.push(n[e])})}),n=t}let r=!1;const i=n.length;if(e){if(e>4){if(1!==arguments.length)throw new Error("Only new Bezier(point[]) is accepted for 4th and higher order curves");r=!0}}else if(6!==i&&8!==i&&9!==i&&12!==i&&1!==arguments.length)throw new Error("Only new Bezier(point[]) is accepted for 4th and higher order curves");const o=this._3d=!r&&(9===i||12===i)||t&&t[0]&&void 0!==t[0].z,a=this.points=[];for(let t=0,e=o?3:2;t<i;t+=e){var u={x:n[t],y:n[t+1]};o&&(u.z=n[t+2]),a.push(u)}const s=this.order=a.length-1,l=this.dims=["x","y"];o&&l.push("z"),this.dimlen=l.length;const c=za.align(a,{p1:a[0],p2:a[s]}),h=za.dist(a[0],a[s]);this._linear=c.reduce((t,n)=>t+Ca(n.y),0)<h/50,this._lut=[],this._t1=0,this._t2=1,this.update()}static quadraticFromPoints(t,n,e,r){if(void 0===r&&(r=.5),0===r)return new Da(n,n,e);if(1===r)return new Da(t,n,n);const i=Da.getABC(2,t,n,e,r);return new Da(t,i.A,e)}static cubicFromPoints(t,n,e,r,i){void 0===r&&(r=.5);const o=Da.getABC(3,t,n,e,r);void 0===i&&(i=za.dist(n,o.C));const a=i*(1-r)/r,u=za.dist(t,e),s=(e.x-t.x)/u,l=(e.y-t.y)/u,c=i*s,h=i*l,f=a*s,p=a*l,d=n.x-c,g=n.y-h,y=n.x+f,_=n.y+p,v=o.A,m=v.x+(d-v.x)/(1-r),x=v.y+(g-v.y)/(1-r),b=v.x+(y-v.x)/r,w=v.y+(_-v.y)/r,k={x:t.x+(m-t.x)/r,y:t.y+(x-t.y)/r},M={x:e.x+(b-e.x)/(1-r),y:e.y+(w-e.y)/(1-r)};return new Da(t,k,M,e)}static getUtils(){return za}getUtils(){return Da.getUtils()}static get PolyBezier(){return Sa}valueOf(){return this.toString()}toString(){return za.pointsToString(this.points)}toSVG(){if(this._3d)return!1;const t=this.points,n=["M",t[0].x,t[0].y,2===this.order?"Q":"C"];for(let e=1,r=t.length;e<r;e++)n.push(t[e].x),n.push(t[e].y);return n.join(" ")}setRatios(t){if(t.length!==this.points.length)throw new Error("incorrect number of ratio values");this.ratios=t,this._lut=[]}verify(){const t=this.coordDigest();t!==this._print&&(this._print=t,this.update())}coordDigest(){return this.points.map(function(t,n){return""+n+t.x+t.y+(t.z?t.z:0)}).join("")}update(){this._lut=[],this.dpoints=za.derive(this.points,this._3d),this.computedirection()}computedirection(){const t=this.points,n=za.angle(t[0],t[this.order],t[1]);this.clockwise=n>0}length(){return za.length(this.derivative.bind(this))}static getABC(t=2,n,e,r,i=.5){const o=za.projectionratio(i,t),a=1-o,u={x:o*n.x+a*r.x,y:o*n.y+a*r.y},s=za.abcratio(i,t);return{A:{x:e.x+(e.x-u.x)/s,y:e.y+(e.y-u.y)/s},B:e,C:u,S:n,E:r}}getABC(t,n){n=n||this.get(t);let e=this.points[0],r=this.points[this.order];return Da.getABC(this.order,e,n,r,t)}getLUT(t){if(this.verify(),t=t||100,this._lut.length===t+1)return this._lut;this._lut=[],t++,this._lut=[];for(let n,e,r=0;r<t;r++)e=r/(t-1),n=this.compute(e),n.t=e,this._lut.push(n);return this._lut}on(n,e){e=e||5;const r=this.getLUT(),i=[];for(let t,o=0,a=0;o<r.length;o++)t=r[o],za.dist(t,n)<e&&(i.push(t),a+=o/r.length);return!!i.length&&(t/=i.length)}project(t){const n=this.getLUT(),e=n.length-1,r=za.closest(n,t),i=r.mpos,o=(i-1)/e,a=(i+1)/e,u=.1/e;let s,l=r.mdist,c=o,h=c;l+=1;for(let n;c<a+u;c+=u)s=this.compute(c),n=za.dist(t,s),n<l&&(l=n,h=c);return h=h<0?0:h>1?1:h,s=this.compute(h),s.t=h,s.d=l,s}get(t){return this.compute(t)}point(t){return this.points[t]}compute(t){return this.ratios?za.computeWithRatios(t,this.points,this.ratios,this._3d):za.compute(t,this.points,this._3d,this.ratios)}raise(){const t=this.points,n=[t[0]],e=t.length;for(let r,i,o=1;o<e;o++)r=t[o],i=t[o-1],n[o]={x:(e-o)/e*r.x+o/e*i.x,y:(e-o)/e*r.y+o/e*i.y};return n[e]=t[e-1],new Da(n)}derivative(t){return za.compute(t,this.dpoints[0],this._3d)}dderivative(t){return za.compute(t,this.dpoints[1],this._3d)}align(){let t=this.points;return new Da(za.align(t,{p1:t[0],p2:t[t.length-1]}))}curvature(t){return za.curvature(t,this.dpoints[0],this.dpoints[1],this._3d)}inflections(){return za.inflections(this.points)}normal(t){return this._3d?this.__normal3(t):this.__normal2(t)}__normal2(t){const n=this.derivative(t),e=Ta(n.x*n.x+n.y*n.y);return{t:t,x:-n.y/e,y:n.x/e}}__normal3(t){const n=this.derivative(t),e=this.derivative(t+.01),r=Ta(n.x*n.x+n.y*n.y+n.z*n.z),i=Ta(e.x*e.x+e.y*e.y+e.z*e.z);n.x/=r,n.y/=r,n.z/=r,e.x/=i,e.y/=i,e.z/=i;const o={x:e.y*n.z-e.z*n.y,y:e.z*n.x-e.x*n.z,z:e.x*n.y-e.y*n.x},a=Ta(o.x*o.x+o.y*o.y+o.z*o.z);o.x/=a,o.y/=a,o.z/=a;const u=[o.x*o.x,o.x*o.y-o.z,o.x*o.z+o.y,o.x*o.y+o.z,o.y*o.y,o.y*o.z-o.x,o.x*o.z-o.y,o.y*o.z+o.x,o.z*o.z];return{t:t,x:u[0]*n.x+u[1]*n.y+u[2]*n.z,y:u[3]*n.x+u[4]*n.y+u[5]*n.z,z:u[6]*n.x+u[7]*n.y+u[8]*n.z}}hull(t){let n=this.points,e=[],r=[],i=0;for(r[i++]=n[0],r[i++]=n[1],r[i++]=n[2],3===this.order&&(r[i++]=n[3]);n.length>1;){e=[];for(let o,a=0,u=n.length-1;a<u;a++)o=za.lerp(t,n[a],n[a+1]),r[i++]=o,e.push(o);n=e}return r}split(t,n){if(0===t&&n)return this.split(n).left;if(1===n)return this.split(t).right;const e=this.hull(t),r={left:2===this.order?new Da([e[0],e[3],e[5]]):new Da([e[0],e[4],e[7],e[9]]),right:2===this.order?new Da([e[5],e[4],e[2]]):new Da([e[9],e[8],e[6],e[3]]),span:e};return r.left._t1=za.map(0,0,1,this._t1,this._t2),r.left._t2=za.map(t,0,1,this._t1,this._t2),r.right._t1=za.map(t,0,1,this._t1,this._t2),r.right._t2=za.map(1,0,1,this._t1,this._t2),n?(n=za.map(n,t,1,0,1),r.right.split(n).left):r}extrema(){const t={};let n=[];return this.dims.forEach(function(e){let r=function(t){return t[e]},i=this.dpoints[0].map(r);t[e]=za.droots(i),3===this.order&&(i=this.dpoints[1].map(r),t[e]=t[e].concat(za.droots(i))),t[e]=t[e].filter(function(t){return t>=0&&t<=1}),n=n.concat(t[e].sort(za.numberSort))}.bind(this)),t.values=n.sort(za.numberSort).filter(function(t,e){return n.indexOf(t)===e}),t}bbox(){const t=this.extrema(),n={};return this.dims.forEach(function(e){n[e]=za.getminmax(this,e,t[e])}.bind(this)),n}overlaps(t){const n=this.bbox(),e=t.bbox();return za.bboxoverlap(n,e)}offset(t,n){if(void 0!==n){const e=this.get(t),r=this.normal(t),i={c:e,n:r,x:e.x+r.x*n,y:e.y+r.y*n};return this._3d&&(i.z=e.z+r.z*n),i}if(this._linear){const n=this.normal(0),e=this.points.map(function(e){const r={x:e.x+t*n.x,y:e.y+t*n.y};return e.z&&n.z&&(r.z=e.z+t*n.z),r});return[new Da(e)]}return this.reduce().map(function(n){return n._linear?n.offset(t)[0]:n.scale(t)})}simple(){if(3===this.order){const t=za.angle(this.points[0],this.points[3],this.points[1]),n=za.angle(this.points[0],this.points[3],this.points[2]);if(t>0&&n<0||t<0&&n>0)return!1}const t=this.normal(0),n=this.normal(1);let e=t.x*n.x+t.y*n.y;return this._3d&&(e+=t.z*n.z),Ca(ja(e))<Ra/3}reduce(){let t,n,e=0,r=0,i=.01,o=[],a=[],u=this.extrema().values;for(-1===u.indexOf(0)&&(u=[0].concat(u)),-1===u.indexOf(1)&&u.push(1),e=u[0],t=1;t<u.length;t++)r=u[t],n=this.split(e,r),n._t1=e,n._t2=r,o.push(n),e=r;return o.forEach(function(t){for(e=0,r=0;r<=1;)for(r=e+i;r<=1.01;r+=i)if(n=t.split(e,r),!n.simple()){if(r-=i,Ca(e-r)<i)return[];n=t.split(e,r),n._t1=za.map(e,0,1,t._t1,t._t2),n._t2=za.map(r,0,1,t._t1,t._t2),a.push(n),e=r;break}e<1&&(n=t.split(e,1),n._t1=za.map(e,0,1,t._t1,t._t2),n._t2=t._t2,a.push(n))}),a}translate(t,n,e){e="number"==typeof e?e:n;const r=this.order;let i=this.points.map((t,i)=>(1-i/r)*n+i/r*e);return new Da(this.points.map((n,e)=>({x:n.x+t.x*i[e],y:n.y+t.y*i[e]})))}scale(t){const n=this.order;let e=!1;if("function"==typeof t&&(e=t),e&&2===n)return this.raise().scale(e);const r=this.clockwise,i=this.points;if(this._linear)return this.translate(this.normal(0),e?e(0):t,e?e(1):t);const o=e?e(0):t,a=e?e(1):t,u=[this.offset(0,10),this.offset(1,10)],s=[],l=za.lli4(u[0],u[0].c,u[1],u[1].c);if(!l)throw new Error("cannot scale this curve. Try reducing it first.");return[0,1].forEach(function(t){const e=s[t*n]=za.copy(i[t*n]);e.x+=(t?a:o)*u[t].n.x,e.y+=(t?a:o)*u[t].n.y}),e?([0,1].forEach(function(o){if(2!==n||!o){var a=i[o+1],u={x:a.x-l.x,y:a.y-l.y},c=e?e((o+1)/n):t;e&&!r&&(c=-c);var h=Ta(u.x*u.x+u.y*u.y);u.x/=h,u.y/=h,s[o+1]={x:a.x+c*u.x,y:a.y+c*u.y}}}),new Da(s)):([0,1].forEach(t=>{if(2===n&&t)return;const e=s[t*n],r=this.derivative(t),o={x:e.x+r.x,y:e.y+r.y};s[t+1]=za.lli4(e,o,l,i[t+1])}),new Da(s))}outline(t,n,e,r){if(n=void 0===n?t:n,this._linear){const i=this.normal(0),o=this.points[0],a=this.points[this.points.length-1];let u,s,l;void 0===e&&(e=t,r=n),u={x:o.x+i.x*t,y:o.y+i.y*t},l={x:a.x+i.x*e,y:a.y+i.y*e},s={x:(u.x+l.x)/2,y:(u.y+l.y)/2};const c=[u,s,l];u={x:o.x-i.x*n,y:o.y-i.y*n},l={x:a.x-i.x*r,y:a.y-i.y*r},s={x:(u.x+l.x)/2,y:(u.y+l.y)/2};const h=[l,s,u],f=za.makeline(h[2],c[0]),p=za.makeline(c[2],h[0]),d=[f,new Da(c),p,new Da(h)];return new Sa(d)}const i=this.reduce(),o=i.length,a=[];let u,s=[],l=0,c=this.length();const h=void 0!==e&&void 0!==r;function f(t,n,e,r,i){return function(o){const a=r/e,u=(r+i)/e,s=n-t;return za.map(o,0,1,t+a*s,t+u*s)}}i.forEach(function(i){const o=i.length();h?(a.push(i.scale(f(t,e,c,l,o))),s.push(i.scale(f(-n,-r,c,l,o)))):(a.push(i.scale(t)),s.push(i.scale(-n))),l+=o}),s=s.map(function(t){return u=t.points,u[3]?t.points=[u[3],u[2],u[1],u[0]]:t.points=[u[2],u[1],u[0]],t}).reverse();const p=a[0].points[0],d=a[o-1].points[a[o-1].points.length-1],g=s[o-1].points[s[o-1].points.length-1],y=s[0].points[0],_=za.makeline(g,p),v=za.makeline(d,y),m=[_].concat(a).concat([v]).concat(s);return new Sa(m)}outlineshapes(t,n,e){n=n||t;const r=this.outline(t,n).curves,i=[];for(let t=1,n=r.length;t<n/2;t++){const o=za.makeshape(r[t],r[n-t],e);o.startcap.virtual=t>1,o.endcap.virtual=t<n/2-1,i.push(o)}return i}intersects(t,n){return t?t.p1&&t.p2?this.lineIntersects(t):(t instanceof Da&&(t=t.reduce()),this.curveintersects(this.reduce(),t,n)):this.selfintersects(n)}lineIntersects(t){const n=Ea(t.p1.x,t.p2.x),e=Ea(t.p1.y,t.p2.y),r=Pa(t.p1.x,t.p2.x),i=Pa(t.p1.y,t.p2.y);return za.roots(this.points,t).filter(t=>{var o=this.get(t);return za.between(o.x,n,r)&&za.between(o.y,e,i)})}selfintersects(t){const n=this.reduce(),e=n.length-2,r=[];for(let i,o,a,u=0;u<e;u++)o=n.slice(u,u+1),a=n.slice(u+2),i=this.curveintersects(o,a,t),r.push(...i);return r}curveintersects(t,n,e){const r=[];t.forEach(function(t){n.forEach(function(n){t.overlaps(n)&&r.push({left:t,right:n})})});let i=[];return r.forEach(function(t){const n=za.pairiteration(t.left,t.right,e);n.length>0&&(i=i.concat(n))}),i}arcs(t){return t=t||.5,this._iterate(t,[])}_error(t,n,e,r){const i=(r-e)/4,o=this.get(e+i),a=this.get(r-i),u=za.dist(t,n),s=za.dist(t,o),l=za.dist(t,a);return Ca(s-u)+Ca(l-u)}_iterate(t,n){let e,r=0,i=1;do{e=0,i=1;let o,a,u,s,l,c=this.get(r),h=!1,f=!1,p=i,d=1;do{if(f=h,s=u,p=(r+i)/2,o=this.get(p),a=this.get(i),u=za.getccenter(c,o,a),u.interval={start:r,end:i},h=this._error(u,c,r,i)<=t,l=f&&!h,l||(d=i),h){if(i>=1){if(u.interval.end=d=1,s=u,i>1){let t={x:u.x+u.r*Oa(u.e),y:u.y+u.r*Na(u.e)};u.e+=za.angle({x:u.x,y:u.y},t,this.get(1))}break}i+=(i-r)/2}else i=p}while(!l&&e++<100);if(e>=100)break;s=s||u,n.push(s),r=d}while(i<1);return n}}function Ia(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}function Ua(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,i,o,a,u=[],s=!0,l=!1;try{if(o=(e=e.call(t)).next,0===n);else for(;!(s=(r=o.call(e)).done)&&(u.push(r.value),u.length!==n);s=!0);}catch(t){l=!0,i=t}finally{try{if(!s&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,n)||qa(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fa(t){return function(t){if(Array.isArray(t))return Ia(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||qa(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function La(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var r=e.call(t,n);if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof n?n:n+""}function qa(t,n){if(t){if("string"==typeof t)return Ia(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Ia(t,n):void 0}}var $a=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],e=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=(n instanceof Array?n.length?n:[void 0]:[n]).map(function(t){return{keyAccessor:t,isProp:!(t instanceof Function)}}),o=t.reduce(function(t,n){var r=t,o=n;return i.forEach(function(t,n){var a,u=t.keyAccessor;if(t.isProp){var s=o,l=s[u],c=function(t,n){if(null==t)return{};var e,r,i=function(t,n){if(null==t)return{};var e={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(n.includes(r))continue;e[r]=t[r]}return e}(t,n);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)e=o[r],n.includes(e)||{}.propertyIsEnumerable.call(t,e)&&(i[e]=t[e])}return i}(s,[u].map(La));a=l,o=c}else a=u(o,n);n+1<i.length?(r.hasOwnProperty(a)||(r[a]={}),r=r[a]):e?(r.hasOwnProperty(a)||(r[a]=[]),r[a].push(o)):r[a]=o}),t},{});e instanceof Function&&function t(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;r===i.length?Object.keys(n).forEach(function(t){return n[t]=e(n[t])}):Object.values(n).forEach(function(n){return t(n,r+1)})}(o);var a=o;return r&&(a=[],function t(n){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.length===i.length?a.push({keys:e,vals:n}):Object.entries(n).forEach(function(n){var r=Ua(n,2),i=r[0],o=r[1];return t(o,[].concat(Fa(e),[i]))})}(o),n instanceof Array&&0===n.length&&1===a.length&&(a[0].keys=[])),a};function Ba(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}const Ha=Symbol("implicit");var Va=function(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),Xa=function t(){var n=new We,e=[],r=[],i=Ha;function o(t){let o=n.get(t);if(void 0===o){if(i!==Ha)return i;n.set(t,o=e.push(t)-1)}return r[o%r.length]}return o.domain=function(t){if(!arguments.length)return e.slice();e=[],n=new We;for(const r of t)n.has(r)||n.set(r,e.push(r)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(i=t,o):i},o.copy=function(){return t(e,r).unknown(i)},Ba.apply(o,arguments),o}(Va);function Ga(t,n,e){n&&"string"==typeof e&&t.filter(function(t){return!t[e]}).forEach(function(t){t[e]=Xa(n(t))})}var Ya=function(t,n){return n.onNeedsRedraw&&n.onNeedsRedraw()},Wa=function(t,n){if(!n.isShadow){var e=Ir(n.linkDirectionalParticles);n.graphData.links.forEach(function(t){var n=Math.round(Math.abs(e(t)));n?t.__photons=s(Array(n)).map(function(){return{}}):delete t.__photons})}},Za=Dr({props:{graphData:{default:{nodes:[],links:[]},onChange:function(t,n){n.engineRunning=!1,Wa(0,n)}},dagMode:{onChange:function(t,n){!t&&(n.graphData.nodes||[]).forEach(function(t){return t.fx=t.fy=void 0})}},dagLevelDistance:{},dagNodeFilter:{default:function(t){return!0}},onDagError:{triggerUpdate:!1},nodeRelSize:{default:4,triggerUpdate:!1,onChange:Ya},nodeId:{default:"id"},nodeVal:{default:"val",triggerUpdate:!1,onChange:Ya},nodeColor:{default:"color",triggerUpdate:!1,onChange:Ya},nodeAutoColorBy:{},nodeCanvasObject:{triggerUpdate:!1,onChange:Ya},nodeCanvasObjectMode:{default:function(){return"replace"},triggerUpdate:!1,onChange:Ya},nodeVisibility:{default:!0,triggerUpdate:!1,onChange:Ya},linkSource:{default:"source"},linkTarget:{default:"target"},linkVisibility:{default:!0,triggerUpdate:!1,onChange:Ya},linkColor:{default:"color",triggerUpdate:!1,onChange:Ya},linkAutoColorBy:{},linkLineDash:{triggerUpdate:!1,onChange:Ya},linkWidth:{default:1,triggerUpdate:!1,onChange:Ya},linkCurvature:{default:0,triggerUpdate:!1,onChange:Ya},linkCanvasObject:{triggerUpdate:!1,onChange:Ya},linkCanvasObjectMode:{default:function(){return"replace"},triggerUpdate:!1,onChange:Ya},linkDirectionalArrowLength:{default:0,triggerUpdate:!1,onChange:Ya},linkDirectionalArrowColor:{triggerUpdate:!1,onChange:Ya},linkDirectionalArrowRelPos:{default:.5,triggerUpdate:!1,onChange:Ya},linkDirectionalParticles:{default:0,triggerUpdate:!1,onChange:Wa},linkDirectionalParticleSpeed:{default:.01,triggerUpdate:!1},linkDirectionalParticleOffset:{default:0,triggerUpdate:!1},linkDirectionalParticleWidth:{default:4,triggerUpdate:!1},linkDirectionalParticleColor:{triggerUpdate:!1},linkDirectionalParticleCanvasObject:{triggerUpdate:!1},globalScale:{default:1,triggerUpdate:!1},d3AlphaMin:{default:0,triggerUpdate:!1},d3AlphaDecay:{default:.0228,triggerUpdate:!1,onChange:function(t,n){n.forceLayout.alphaDecay(t)}},d3AlphaTarget:{default:0,triggerUpdate:!1,onChange:function(t,n){n.forceLayout.alphaTarget(t)}},d3VelocityDecay:{default:.4,triggerUpdate:!1,onChange:function(t,n){n.forceLayout.velocityDecay(t)}},warmupTicks:{default:0,triggerUpdate:!1},cooldownTicks:{default:1/0,triggerUpdate:!1},cooldownTime:{default:15e3,triggerUpdate:!1},onUpdate:{default:function(){},triggerUpdate:!1},onFinishUpdate:{default:function(){},triggerUpdate:!1},onEngineTick:{default:function(){},triggerUpdate:!1},onEngineStop:{default:function(){},triggerUpdate:!1},onNeedsRedraw:{triggerUpdate:!1},isShadow:{default:!1,triggerUpdate:!1}},methods:{d3Force:function(t,n,e){return void 0===e?t.forceLayout.force(n):(t.forceLayout.force(n,e),this)},d3ReheatSimulation:function(t){return t.forceLayout.alpha(1),this.resetCountdown(),this},resetCountdown:function(t){return t.cntTicks=0,t.startTickTime=new Date,t.engineRunning=!0,this},isEngineRunning:function(t){return!!t.engineRunning},tickFrame:function(t){var n,r,i,o,a,l;return!t.isShadow&&t.engineRunning&&(++t.cntTicks>t.cooldownTicks||new Date-t.startTickTime>t.cooldownTime||t.d3AlphaMin>0&&t.forceLayout.alpha()<t.d3AlphaMin?(t.engineRunning=!1,t.onEngineStop()):(t.forceLayout.tick(),t.onEngineTick())),function(){var n=Ir(t.linkVisibility),e=Ir(t.linkColor),r=Ir(t.linkWidth),i=Ir(t.linkLineDash),o=Ir(t.linkCurvature),a=Ir(t.linkCanvasObjectMode),l=t.ctx,c=2*t.isShadow,h=t.graphData.links.filter(n);h.forEach(function(t){var n=o(t);if(!n)return void(t.__controlPoints=null);var e=t.source,r=t.target;if(!(e&&r&&e.hasOwnProperty("x")&&r.hasOwnProperty("x")))return;var i=Math.sqrt(Math.pow(r.x-e.x,2)+Math.pow(r.y-e.y,2));if(i>0){var a=Math.atan2(r.y-e.y,r.x-e.x),u=i*n,s={x:(e.x+r.x)/2+u*Math.cos(a-Math.PI/2),y:(e.y+r.y)/2+u*Math.sin(a-Math.PI/2)};t.__controlPoints=[s.x,s.y]}else{var l=70*n;t.__controlPoints=[r.x,r.y-l,r.x+l,r.y]}});var f=[],p=[],d=h;if(t.linkCanvasObject){var g=[],y=[];h.forEach(function(t){return({before:f,after:p,replace:g}[a(t)]||y).push(t)}),d=[].concat(s(f),p,y),f=f.concat(g)}l.save(),f.forEach(function(n){return t.linkCanvasObject(n,l,t.globalScale)}),l.restore();var _=$a(d,[e,r,i]);l.save(),Object.entries(_).forEach(function(n){var e=u(n,2),r=e[0],o=e[1],a=r&&"undefined"!==r?r:"rgba(0,0,0,0.15)";Object.entries(o).forEach(function(n){var e=u(n,2),r=e[0],o=e[1],h=(r||1)/t.globalScale+c;Object.entries(o).forEach(function(t){var n=u(t,2);n[0];var e=n[1],r=i(e[0]);l.beginPath(),e.forEach(function(t){var n=t.source,e=t.target;if(n&&e&&n.hasOwnProperty("x")&&e.hasOwnProperty("x")){l.moveTo(n.x,n.y);var r=t.__controlPoints;r?l[2===r.length?"quadraticCurveTo":"bezierCurveTo"].apply(l,s(r).concat([e.x,e.y])):l.lineTo(e.x,e.y)}}),l.strokeStyle=a,l.lineWidth=h,l.setLineDash(r||[]),l.stroke()})})}),l.restore(),l.save(),p.forEach(function(n){return t.linkCanvasObject(n,l,t.globalScale)}),l.restore()}(),!t.isShadow&&(n=Ir(t.linkDirectionalArrowLength),r=Ir(t.linkDirectionalArrowRelPos),i=Ir(t.linkVisibility),o=Ir(t.linkDirectionalArrowColor||t.linkColor),a=Ir(t.nodeVal),(l=t.ctx).save(),t.graphData.links.filter(i).forEach(function(i){var u=n(i);if(u&&!(u<0)){var c=i.source,h=i.target;if(c&&h&&c.hasOwnProperty("x")&&h.hasOwnProperty("x")){var f=Math.sqrt(Math.max(0,a(c)||1))*t.nodeRelSize,p=Math.sqrt(Math.max(0,a(h)||1))*t.nodeRelSize,d=Math.min(1,Math.max(0,r(i))),g=o(i)||"rgba(0,0,0,0.28)",y=u/1.6/2,_=i.__controlPoints&&e(Da,[c.x,c.y].concat(s(i.__controlPoints),[h.x,h.y])),v=_?function(t){return _.get(t)}:function(t){return{x:c.x+(h.x-c.x)*t||0,y:c.y+(h.y-c.y)*t||0}},m=_?_.length():Math.sqrt(Math.pow(h.x-c.x,2)+Math.pow(h.y-c.y,2)),x=f+u+(m-f-p-u)*d,b=v(x/m),w=v((x-u)/m),k=v((x-.8*u)/m),M=Math.atan2(b.y-w.y,b.x-w.x)-Math.PI/2;l.beginPath(),l.moveTo(b.x,b.y),l.lineTo(w.x+y*Math.cos(M),w.y+y*Math.sin(M)),l.lineTo(k.x,k.y),l.lineTo(w.x-y*Math.cos(M),w.y-y*Math.sin(M)),l.fillStyle=g,l.fill()}}}),l.restore()),!t.isShadow&&function(){var n=Ir(t.linkDirectionalParticles),r=Ir(t.linkDirectionalParticleSpeed),i=Ir(t.linkDirectionalParticleOffset),o=Ir(t.linkDirectionalParticleWidth),a=Ir(t.linkVisibility),u=Ir(t.linkDirectionalParticleColor||t.linkColor),l=t.ctx;l.save(),t.graphData.links.filter(a).forEach(function(a){var c=n(a);if(a.hasOwnProperty("__photons")&&a.__photons.length){var h=a.source,f=a.target;if(h&&f&&h.hasOwnProperty("x")&&f.hasOwnProperty("x")){var p=r(a),d=Math.abs(i(a)),g=a.__photons||[],y=Math.max(0,o(a)/2)/Math.sqrt(t.globalScale),_=u(a)||"rgba(0,0,0,0.28)";l.fillStyle=_;var v=a.__controlPoints?e(Da,[h.x,h.y].concat(s(a.__controlPoints),[f.x,f.y])):null,m=0,x=!1;g.forEach(function(n){var e=!!n.__singleHop;if(n.hasOwnProperty("__progressRatio")||(n.__progressRatio=e?0:(m+d)/c),!e&&m++,n.__progressRatio+=p,n.__progressRatio>=1){if(e)return void(x=!0);n.__progressRatio=n.__progressRatio%1}var r=n.__progressRatio,i=v?v.get(r):{x:h.x+(f.x-h.x)*r||0,y:h.y+(f.y-h.y)*r||0};t.linkDirectionalParticleCanvasObject?t.linkDirectionalParticleCanvasObject(i.x,i.y,a,l,t.globalScale):(l.beginPath(),l.arc(i.x,i.y,y,0,2*Math.PI,!1),l.fill())}),x&&(a.__photons=a.__photons.filter(function(t){return!t.__singleHop||t.__progressRatio<=1}))}}}),l.restore()}(),function(){var n=Ir(t.nodeVisibility),e=Ir(t.nodeVal),r=Ir(t.nodeColor),i=Ir(t.nodeCanvasObjectMode),o=t.ctx,a=t.isShadow/t.globalScale,u=t.graphData.nodes.filter(n);o.save(),u.forEach(function(n){var u=i(n);if(!t.nodeCanvasObject||"before"!==u&&"replace"!==u||(t.nodeCanvasObject(n,o,t.globalScale),"replace"!==u)){var s=Math.sqrt(Math.max(0,e(n)||1))*t.nodeRelSize+a;o.beginPath(),o.arc(n.x,n.y,s,0,2*Math.PI,!1),o.fillStyle=r(n)||"rgba(31, 120, 180, 0.92)",o.fill(),t.nodeCanvasObject&&"after"===u&&t.nodeCanvasObject(n,t.ctx,t.globalScale)}else o.restore()}),o.restore()}(),this},emitParticle:function(t,n){return n&&(!n.__photons&&(n.__photons=[]),n.__photons.push({__singleHop:!0})),this}},stateInit:function(){return{forceLayout:ca().force("link",ra()).force("charge",ha()).force("center",So()).force("dagRadial",null).stop(),engineRunning:!1}},init:function(t,n){n.ctx=t},update:function(t,n){t.engineRunning=!1,t.onUpdate(),null!==t.nodeAutoColorBy&&Ga(t.graphData.nodes,Ir(t.nodeAutoColorBy),t.nodeColor),null!==t.linkAutoColorBy&&Ga(t.graphData.links,Ir(t.linkAutoColorBy),t.linkColor),t.graphData.links.forEach(function(n){n.source=n[t.linkSource],n.target=n[t.linkTarget]}),t.forceLayout.stop().alpha(1).nodes(t.graphData.nodes);var e=t.forceLayout.force("link");e&&e.id(function(n){return n[t.nodeId]}).links(t.graphData.links);var i=t.dagMode&&function(t,n){var e=t.nodes,i=t.links,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=o.nodeFilter,c=void 0===a?function(){return!0}:a,h=o.onLoopError,f=void 0===h?function(t){throw"Invalid DAG structure! Found cycle in node path: ".concat(t.join(" -> "),".")}:h,p={};e.forEach(function(t){return p[n(t)]={data:t,out:[],depth:-1,skip:!c(t)}}),i.forEach(function(t){var e=t.source,r=t.target,i=s(e),o=s(r);if(!p.hasOwnProperty(i))throw"Missing source node with id: ".concat(i);if(!p.hasOwnProperty(o))throw"Missing target node with id: ".concat(o);var a=p[i],u=p[o];function s(t){return"object"===l(t)?n(t):t}a.out.push(u)});var d=[];return function t(e){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=function(){var o=e[a];if(-1!==r.indexOf(o)){var u=[].concat(s(r.slice(r.indexOf(o))),[o]).map(function(t){return n(t.data)});return d.some(function(t){return t.length===u.length&&t.every(function(t,n){return t===u[n]})})||(d.push(u),f(u)),1}i>o.depth&&(o.depth=i,t(o.out,[].concat(s(r),[o]),i+(o.skip?0:1)))},a=0,u=e.length;a<u;a++)o()}(Object.values(p)),Object.assign.apply(Object,[{}].concat(s(Object.entries(p).filter(function(t){return!u(t,2)[1].skip}).map(function(t){var n=u(t,2);return r({},n[0],n[1].depth)}))))}(t.graphData,function(n){return n[t.nodeId]},{nodeFilter:t.dagNodeFilter,onLoopError:t.onDagError||void 0}),o=Math.max.apply(Math,s(Object.values(i||[]))),a=t.dagLevelDistance||t.graphData.nodes.length/(o||1)*2*(-1!==["radialin","radialout"].indexOf(t.dagMode)?.7:1);if(["lr","rl","td","bu"].includes(n.dagMode)){var c=["lr","rl"].includes(n.dagMode)?"fx":"fy";t.graphData.nodes.filter(t.dagNodeFilter).forEach(function(t){return delete t[c]})}if(["lr","rl","td","bu"].includes(t.dagMode)){var h=["rl","bu"].includes(t.dagMode),f=["lr","rl"].includes(t.dagMode)?"fx":"fy";t.graphData.nodes.filter(t.dagNodeFilter).forEach(function(n){return n[f]=function(n){return(i[n[t.nodeId]]-o/2)*a*(h?-1:1)}(n)})}t.forceLayout.force("dagRadial",-1!==["radialin","radialout"].indexOf(t.dagMode)?function(t,n,e,r){var i,o,a,u,s=Jo(.1);function l(t){for(var s=0,l=i.length;s<l;++s){var c=i[s],h=c.x-n||1e-6,f=(c.y||0)-e||1e-6,p=(c.z||0)-r||1e-6,d=Math.sqrt(h*h+f*f+p*p),g=(u[s]-d)*a[s]*t/d;c.vx+=h*g,o>1&&(c.vy+=f*g),o>2&&(c.vz+=p*g)}}function c(){if(i){var n,e=i.length;for(a=new Array(e),u=new Array(e),n=0;n<e;++n)u[n]=+t(i[n],n,i),a[n]=isNaN(u[n])?0:+s(i[n],n,i)}}return"function"!=typeof t&&(t=Jo(+t)),null==n&&(n=0),null==e&&(e=0),null==r&&(r=0),l.initialize=function(t,...n){i=t,o=n.find(t=>[1,2,3].includes(t))||2,c()},l.strength=function(t){return arguments.length?(s="function"==typeof t?t:Jo(+t),c(),l):s},l.radius=function(n){return arguments.length?(t="function"==typeof n?n:Jo(+n),c(),l):t},l.x=function(t){return arguments.length?(n=+t,l):n},l.y=function(t){return arguments.length?(e=+t,l):e},l.z=function(t){return arguments.length?(r=+t,l):r},l}(function(n){var e=i[n[t.nodeId]]||-1;return("radialin"===t.dagMode?o-e:e)*a}).strength(function(n){return t.dagNodeFilter(n)?1:0}):null);for(var p=0;p<t.warmupTicks&&!(t.d3AlphaMin>0&&t.forceLayout.alpha()<t.d3AlphaMin);p++)t.forceLayout.tick();this.resetCountdown(),t.onFinishUpdate()}});function Qa(t,n){var e=t instanceof Array?t:[t],r=new n;return r._destructor&&r._destructor(),{linkProp:function(t){return{default:r[t](),onChange:function(n,r){e.forEach(function(e){return r[e][t](n)})},triggerUpdate:!1}},linkMethod:function(t){return function(n){for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];var a=[];return e.forEach(function(e){var r=n[e],o=r[t].apply(r,i);o!==r&&a.push(o)}),a.length?a[0]:this}}}}var Ka=Qa("forceGraph",Za),Ja=Qa(["forceGraph","shadowGraph"],Za),tu=Object.assign.apply(Object,s(["nodeColor","nodeAutoColorBy","nodeCanvasObject","nodeCanvasObjectMode","linkColor","linkAutoColorBy","linkLineDash","linkWidth","linkCanvasObject","linkCanvasObjectMode","linkDirectionalArrowLength","linkDirectionalArrowColor","linkDirectionalArrowRelPos","linkDirectionalParticles","linkDirectionalParticleSpeed","linkDirectionalParticleOffset","linkDirectionalParticleWidth","linkDirectionalParticleColor","linkDirectionalParticleCanvasObject","dagMode","dagLevelDistance","dagNodeFilter","onDagError","d3AlphaMin","d3AlphaDecay","d3VelocityDecay","warmupTicks","cooldownTicks","cooldownTime","onEngineTick","onEngineStop"].map(function(t){return r({},t,Ka.linkProp(t))})).concat(s(["nodeRelSize","nodeId","nodeVal","nodeVisibility","linkSource","linkTarget","linkVisibility","linkCurvature"].map(function(t){return r({},t,Ja.linkProp(t))})))),nu=Object.assign.apply(Object,s(["d3Force","d3ReheatSimulation","emitParticle"].map(function(t){return r({},t,Ka.linkMethod(t))})));function eu(t){if(t.canvas){var n=t.canvas.width,e=t.canvas.height;300===n&&150===e&&(n=e=0);var r=window.devicePixelRatio;n/=r,e/=r,[t.canvas,t.shadowCanvas].forEach(function(i){i.style.width="".concat(t.width,"px"),i.style.height="".concat(t.height,"px"),i.width=t.width*r,i.height=t.height*r,n||e||i.getContext("2d").scale(r,r)});var i=Fe(t.canvas).k;t.zoom.translateBy(t.zoom.__baseElem,(t.width-n)/2/i,(t.height-e)/2/i),t.needsRedraw=!0}}function ru(t){var n=window.devicePixelRatio;t.setTransform(n,0,0,n,0,0)}function iu(t,n,e){t.save(),ru(t),t.clearRect(0,0,n,e),t.restore()}var ou=Dr({props:a({width:{default:window.innerWidth,onChange:function(t,n){return eu(n)},triggerUpdate:!1},height:{default:window.innerHeight,onChange:function(t,n){return eu(n)},triggerUpdate:!1},graphData:{default:{nodes:[],links:[]},onChange:function(t,n){[t.nodes,t.links].every(function(t){return(t||[]).every(function(t){return!t.hasOwnProperty("__indexColor")})})&&n.colorTracker.reset(),[{type:"Node",objs:t.nodes},{type:"Link",objs:t.links}].forEach(function(t){var e=t.type;t.objs.filter(function(t){if(!t.hasOwnProperty("__indexColor"))return!0;var e=n.colorTracker.lookup(t.__indexColor);return!e||!e.hasOwnProperty("d")||e.d!==t}).forEach(function(t){t.__indexColor=n.colorTracker.register({type:e,d:t})})}),n.forceGraph.graphData(t),n.shadowGraph.graphData(t)},triggerUpdate:!1},backgroundColor:{onChange:function(t,n){n.canvas&&t&&(n.canvas.style.background=t)},triggerUpdate:!1},nodeLabel:{default:"name",triggerUpdate:!1},nodePointerAreaPaint:{onChange:function(t,n){n.shadowGraph.nodeCanvasObject(t?function(n,e,r){return t(n,n.__indexColor,e,r)}:null),n.flushShadowCanvas&&n.flushShadowCanvas()},triggerUpdate:!1},linkPointerAreaPaint:{onChange:function(t,n){n.shadowGraph.linkCanvasObject(t?function(n,e,r){return t(n,n.__indexColor,e,r)}:null),n.flushShadowCanvas&&n.flushShadowCanvas()},triggerUpdate:!1},linkLabel:{default:"name",triggerUpdate:!1},linkHoverPrecision:{default:4,triggerUpdate:!1},minZoom:{default:.01,onChange:function(t,n){n.zoom.scaleExtent([t,n.zoom.scaleExtent()[1]])},triggerUpdate:!1},maxZoom:{default:1e3,onChange:function(t,n){n.zoom.scaleExtent([n.zoom.scaleExtent()[0],t])},triggerUpdate:!1},enableNodeDrag:{default:!0,triggerUpdate:!1},enableZoomInteraction:{default:!0,triggerUpdate:!1},enablePanInteraction:{default:!0,triggerUpdate:!1},enableZoomPanInteraction:{default:!0,triggerUpdate:!1},enablePointerInteraction:{default:!0,onChange:function(t,n){n.hoverObj=null},triggerUpdate:!1},autoPauseRedraw:{default:!0,triggerUpdate:!1},onNodeDrag:{default:function(){},triggerUpdate:!1},onNodeDragEnd:{default:function(){},triggerUpdate:!1},onNodeClick:{triggerUpdate:!1},onNodeRightClick:{triggerUpdate:!1},onNodeHover:{triggerUpdate:!1},onLinkClick:{triggerUpdate:!1},onLinkRightClick:{triggerUpdate:!1},onLinkHover:{triggerUpdate:!1},onBackgroundClick:{triggerUpdate:!1},onBackgroundRightClick:{triggerUpdate:!1},onZoom:{triggerUpdate:!1},onZoomEnd:{triggerUpdate:!1},onRenderFramePre:{triggerUpdate:!1},onRenderFramePost:{triggerUpdate:!1}},tu),aliases:{stopAnimation:"pauseAnimation"},methods:a({graph2ScreenCoords:function(t,n,e){var r=Fe(t.canvas);return{x:n*r.k+r.x,y:e*r.k+r.y}},screen2GraphCoords:function(t,n,e){var r=Fe(t.canvas);return{x:(n-r.x)/r.k,y:(e-r.y)/r.k}},centerAt:function(t,n,e,r){if(!t.canvas)return null;if(void 0!==n||void 0!==e){var i=Object.assign({},void 0!==n?{x:n}:{},void 0!==e?{y:e}:{});return r?t.tweenGroup.add(new Pr(o()).to(i,r).easing(Mr.Quadratic.Out).onUpdate(a).start()):a(i),this}return o();function o(){var n=Fe(t.canvas);return{x:(t.width/2-n.x)/n.k,y:(t.height/2-n.y)/n.k}}function a(n){var e=n.x,r=n.y;t.zoom.translateTo(t.zoom.__baseElem,void 0===e?o().x:e,void 0===r?o().y:r),t.needsRedraw=!0}},zoom:function(t,n,e){return t.canvas?void 0!==n?(e?t.tweenGroup.add(new Pr({k:r()}).to({k:n},e).easing(Mr.Quadratic.Out).onUpdate(function(t){return i(t.k)}).start()):i(n),this):r():null;function r(){return Fe(t.canvas).k}function i(n){t.zoom.scaleTo(t.zoom.__baseElem,n),t.needsRedraw=!0}},zoomToFit:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=arguments.length,i=new Array(r>3?r-3:0),o=3;o<r;o++)i[o-3]=arguments[o];var a=this.getGraphBbox.apply(this,i);if(a){var u={x:(a.x[0]+a.x[1])/2,y:(a.y[0]+a.y[1])/2},s=Math.max(1e-12,Math.min(1e12,(t.width-2*e)/(a.x[1]-a.x[0]),(t.height-2*e)/(a.y[1]-a.y[0])));this.centerAt(u.x,u.y,n),this.zoom(s,n)}return this},getGraphBbox:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){return!0},e=Ir(t.nodeVal),r=function(n){return Math.sqrt(Math.max(0,e(n)||1))*t.nodeRelSize},i=t.graphData.nodes.filter(n).map(function(t){return{x:t.x,y:t.y,r:r(t)}});return i.length?{x:[Je(i,function(t){return t.x-t.r}),Ke(i,function(t){return t.x+t.r})],y:[Je(i,function(t){return t.y-t.r}),Ke(i,function(t){return t.y+t.r})]}:null},pauseAnimation:function(t){return t.animationFrameRequestId&&(cancelAnimationFrame(t.animationFrameRequestId),t.animationFrameRequestId=null),this},resumeAnimation:function(t){return t.animationFrameRequestId||this._animationCycle(),this},_destructor:function(){this.pauseAnimation(),this.graphData({nodes:[],links:[]})}},nu),stateInit:function(){return{lastSetZoom:1,zoom:Ye(),forceGraph:new Za,shadowGraph:(new Za).cooldownTicks(0).nodeColor("__indexColor").linkColor("__indexColor").isShadow(!0),colorTracker:new Bi,tweenGroup:new zr}},init:function(t,n){var e=this;t.innerHTML="";var r=document.createElement("div");r.classList.add("force-graph-container"),r.style.position="relative",t.appendChild(r),n.canvas=document.createElement("canvas"),n.backgroundColor&&(n.canvas.style.background=n.backgroundColor),r.appendChild(n.canvas),n.shadowCanvas=document.createElement("canvas");var i=n.canvas.getContext("2d"),o=n.shadowCanvas.getContext("2d",{willReadFrequently:!0}),u={x:-1e12,y:-1e12},s=function(){var t=null,e=window.devicePixelRatio,r=u.x>0&&u.y>0?o.getImageData(u.x*e,u.y*e,1,1):null;return r&&(t=n.colorTracker.lookup(r.data)),t};kt(n.canvas).call(function(){var t,n,e,r,i=Ut,o=Ft,a=Lt,u=qt,s={},l=zt("start","drag","end"),c=0,h=0;function f(t){t.on("mousedown.drag",p).filter(u).on("touchstart.drag",y).on("touchmove.drag",_,Pt).on("touchend.drag touchcancel.drag",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(a,u){if(!r&&i.call(this,a,u)){var s=m(this,o.call(this,a,u),a,u,"mouse");s&&(kt(a.view).on("mousemove.drag",d,Ot).on("mouseup.drag",g,Ot),Tt(a.view),Nt(a),e=!1,t=a.clientX,n=a.clientY,s("start",a))}}function d(r){if(jt(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>h}s.mouse("drag",r)}function g(t){kt(t.view).on("mousemove.drag mouseup.drag",null),Rt(t.view,e),jt(t),s.mouse("end",t)}function y(t,n){if(i.call(this,t,n)){var e,r,a=t.changedTouches,u=o.call(this,t,n),s=a.length;for(e=0;e<s;++e)(r=m(this,u,t,n,a[e].identifier,a[e]))&&(Nt(t),r("start",t,a[e]))}}function _(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=s[r[n].identifier])&&(jt(t),e("drag",t,r[n]))}function v(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=s[i[n].identifier])&&(Nt(t),e("end",t,i[n]))}function m(t,n,e,r,i,o){var u,h,p,d=l.copy(),g=Mt(o||e,n);if(null!=(p=a.call(t,new It("beforestart",{sourceEvent:e,target:f,identifier:i,active:c,x:g[0],y:g[1],dx:0,dy:0,dispatch:d}),r)))return u=p.x-g[0]||0,h=p.y-g[1]||0,function e(o,a,l){var y,_=g;switch(o){case"start":s[i]=e,y=c++;break;case"end":delete s[i],--c;case"drag":g=Mt(l||a,n),y=c}d.call(o,t,new It(o,{sourceEvent:a,subject:p,target:f,identifier:i,active:y,x:g[0]+u,y:g[1]+h,dx:g[0]-_[0],dy:g[1]-_[1],dispatch:d}),r)}}return f.filter=function(t){return arguments.length?(i="function"==typeof t?t:Dt(!!t),f):i},f.container=function(t){return arguments.length?(o="function"==typeof t?t:Dt(t),f):o},f.subject=function(t){return arguments.length?(a="function"==typeof t?t:Dt(t),f):a},f.touchable=function(t){return arguments.length?(u="function"==typeof t?t:Dt(!!t),f):u},f.on=function(){var t=l.on.apply(l,arguments);return t===l?f:t},f.clickDistance=function(t){return arguments.length?(h=(t=+t)*t,f):Math.sqrt(h)},f}().subject(function(){if(!n.enableNodeDrag)return null;var t=s();return t&&"Node"===t.type?t.d:null}).on("start",function(t){var e=t.subject;e.__initialDragPos={x:e.x,y:e.y,fx:e.fx,fy:e.fy},t.active||(e.fx=e.x,e.fy=e.y),n.canvas.classList.add("grabbable")}).on("drag",function(t){var e=t.subject,r=e.__initialDragPos,i=t,o=Fe(n.canvas).k,a={x:r.x+(i.x-r.x)/o-e.x,y:r.y+(i.y-r.y)/o-e.y};["x","y"].forEach(function(t){return e["f".concat(t)]=e[t]=r[t]+(i[t]-r[t])/o}),!e.__dragged&&5>=Math.sqrt(function(t){let n=0;for(let e of t)(e=+e)&&(n+=e);return n}(["x","y"].map(function(n){return Math.pow(t[n]-r[n],2)})))||(n.forceGraph.d3AlphaTarget(.3).resetCountdown(),n.isPointerDragging=!0,e.__dragged=!0,n.onNodeDrag(e,a))}).on("end",function(t){var e=t.subject,r=e.__initialDragPos,i={x:e.x-r.x,y:e.y-r.y};void 0===r.fx&&(e.fx=void 0),void 0===r.fy&&(e.fy=void 0),delete e.__initialDragPos,n.forceGraph.d3AlphaTarget()&&n.forceGraph.d3AlphaTarget(0).resetCountdown(),n.canvas.classList.remove("grabbable"),n.isPointerDragging=!1,e.__dragged&&(delete e.__dragged,n.onNodeDragEnd(e,i))})),n.zoom(n.zoom.__baseElem=kt(n.canvas)),n.zoom.__baseElem.on("dblclick.zoom",null),n.zoom.filter(function(t){return!t.button&&n.enableZoomPanInteraction&&("wheel"!==t.type||Ir(n.enableZoomInteraction)(t))&&("wheel"===t.type||Ir(n.enablePanInteraction)(t))}).on("zoom",function(t){var r=t.transform;[i,o].forEach(function(t){ru(t),t.translate(r.x,r.y),t.scale(r.k,r.k)}),n.isPointerDragging=!0,n.onZoom&&n.onZoom(a(a({},r),e.centerAt())),n.needsRedraw=!0}).on("end",function(t){n.isPointerDragging=!1,n.onZoomEnd&&n.onZoomEnd(a(a({},t.transform),e.centerAt()))}),eu(n),n.forceGraph.onNeedsRedraw(function(){return n.needsRedraw=!0}).onFinishUpdate(function(){Fe(n.canvas).k===n.lastSetZoom&&n.graphData.nodes.length&&(n.zoom.scaleTo(n.zoom.__baseElem,n.lastSetZoom=4/Math.cbrt(n.graphData.nodes.length)),n.needsRedraw=!0)}),n.tooltip=new zo(r),["pointermove","pointerdown"].forEach(function(t){return r.addEventListener(t,function(e){"pointerdown"===t&&(n.isPointerPressed=!0,n.pointerDownEvent=e),!n.isPointerDragging&&"pointermove"===e.type&&n.onBackgroundClick&&(e.pressure>0||n.isPointerPressed)&&("mouse"===e.pointerType||void 0===e.movementX||[e.movementX,e.movementY].some(function(t){return Math.abs(t)>1}))&&(n.isPointerDragging=!0);var i,o,a,s=(i=r.getBoundingClientRect(),o=window.pageXOffset||document.documentElement.scrollLeft,a=window.pageYOffset||document.documentElement.scrollTop,{top:i.top+a,left:i.left+o});u.x=e.pageX-s.left,u.y=e.pageY-s.top},{passive:!0})}),r.addEventListener("pointerup",function(t){if(n.isPointerPressed)if(n.isPointerPressed=!1,n.isPointerDragging)n.isPointerDragging=!1;else{var e=[t,n.pointerDownEvent];requestAnimationFrame(function(){if(0===t.button)if(n.hoverObj){var r=n["on".concat(n.hoverObj.type,"Click")];r&&r.apply(void 0,[n.hoverObj.d].concat(e))}else n.onBackgroundClick&&n.onBackgroundClick.apply(n,e);if(2===t.button)if(n.hoverObj){var i=n["on".concat(n.hoverObj.type,"RightClick")];i&&i.apply(void 0,[n.hoverObj.d].concat(e))}else n.onBackgroundRightClick&&n.onBackgroundRightClick.apply(n,e)})}},{passive:!0}),r.addEventListener("contextmenu",function(t){return!(n.onBackgroundRightClick||n.onNodeRightClick||n.onLinkRightClick)||(t.preventDefault(),!1)}),n.forceGraph(i),n.shadowGraph(o);var l=function(t,n,e){var r=!0,i=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return dr(e)&&(r="leading"in e?!!e.leading:r,i="trailing"in e?!!e.trailing:i),kr(t,n,{leading:r,maxWait:n,trailing:i})}(function(){iu(o,n.width,n.height),n.shadowGraph.linkWidth(function(t){return Ir(n.linkWidth)(t)+n.linkHoverPrecision});var t=Fe(n.canvas);n.shadowGraph.globalScale(t.k).tickFrame()},800);n.flushShadowCanvas=l.flush,(this._animationCycle=function t(){var e=!n.autoPauseRedraw||!!n.needsRedraw||n.forceGraph.isEngineRunning()||n.graphData.links.some(function(t){return t.__photons&&t.__photons.length});if(n.needsRedraw=!1,n.enablePointerInteraction){var r=n.isPointerDragging?null:s();if(r!==n.hoverObj){var o=n.hoverObj,a=o?o.type:null,u=r?r.type:null;if(a&&a!==u){var c=n["on".concat(a,"Hover")];c&&c(null,o.d)}if(u){var h=n["on".concat(u,"Hover")];h&&h(r.d,a===u?o.d:null)}n.tooltip.content(r&&Ir(n["".concat(r.type.toLowerCase(),"Label")])(r.d)||null),n.canvas.classList[r&&n["on".concat(u,"Click")]||!r&&n.onBackgroundClick?"add":"remove"]("clickable"),n.hoverObj=r}e&&l()}if(e){iu(i,n.width,n.height);var f=Fe(n.canvas).k;n.onRenderFramePre&&n.onRenderFramePre(i,f),n.forceGraph.globalScale(f).tickFrame(),n.onRenderFramePost&&n.onRenderFramePost(i,f)}n.tweenGroup.update(),n.animationFrameRequestId=requestAnimationFrame(t)})()},update:function(t){}});return ou});
