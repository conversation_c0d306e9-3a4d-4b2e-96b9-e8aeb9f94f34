{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.7", "@types/d3": "^7.4.3", "axios": "^1.11.0", "clsx": "^2.1.1", "d3": "^7.9.0", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-force-graph-2d": "^1.28.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/node": "^24.2.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}